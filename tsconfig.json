{"compilerOptions": {"target": "ES2017", "module": "CommonJS", "allowJs": true, "strict": false, "experimentalDecorators": true, "isolatedModules": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./temp/compiled", "noEmit": true, "paths": {"cc": ["./temp/declarations/cc"], "cc/*": ["./temp/declarations/cc/*"]}, "typeRoots": ["./temp/declarations"], "lib": ["DOM", "ES2017"]}, "exclude": ["node_modules", "library", "temp", "build", "settings", "tools", "game-version-tool"]}