#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { VersionCommand } from './commands/VersionCommand';
import { BuildCommand } from './commands/BuildCommand';
import { DeployCommand } from './commands/DeployCommand';
import { ConfigCommand } from './commands/ConfigCommand';
import { RollbackCommand } from './commands/RollbackCommand';

const program = new Command();

// 设置程序信息
program
  .name('game-version-tool')
  .description('LuckyCoin 游戏版本管理工具')
  .version('1.0.0');

// 注册命令
const versionCommand = new VersionCommand();
const buildCommand = new BuildCommand();
const deployCommand = new DeployCommand();
const configCommand = new ConfigCommand();
const rollbackCommand = new RollbackCommand();

// 版本管理命令
program
  .command('version')
  .alias('v')
  .description('版本管理')
  .addCommand(versionCommand.getCommand());

// 构建管理命令
program
  .command('build')
  .alias('b')
  .description('构建管理')
  .addCommand(buildCommand.getCommand());

// 部署管理命令
program
  .command('deploy')
  .alias('d')
  .description('部署管理')
  .addCommand(deployCommand.getCommand());

// 配置管理命令 - 直接注册子命令
configCommand.registerCommands(program);

// 回滚管理命令
program
  .command('rollback')
  .alias('r')
  .description('回滚管理')
  .addCommand(rollbackCommand.getCommand());

// 错误处理
program.exitOverride();

process.on('uncaughtException', (error) => {
  console.error(chalk.red('未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('未处理的 Promise 拒绝:'), reason);
  process.exit(1);
});

// 解析命令行参数
try {
  program.parse(process.argv);
} catch (error) {
  console.error(chalk.red('命令执行失败:'), error);
  process.exit(1);
}

// 如果没有提供任何命令，显示帮助信息
if (!process.argv.slice(2).length) {
  console.log(chalk.green('🎮 LuckyCoin 游戏版本管理工具'));
  console.log('');
  console.log('使用说明:');
  console.log('  game-version init              # 初始化配置文件');
  console.log('  game-version version bump patch # 升级补丁版本');
  console.log('  game-version build web          # 构建 Web 版本');
  console.log('  game-version deploy staging     # 部署到测试环境');
  console.log('');
  console.log('更多命令请使用 --help 查看');
  program.help();
}