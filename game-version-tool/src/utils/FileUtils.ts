import * as fs from 'fs-extra';
import * as path from 'path';
import { glob } from 'glob';

export class FileUtils {
  /**
   * 递归复制目录，支持排除文件
   */
  static async copyDir(
    src: string, 
    dest: string, 
    options: {
      excludePatterns?: string[];
      overwrite?: boolean;
    } = {}
  ): Promise<void> {
    const { excludePatterns = [], overwrite = true } = options;
    
    await fs.ensureDir(dest);
    
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      // 检查是否应该排除
      const shouldExclude = excludePatterns.some(pattern => {
        return srcPath.includes(pattern) || entry.name.match(new RegExp(pattern));
      });
      
      if (shouldExclude) {
        continue;
      }
      
      if (entry.isDirectory()) {
        await this.copyDir(srcPath, destPath, options);
      } else {
        if (overwrite || !await fs.pathExists(destPath)) {
          await fs.copy(srcPath, destPath);
        }
      }
    }
  }

  /**
   * 获取目录大小（字节）
   */
  static async getDirectorySize(dirPath: string): Promise<number> {
    if (!await fs.pathExists(dirPath)) {
      return 0;
    }

    let totalSize = 0;
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        totalSize += await this.getDirectorySize(entryPath);
      } else {
        const stats = await fs.stat(entryPath);
        totalSize += stats.size;
      }
    }

    return totalSize;
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = bytes / Math.pow(1024, i);
    
    return `${size.toFixed(2)} ${sizes[i]}`;
  }

  /**
   * 查找文件
   */
  static async findFiles(
    dir: string, 
    pattern: string, 
    options: {
      recursive?: boolean;
      caseSensitive?: boolean;
    } = {}
  ): Promise<string[]> {
    const { recursive = true, caseSensitive = false } = options;
    
    const globPattern = recursive 
      ? path.join(dir, '**', pattern)
      : path.join(dir, pattern);
    
    return glob(globPattern, {
      nocase: !caseSensitive,
      absolute: true
    });
  }

  /**
   * 清理空目录
   */
  static async cleanEmptyDirs(dirPath: string): Promise<void> {
    if (!await fs.pathExists(dirPath)) {
      return;
    }

    const entries = await fs.readdir(dirPath);
    
    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry);
      const stats = await fs.stat(entryPath);
      
      if (stats.isDirectory()) {
        await this.cleanEmptyDirs(entryPath);
        
        // 检查目录是否为空
        const subEntries = await fs.readdir(entryPath);
        if (subEntries.length === 0) {
          await fs.remove(entryPath);
        }
      }
    }
  }

  /**
   * 创建备份文件
   */
  static async backup(filePath: string, backupDir?: string): Promise<string> {
    if (!await fs.pathExists(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = path.basename(filePath);
    const backupFileName = `${fileName}.backup.${timestamp}`;
    
    const backupPath = backupDir 
      ? path.join(backupDir, backupFileName)
      : path.join(path.dirname(filePath), backupFileName);

    await fs.ensureDir(path.dirname(backupPath));
    await fs.copy(filePath, backupPath);
    
    return backupPath;
  }

  /**
   * 安全删除文件（移动到回收站目录）
   */
  static async safeDelete(filePath: string, trashDir?: string): Promise<void> {
    if (!await fs.pathExists(filePath)) {
      return;
    }

    const defaultTrashDir = path.join(process.cwd(), '.trash');
    const targetTrashDir = trashDir || defaultTrashDir;
    
    await fs.ensureDir(targetTrashDir);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = path.basename(filePath);
    const trashedPath = path.join(targetTrashDir, `${fileName}.${timestamp}`);
    
    await fs.move(filePath, trashedPath);
  }

  /**
   * 检查路径是否在指定目录内
   */
  static isSubPath(childPath: string, parentPath: string): boolean {
    const relative = path.relative(parentPath, childPath);
    return !relative.startsWith('..') && !path.isAbsolute(relative);
  }

  /**
   * 确保文件路径安全（防止路径遍历攻击）
   */
  static sanitizePath(inputPath: string, basePath: string): string {
    const resolved = path.resolve(basePath, inputPath);
    
    if (!this.isSubPath(resolved, basePath)) {
      throw new Error(`不安全的文件路径: ${inputPath}`);
    }
    
    return resolved;
  }

  /**
   * 读取 JSON 文件，支持注释
   */
  static async readJsonWithComments(filePath: string): Promise<any> {
    const content = await fs.readFile(filePath, 'utf-8');
    
    // 移除注释（简单实现，可能需要更复杂的解析器）
    const withoutComments = content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 块注释
      .replace(/\/\/.*$/gm, ''); // 行注释
    
    return JSON.parse(withoutComments);
  }
}