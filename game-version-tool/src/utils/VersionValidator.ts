import * as semver from 'semver';
import * as fs from 'fs-extra';
import * as path from 'path';
import { simpleGit, SimpleGit } from 'simple-git';
import { Logger } from './Logger';

/**
 * 版本验证工具类
 * 严格按照VERSION_MAINTENANCE_RULES.md规范实现
 */
export class VersionValidator {
  private static logger = Logger.getInstance();
  private static git: SimpleGit;

  /**
   * 初始化Git实例
   */
  static initialize(projectRoot: string = process.cwd()) {
    this.git = simpleGit(projectRoot);
  }

  /**
   * 规则1: 验证版本号严格递增
   * 必须确保新版本大于当前版本
   */
  static validateVersionIncrement(current: string, next: string): {
    valid: boolean;
    error?: string;
  } {
    try {
      // 验证版本格式
      if (!semver.valid(current)) {
        return { valid: false, error: `当前版本格式无效: ${current}` };
      }
      
      if (!semver.valid(next)) {
        return { valid: false, error: `目标版本格式无效: ${next}` };
      }

      // 检查递增性
      if (!semver.gt(next, current)) {
        return { 
          valid: false, 
          error: `版本号必须递增: ${current} → ${next} (违反递增规则)` 
        };
      }

      // 检查递增的合理性
      const currentParsed = semver.parse(current)!;
      const nextParsed = semver.parse(next)!;

      // 防止跨越式升级（如 1.0.0 → 3.0.0）
      if (nextParsed.major > currentParsed.major + 1) {
        return {
          valid: false,
          error: `主版本号跨越过大: ${current} → ${next} (建议逐步升级)`
        };
      }

      this.logger.info('版本递增验证通过', { current, next });
      return { valid: true };

    } catch (error) {
      return { 
        valid: false, 
        error: `版本验证异常: ${error instanceof Error ? error.message : String(error)}` 
      };
    }
  }

  /**
   * 规则2: 验证回滚版本安全性
   * 回滚不能破坏版本递增规则
   */
  static async validateRollback(current: string, target: string): Promise<{
    valid: boolean;
    error?: string;
    strategy?: 'safe' | 'emergency';
  }> {
    try {
      // 验证目标版本格式
      if (!semver.valid(target)) {
        return { valid: false, error: `回滚目标版本格式无效: ${target}` };
      }

      // 检查目标版本是否存在于Git历史中
      const tagExists = await this.checkGitTagExists(target);
      if (!tagExists) {
        return { 
          valid: false, 
          error: `回滚目标版本不存在于Git历史: ${target}` 
        };
      }

      // 获取版本历史中的最高版本
      const highestVersion = await this.getHighestHistoricalVersion();
      
      // 如果回滚目标低于历史最高版本，需要特殊处理
      if (semver.lt(target, highestVersion)) {
        this.logger.warn('回滚将导致版本倒退', { 
          current, 
          target, 
          highest: highestVersion 
        });
        
        return {
          valid: true,
          strategy: 'emergency',
          error: `警告: 回滚到 ${target} 将低于历史最高版本 ${highestVersion}`
        };
      }

      return { valid: true, strategy: 'safe' };

    } catch (error) {
      return { 
        valid: false, 
        error: `回滚验证异常: ${error instanceof Error ? error.message : String(error)}` 
      };
    }
  }

  /**
   * 规则3: 检查版本冲突
   * 确保版本号唯一性
   */
  static async checkVersionConflict(version: string): Promise<{
    hasConflict: boolean;
    conflicts: string[];
  }> {
    const conflicts: string[] = [];

    try {
      // 检查Git标签冲突
      const tagExists = await this.checkGitTagExists(version);
      if (tagExists) {
        conflicts.push(`Git标签已存在: v${version}`);
      }

      // 检查预发布版本冲突
      const prereleaseConflict = await this.checkPrereleaseConflict(version);
      if (prereleaseConflict) {
        conflicts.push(`预发布版本冲突: ${prereleaseConflict}`);
      }

      return {
        hasConflict: conflicts.length > 0,
        conflicts
      };

    } catch (error) {
      this.logger.error('版本冲突检查失败', error);
      return {
        hasConflict: true,
        conflicts: [`冲突检查异常: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * 规则4: 验证预发布版本规范
   * 确保预发布版本遵循正确的升级路径
   */
  static validatePrereleaseVersion(current: string, next: string, prereleaseType: string): {
    valid: boolean;
    error?: string;
    correctedVersion?: string;
  } {
    try {
      const currentParsed = semver.parse(current);
      const nextParsed = semver.parse(next);

      if (!currentParsed || !nextParsed) {
        return { valid: false, error: '版本格式解析失败' };
      }

      // 当前是正式版本，创建预发布版本
      if (!currentParsed.prerelease.length) {
        const expectedNext = semver.inc(current, 'patch') + `-${prereleaseType}.0`;
        if (next !== expectedNext) {
          return {
            valid: false,
            error: `预发布版本格式错误: 期望 ${expectedNext}, 实际 ${next}`,
            correctedVersion: expectedNext
          };
        }
      }
      // 当前是预发布版本
      else {
        const currentPrereleaseType = currentParsed.prerelease[0] as string;
        
        // 同类型预发布版本递增
        if (prereleaseType === currentPrereleaseType) {
          const expectedNext = semver.inc(current, 'prerelease');
          if (next !== expectedNext) {
            return {
              valid: false,
              error: `预发布版本递增错误: 期望 ${expectedNext}, 实际 ${next}`,
              correctedVersion: expectedNext || undefined
            };
          }
        }
        // 不同类型预发布版本升级
        else {
          const baseVersion = `${currentParsed.major}.${currentParsed.minor}.${currentParsed.patch}`;
          const expectedNext = `${baseVersion}-${prereleaseType}.0`;
          if (next !== expectedNext) {
            return {
              valid: false,
              error: `预发布类型升级错误: 期望 ${expectedNext}, 实际 ${next}`,
              correctedVersion: expectedNext
            };
          }
        }
      }

      return { valid: true };

    } catch (error) {
      return { 
        valid: false, 
        error: `预发布版本验证异常: ${error instanceof Error ? error.message : String(error)}` 
      };
    }
  }

  /**
   * 规则5: 验证多文件版本同步
   * 确保所有配置文件中的版本号一致
   */
  static async validateVersionSync(projectRoot: string, expectedVersion: string): Promise<{
    synced: boolean;
    inconsistencies: Array<{
      file: string;
      currentVersion: string;
      expectedVersion: string;
    }>;
  }> {
    const inconsistencies: Array<{
      file: string;
      currentVersion: string;
      expectedVersion: string;
    }> = [];

    try {
      // 检查的文件列表
      const filesToCheck = [
        { path: 'game-version.config.json', key: 'project.version' },
        { path: 'package.json', key: 'version' },
        { path: 'web/package.json', key: 'version' },
        { path: 'assets/scripts/config/VersionConfig.ts', key: 'CLIENT_VERSION' }
      ];

      for (const fileInfo of filesToCheck) {
        const filePath = path.join(projectRoot, fileInfo.path);
        
        if (await fs.pathExists(filePath)) {
          let currentVersion: string | undefined;

          if (fileInfo.path.endsWith('.json')) {
            const content = await fs.readJSON(filePath);
            currentVersion = this.getNestedValue(content, fileInfo.key);
          } else if (fileInfo.path.endsWith('.ts')) {
            const content = await fs.readFile(filePath, 'utf8');
            const match = content.match(/CLIENT_VERSION\s*=\s*['"`]([^'"`]+)['"`]/);
            currentVersion = match ? match[1] : undefined;
          }

          if (currentVersion && currentVersion !== expectedVersion) {
            inconsistencies.push({
              file: fileInfo.path,
              currentVersion,
              expectedVersion
            });
          }
        }
      }

      return {
        synced: inconsistencies.length === 0,
        inconsistencies
      };

    } catch (error) {
      this.logger.error('版本同步检查失败', error);
      return {
        synced: false,
        inconsistencies: [{
          file: 'unknown',
          currentVersion: 'error',
          expectedVersion
        }]
      };
    }
  }

  /**
   * 检查Git标签是否存在
   */
  private static async checkGitTagExists(version: string): Promise<boolean> {
    try {
      const tags = await this.git.tags();
      const tagName = version.startsWith('v') ? version : `v${version}`;
      return tags.all.includes(tagName);
    } catch (error) {
      this.logger.warn('检查Git标签失败', error);
      return false;
    }
  }

  /**
   * 获取历史最高版本
   */
  private static async getHighestHistoricalVersion(): Promise<string> {
    try {
      const tags = await this.git.tags(['--sort=-version:refname']);
      
      for (const tag of tags.all) {
        if (tag.startsWith('v')) {
          const version = tag.substring(1);
          if (semver.valid(version)) {
            return version;
          }
        }
      }
      
      return '0.0.0';
    } catch (error) {
      this.logger.warn('获取历史版本失败', error);
      return '0.0.0';
    }
  }

  /**
   * 检查预发布版本冲突
   */
  private static async checkPrereleaseConflict(version: string): Promise<string | null> {
    try {
      const parsed = semver.parse(version);
      if (!parsed || !parsed.prerelease.length) {
        return null;
      }

      const baseVersion = `${parsed.major}.${parsed.minor}.${parsed.patch}`;
      const tags = await this.git.tags();
      
      // 检查是否存在相同基础版本的其他预发布版本
      for (const tag of tags.all) {
        if (tag.startsWith('v')) {
          const tagVersion = tag.substring(1);
          const tagParsed = semver.parse(tagVersion);
          
          if (tagParsed && tagParsed.prerelease.length) {
            const tagBaseVersion = `${tagParsed.major}.${tagParsed.minor}.${tagParsed.patch}`;
            if (tagBaseVersion === baseVersion && tagVersion !== version) {
              return `存在相同基础版本的预发布版本: ${tagVersion}`;
            }
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.warn('检查预发布版本冲突失败', error);
      return null;
    }
  }

  /**
   * 获取嵌套对象的值
   */
  private static getNestedValue(obj: any, key: string): string | undefined {
    const keys = key.split('.');
    let current = obj;
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return undefined;
      }
    }
    
    return typeof current === 'string' ? current : undefined;
  }
}
