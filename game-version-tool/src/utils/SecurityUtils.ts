import * as crypto from 'crypto';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from './Logger';

/**
 * 安全工具类
 * 提供命令注入防护、路径验证、敏感信息处理等安全功能
 */
export class SecurityUtils {
  private static logger = Logger.getInstance();

  /**
   * 验证并清理命令参数，防止命令注入
   */
  static sanitizeCommandArgs(args: string[]): string[] {
    const sanitized: string[] = [];
    
    for (const arg of args) {
      // 检查危险字符
      if (this.containsDangerousChars(arg)) {
        this.logger.warn('检测到潜在危险字符，已过滤', { arg });
        continue;
      }

      // 转义特殊字符
      const escaped = this.escapeShellArg(arg);
      sanitized.push(escaped);
    }

    return sanitized;
  }

  /**
   * 安全执行Git命令
   */
  static sanitizeGitCommand(command: string, args: string[]): { command: string; args: string[] } {
    // 验证Git命令白名单
    const allowedCommands = [
      'status', 'add', 'commit', 'push', 'pull', 'fetch', 'checkout', 
      'branch', 'tag', 'log', 'diff', 'reset', 'merge', 'clone'
    ];

    if (!allowedCommands.includes(command)) {
      throw new Error(`不允许的Git命令: ${command}`);
    }

    // 清理参数
    const sanitizedArgs = this.sanitizeCommandArgs(args);

    // 验证参数格式
    for (const arg of sanitizedArgs) {
      if (arg.includes('..') && !arg.startsWith('--')) {
        throw new Error(`潜在的路径遍历攻击: ${arg}`);
      }
    }

    return { command, args: sanitizedArgs };
  }

  /**
   * 验证文件路径安全性
   */
  static validateFilePath(filePath: string, allowedBasePaths: string[] = []): boolean {
    try {
      // 解析绝对路径
      const absolutePath = path.resolve(filePath);
      
      // 检查路径遍历
      if (filePath.includes('..') || filePath.includes('~')) {
        this.logger.warn('检测到路径遍历尝试', { filePath });
        return false;
      }

      // 检查是否在允许的基础路径内
      if (allowedBasePaths.length > 0) {
        const isAllowed = allowedBasePaths.some(basePath => {
          const absoluteBasePath = path.resolve(basePath);
          return absolutePath.startsWith(absoluteBasePath);
        });

        if (!isAllowed) {
          this.logger.warn('文件路径不在允许范围内', { filePath, allowedBasePaths });
          return false;
        }
      }

      // 检查危险路径
      const dangerousPaths = [
        '/etc', '/usr/bin', '/bin', '/sbin', '/boot',
        'C:\\Windows', 'C:\\Program Files', 'C:\\System32'
      ];

      for (const dangerousPath of dangerousPaths) {
        if (absolutePath.startsWith(path.resolve(dangerousPath))) {
          this.logger.warn('尝试访问危险路径', { filePath, dangerousPath });
          return false;
        }
      }

      return true;

    } catch (error) {
      this.logger.error('路径验证失败', { filePath, error: error instanceof Error ? error.message : String(error) });
      return false;
    }
  }

  /**
   * 安全的文件操作
   */
  static async safeFileOperation<T>(
    operation: () => Promise<T>,
    filePath: string,
    allowedBasePaths: string[] = []
  ): Promise<T> {
    // 验证路径安全性
    if (!this.validateFilePath(filePath, allowedBasePaths)) {
      throw new Error(`不安全的文件路径: ${filePath}`);
    }

    // 检查文件权限
    try {
      await fs.access(path.dirname(filePath), fs.constants.W_OK);
    } catch (error) {
      throw new Error(`没有文件操作权限: ${filePath}`);
    }

    // 执行操作
    return await operation();
  }

  /**
   * 加密敏感配置
   */
  static encryptSensitiveData(data: string, key?: string): string {
    try {
      const encryptionKey = key || this.getEncryptionKey();
      const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return encrypted;
    } catch (error) {
      this.logger.error('数据加密失败', error instanceof Error ? error : new Error(String(error)));
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密敏感配置
   */
  static decryptSensitiveData(encryptedData: string, key?: string): string {
    try {
      const encryptionKey = key || this.getEncryptionKey();
      const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
      
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('数据解密失败', error instanceof Error ? error : new Error(String(error)));
      throw new Error('数据解密失败');
    }
  }

  /**
   * 生成安全的随机密钥
   */
  static generateSecureKey(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 计算文件哈希值
   */
  static async calculateFileHash(filePath: string, algorithm: string = 'sha256'): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash(algorithm);
      const stream = fs.createReadStream(filePath);

      stream.on('data', (data) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  /**
   * 验证文件完整性
   */
  static async verifyFileIntegrity(filePath: string, expectedHash: string, algorithm: string = 'sha256'): Promise<boolean> {
    try {
      const actualHash = await this.calculateFileHash(filePath, algorithm);
      return actualHash === expectedHash;
    } catch (error) {
      this.logger.error('文件完整性验证失败', { filePath, error });
      return false;
    }
  }

  /**
   * 清理敏感信息日志
   */
  static sanitizeLogData(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sensitiveKeys = [
      'password', 'token', 'key', 'secret', 'auth', 'credential',
      'apiKey', 'accessToken', 'refreshToken', 'privateKey'
    ];

    const sanitized = { ...data };

    for (const key in sanitized) {
      if (sensitiveKeys.some(sensitiveKey => 
        key.toLowerCase().includes(sensitiveKey.toLowerCase())
      )) {
        sanitized[key] = '***REDACTED***';
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeLogData(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * 验证URL安全性
   */
  static validateUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // 只允许HTTP和HTTPS协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        this.logger.warn('不允许的URL协议', { url, protocol: parsedUrl.protocol });
        return false;
      }

      // 检查是否为内网地址
      const hostname = parsedUrl.hostname;
      if (this.isPrivateIP(hostname)) {
        this.logger.warn('不允许访问内网地址', { url, hostname });
        return false;
      }

      return true;

    } catch (error) {
      this.logger.warn('URL格式无效', { url, error });
      return false;
    }
  }

  /**
   * 创建安全的临时目录
   */
  static async createSecureTempDir(prefix: string = 'game-version-'): Promise<string> {
    const tempDir = path.join(
      require('os').tmpdir(),
      `${prefix}${Date.now()}-${crypto.randomBytes(6).toString('hex')}`
    );

    await fs.ensureDir(tempDir);
    
    // 设置严格的权限（仅所有者可读写）
    try {
      await fs.chmod(tempDir, 0o700);
    } catch (error) {
      this.logger.warn('设置临时目录权限失败', { tempDir, error });
    }

    return tempDir;
  }

  /**
   * 安全删除敏感文件
   */
  static async secureDelete(filePath: string): Promise<void> {
    try {
      if (!await fs.pathExists(filePath)) {
        return;
      }

      const stats = await fs.stat(filePath);
      
      if (stats.isFile()) {
        // 多次覆写文件内容
        const fileSize = stats.size;
        const randomData = crypto.randomBytes(fileSize);
        
        await fs.writeFile(filePath, randomData);
        await fs.writeFile(filePath, Buffer.alloc(fileSize, 0));
        await fs.writeFile(filePath, crypto.randomBytes(fileSize));
      }

      // 删除文件
      await fs.remove(filePath);
      
      this.logger.info('敏感文件已安全删除', { filePath });

    } catch (error) {
      this.logger.error('安全删除文件失败', { filePath, error });
      throw error;
    }
  }

  /**
   * 检查是否包含危险字符
   */
  private static containsDangerousChars(input: string): boolean {
    const dangerousPatterns = [
      /[;&|`$(){}[\]]/,  // Shell特殊字符
      /\.\./,            // 路径遍历
      /^-/,              // 以-开头的参数可能被误解释
      /\x00/,            // 空字节
      /[\r\n]/           // 换行符
    ];

    return dangerousPatterns.some(pattern => pattern.test(input));
  }

  /**
   * 转义Shell参数
   */
  private static escapeShellArg(arg: string): string {
    // 如果参数包含空格或特殊字符，用引号包围
    if (/[\s"'\\$`!*?[\]{}();&|<>]/.test(arg)) {
      return `"${arg.replace(/["\\$`]/g, '\\$&')}"`;
    }
    return arg;
  }

  /**
   * 获取加密密钥
   */
  private static getEncryptionKey(): string {
    // 从环境变量获取密钥，如果不存在则生成一个
    let key = process.env.GAME_VERSION_ENCRYPTION_KEY;
    
    if (!key) {
      key = this.generateSecureKey();
      this.logger.warn('未设置加密密钥，使用临时密钥。建议设置环境变量 GAME_VERSION_ENCRYPTION_KEY');
    }

    return key;
  }

  /**
   * 检查是否为私有IP地址
   */
  private static isPrivateIP(hostname: string): boolean {
    const privateRanges = [
      /^127\./,                    // *********/8
      /^10\./,                     // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
      /^192\.168\./,               // ***********/16
      /^localhost$/i,              // localhost
      /^::1$/,                     // IPv6 localhost
      /^fe80:/i                    // IPv6 link-local
    ];

    return privateRanges.some(range => range.test(hostname));
  }

  /**
   * 验证配置文件权限
   */
  static async validateConfigPermissions(configPath: string): Promise<void> {
    try {
      const stats = await fs.stat(configPath);
      const mode = stats.mode & parseInt('777', 8);

      // 检查是否其他用户可读
      if (mode & parseInt('044', 8)) {
        this.logger.warn('配置文件权限过于宽松，建议设置为600', { 
          configPath, 
          currentMode: mode.toString(8) 
        });
      }

      // 尝试修正权限
      try {
        await fs.chmod(configPath, 0o600);
        this.logger.info('配置文件权限已修正', { configPath });
      } catch (error) {
        this.logger.warn('无法修正配置文件权限', { configPath, error });
      }

    } catch (error) {
      this.logger.error('验证配置文件权限失败', { configPath, error });
    }
  }

  /**
   * 生成安全的会话令牌
   */
  static generateSessionToken(): string {
    const timestamp = Date.now().toString();
    const randomBytes = crypto.randomBytes(16).toString('hex');
    const hash = crypto.createHash('sha256').update(timestamp + randomBytes).digest('hex');
    
    return `${timestamp}.${hash}`;
  }

  /**
   * 验证会话令牌
   */
  static validateSessionToken(token: string, maxAge: number = 3600000): boolean {
    try {
      const [timestampStr, hash] = token.split('.');
      const timestamp = parseInt(timestampStr);
      
      // 检查时间戳有效性
      if (Date.now() - timestamp > maxAge) {
        return false;
      }

      // 这里可以添加更复杂的验证逻辑
      return hash && hash.length === 64; // SHA256 hash length

    } catch (error) {
      return false;
    }
  }
}
