/**
 * 模块加载器工具
 * 处理 ESM/CommonJS 兼容性问题
 */

/**
 * 动态导入 ora
 * 支持不同版本的 ora 包
 */
export async function createSpinner(text: string) {
  try {
    // 尝试 ESM 版本的 ora
    const { default: ora } = await import('ora');
    return ora(text);
  } catch {
    // 回退到 CommonJS 版本
    const ora = require('ora');
    return ora(text);
  }
}

/**
 * 创建简单的 spinner 替代品
 * 当 ora 不可用时使用
 */
export function createSimpleSpinner(text: string) {
  let isRunning = false;
  
  return {
    start() {
      isRunning = true;
      console.log(`⏳ ${text}`);
      return this;
    },
    
    succeed(message?: string) {
      isRunning = false;
      console.log(`✅ ${message || text}`);
      return this;
    },
    
    fail(message?: string) {
      isRunning = false;
      console.log(`❌ ${message || text}`);
      return this;
    },
    
    stop() {
      isRunning = false;
      return this;
    },
    
    get isSpinning() {
      return isRunning;
    }
  };
}