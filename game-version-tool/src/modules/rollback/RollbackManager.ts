import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { simpleGit, SimpleGit } from 'simple-git';
import { ConfigManager } from '../config/ConfigManager';
import { VersionManager } from '../version/VersionManager';
import { BuildManager } from '../build/BuildManager';
import { DeployManager } from '../deploy/DeployManager';
import { Logger } from '../../utils/Logger';
import { FileUtils } from '../../utils/FileUtils';

export interface RollbackPoint {
  id: string;
  version: string;
  timestamp: string;
  description: string;
  commitHash: string;
  configSnapshot: any;
  buildArtifacts: string[];
  deploymentInfo: {
    environment: string;
    platforms: string[];
    deployedAt: string;
    status: 'success' | 'failed' | 'partial';
  };
  verified: boolean;
  fileChecksums: Map<string, string>;
}

export interface RollbackResult {
  success: boolean;
  rollbackPointId: string;
  version: string;
  restoredFiles: string[];
  errors: string[];
  rollbackTime: number;
}

export class RollbackManager {
  private configManager: ConfigManager;
  private versionManager: VersionManager;
  private buildManager: BuildManager;
  private deployManager: DeployManager;
  private git: SimpleGit;
  private logger: Logger;
  private rollbackDir: string;
  private backupDir: string;

  constructor(projectRoot: string = process.cwd()) {
    this.configManager = new ConfigManager(projectRoot);
    this.versionManager = new VersionManager(projectRoot);
    this.buildManager = new BuildManager(projectRoot);
    this.deployManager = new DeployManager(projectRoot);
    this.git = simpleGit(projectRoot);
    this.logger = Logger.getInstance();
    this.rollbackDir = path.join(projectRoot, '.rollback');
    this.backupDir = path.join(projectRoot, '.backup');
  }

  /**
   * 初始化回滚管理器
   */
  async initialize(): Promise<void> {
    await fs.ensureDir(this.rollbackDir);
    await fs.ensureDir(this.backupDir);
    this.logger.info('回滚管理器初始化完成');
  }

  /**
   * 创建回滚点
   */
  async createRollbackPoint(description: string, environment?: string): Promise<RollbackPoint> {
    try {
      const startTime = Date.now();
      this.logger.info('开始创建回滚点', { description, environment });

      // 1. 生成回滚点ID
      const timestamp = new Date().toISOString();
      const id = this.generateRollbackId(timestamp);

      // 2. 获取当前状态
      const version = await this.versionManager.getCurrentVersion();
      const commitHash = await this.getCurrentCommitHash();
      const configSnapshot = await this.createConfigSnapshot();

      // 3. 备份构建产物
      const buildArtifacts = await this.backupBuildArtifacts(id);

      // 4. 计算文件校验和
      const fileChecksums = await this.calculateFileChecksums();

      // 5. 创建回滚点
      const rollbackPoint: RollbackPoint = {
        id,
        version,
        timestamp,
        description,
        commitHash,
        configSnapshot,
        buildArtifacts,
        deploymentInfo: {
          environment: environment || 'unknown',
          platforms: configSnapshot.build?.platforms || [],
          deployedAt: timestamp,
          status: 'success'
        },
        verified: false,
        fileChecksums
      };

      // 6. 保存回滚点信息
      await this.saveRollbackPoint(rollbackPoint);

      // 7. 验证回滚点
      rollbackPoint.verified = await this.verifyRollbackPoint(rollbackPoint);

      // 8. 更新回滚点状态
      await this.saveRollbackPoint(rollbackPoint);

      const createTime = Date.now() - startTime;
      this.logger.info('回滚点创建完成', { 
        id, 
        version, 
        verified: rollbackPoint.verified,
        createTime 
      });

      return rollbackPoint;

    } catch (error) {
      this.logger.error('创建回滚点失败', error);
      throw new Error(`创建回滚点失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 执行回滚
   */
  async rollbackToPoint(rollbackPointId: string, options: {
    force?: boolean;
    skipVerification?: boolean;
    restoreConfig?: boolean;
    restoreBuild?: boolean;
    redeploy?: boolean;
  } = {}): Promise<RollbackResult> {
    const startTime = Date.now();
    const result: RollbackResult = {
      success: false,
      rollbackPointId,
      version: '',
      restoredFiles: [],
      errors: [],
      rollbackTime: 0
    };

    try {
      this.logger.info('开始回滚操作', { rollbackPointId, options });

      // 1. 加载回滚点
      const rollbackPoint = await this.loadRollbackPoint(rollbackPointId);
      if (!rollbackPoint) {
        throw new Error(`回滚点不存在: ${rollbackPointId}`);
      }

      result.version = rollbackPoint.version;

      // 2. 验证回滚点（除非跳过验证）
      if (!options.skipVerification && !rollbackPoint.verified) {
        const isValid = await this.verifyRollbackPoint(rollbackPoint);
        if (!isValid && !options.force) {
          throw new Error('回滚点验证失败，使用 --force 强制回滚');
        }
      }

      // 3. 创建当前状态备份
      const backupId = await this.createEmergencyBackup();
      this.logger.info('当前状态已备份', { backupId });

      // 4. 回滚Git状态
      await this.rollbackGitState(rollbackPoint.commitHash);
      this.logger.info('Git状态已回滚', { commitHash: rollbackPoint.commitHash });

      // 5. 恢复配置文件
      if (options.restoreConfig !== false) {
        const configFiles = await this.restoreConfiguration(rollbackPoint);
        result.restoredFiles.push(...configFiles);
        this.logger.info('配置文件已恢复', { count: configFiles.length });
      }

      // 6. 恢复构建产物
      if (options.restoreBuild !== false) {
        const buildFiles = await this.restoreBuildArtifacts(rollbackPoint);
        result.restoredFiles.push(...buildFiles);
        this.logger.info('构建产物已恢复', { count: buildFiles.length });
      }

      // 7. 重新部署（如果需要）
      if (options.redeploy) {
        await this.redeployAfterRollback(rollbackPoint);
        this.logger.info('重新部署完成');
      }

      // 8. 验证回滚结果
      const verificationResult = await this.verifyRollbackResult(rollbackPoint);
      if (!verificationResult.success) {
        result.errors.push(...verificationResult.errors);
        this.logger.warn('回滚验证发现问题', { errors: verificationResult.errors });
      }

      result.success = true;
      result.rollbackTime = Date.now() - startTime;

      this.logger.info('回滚操作完成', {
        rollbackPointId,
        version: result.version,
        restoredFiles: result.restoredFiles.length,
        rollbackTime: result.rollbackTime
      });

      return result;

    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error));
      result.rollbackTime = Date.now() - startTime;
      
      this.logger.error('回滚操作失败', {
        rollbackPointId,
        error: error instanceof Error ? error.message : String(error),
        rollbackTime: result.rollbackTime
      });

      // 尝试恢复到回滚前状态
      try {
        await this.emergencyRestore();
        this.logger.info('已恢复到回滚前状态');
      } catch (restoreError) {
        this.logger.error('紧急恢复失败', restoreError);
        result.errors.push(`紧急恢复失败: ${restoreError instanceof Error ? restoreError.message : String(restoreError)}`);
      }

      return result;
    }
  }

  /**
   * 列出所有回滚点
   */
  async listRollbackPoints(): Promise<RollbackPoint[]> {
    try {
      const rollbackFiles = await fs.readdir(this.rollbackDir);
      const rollbackPoints: RollbackPoint[] = [];

      for (const file of rollbackFiles) {
        if (file.endsWith('.json')) {
          try {
            const rollbackPoint = await this.loadRollbackPoint(path.basename(file, '.json'));
            if (rollbackPoint) {
              rollbackPoints.push(rollbackPoint);
            }
          } catch (error) {
            this.logger.warn('加载回滚点失败', { file, error: error instanceof Error ? error.message : String(error) });
          }
        }
      }

      // 按时间戳排序（最新的在前）
      return rollbackPoints.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

    } catch (error) {
      this.logger.error('列出回滚点失败', error);
      return [];
    }
  }

  /**
   * 删除回滚点
   */
  async deleteRollbackPoint(rollbackPointId: string): Promise<void> {
    try {
      const rollbackPoint = await this.loadRollbackPoint(rollbackPointId);
      if (!rollbackPoint) {
        throw new Error(`回滚点不存在: ${rollbackPointId}`);
      }

      // 删除回滚点文件
      const rollbackFile = path.join(this.rollbackDir, `${rollbackPointId}.json`);
      await fs.remove(rollbackFile);

      // 删除备份的构建产物
      for (const artifact of rollbackPoint.buildArtifacts) {
        const artifactPath = path.join(this.backupDir, artifact);
        if (await fs.pathExists(artifactPath)) {
          await fs.remove(artifactPath);
        }
      }

      this.logger.info('回滚点已删除', { rollbackPointId });

    } catch (error) {
      this.logger.error('删除回滚点失败', error);
      throw error;
    }
  }

  /**
   * 清理旧的回滚点
   */
  async cleanupOldRollbackPoints(keepCount: number = 10): Promise<void> {
    try {
      const rollbackPoints = await this.listRollbackPoints();
      
      if (rollbackPoints.length <= keepCount) {
        this.logger.info('无需清理回滚点', { current: rollbackPoints.length, keep: keepCount });
        return;
      }

      const toDelete = rollbackPoints.slice(keepCount);
      
      for (const rollbackPoint of toDelete) {
        await this.deleteRollbackPoint(rollbackPoint.id);
      }

      this.logger.info('回滚点清理完成', { 
        deleted: toDelete.length, 
        remaining: keepCount 
      });

    } catch (error) {
      this.logger.error('清理回滚点失败', error);
      throw error;
    }
  }

  /**
   * 生成回滚点ID
   */
  private generateRollbackId(timestamp: string): string {
    const hash = crypto.createHash('md5').update(timestamp + Math.random()).digest('hex');
    return `rollback_${timestamp.replace(/[:.]/g, '-')}_${hash.substring(0, 8)}`;
  }

  /**
   * 获取当前提交哈希
   */
  private async getCurrentCommitHash(): Promise<string> {
    try {
      const log = await this.git.log(['-1', '--format=%H']);
      return log.latest?.hash || '';
    } catch (error) {
      this.logger.warn('获取提交哈希失败', error);
      return '';
    }
  }

  /**
   * 创建配置快照
   */
  private async createConfigSnapshot(): Promise<any> {
    try {
      const config = await this.configManager.loadConfig();
      return JSON.parse(JSON.stringify(config)); // 深拷贝
    } catch (error) {
      this.logger.warn('创建配置快照失败', error);
      return {};
    }
  }

  /**
   * 备份构建产物
   */
  private async backupBuildArtifacts(rollbackId: string): Promise<string[]> {
    try {
      const config = await this.configManager.loadConfig();
      const buildDir = config.build.outputDir;
      const backupPath = path.join(this.backupDir, rollbackId);

      if (await fs.pathExists(buildDir)) {
        await fs.copy(buildDir, backupPath);
        this.logger.info('构建产物已备份', { from: buildDir, to: backupPath });
        return [rollbackId];
      }

      return [];
    } catch (error) {
      this.logger.warn('备份构建产物失败', error);
      return [];
    }
  }

  /**
   * 计算文件校验和
   */
  private async calculateFileChecksums(): Promise<Map<string, string>> {
    const checksums = new Map<string, string>();
    
    try {
      const config = await this.configManager.loadConfig();
      const importantFiles = [
        'game-version.config.json',
        'package.json',
        'tsconfig.json'
      ];

      for (const file of importantFiles) {
        if (await fs.pathExists(file)) {
          const content = await fs.readFile(file);
          const checksum = crypto.createHash('md5').update(content).digest('hex');
          checksums.set(file, checksum);
        }
      }

    } catch (error) {
      this.logger.warn('计算文件校验和失败', error);
    }

    return checksums;
  }

  /**
   * 保存回滚点
   */
  private async saveRollbackPoint(rollbackPoint: RollbackPoint): Promise<void> {
    const filePath = path.join(this.rollbackDir, `${rollbackPoint.id}.json`);
    
    // 转换Map为普通对象以便JSON序列化
    const serializable = {
      ...rollbackPoint,
      fileChecksums: Object.fromEntries(rollbackPoint.fileChecksums)
    };
    
    await fs.writeJSON(filePath, serializable, { spaces: 2 });
  }

  /**
   * 加载回滚点
   */
  private async loadRollbackPoint(rollbackPointId: string): Promise<RollbackPoint | null> {
    try {
      const filePath = path.join(this.rollbackDir, `${rollbackPointId}.json`);
      
      if (!await fs.pathExists(filePath)) {
        return null;
      }

      const data = await fs.readJSON(filePath);
      
      // 转换普通对象为Map
      data.fileChecksums = new Map(Object.entries(data.fileChecksums || {}));
      
      return data as RollbackPoint;
    } catch (error) {
      this.logger.error('加载回滚点失败', { rollbackPointId, error });
      return null;
    }
  }

  /**
   * 验证回滚点
   */
  private async verifyRollbackPoint(rollbackPoint: RollbackPoint): Promise<boolean> {
    try {
      // 验证构建产物是否存在
      for (const artifact of rollbackPoint.buildArtifacts) {
        const artifactPath = path.join(this.backupDir, artifact);
        if (!await fs.pathExists(artifactPath)) {
          this.logger.warn('构建产物不存在', { artifact });
          return false;
        }
      }

      // 验证文件校验和
      for (const [file, expectedChecksum] of rollbackPoint.fileChecksums) {
        if (await fs.pathExists(file)) {
          const content = await fs.readFile(file);
          const actualChecksum = crypto.createHash('md5').update(content).digest('hex');
          if (actualChecksum !== expectedChecksum) {
            this.logger.warn('文件校验和不匹配', { file, expected: expectedChecksum, actual: actualChecksum });
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      this.logger.error('验证回滚点失败', error);
      return false;
    }
  }

  /**
   * 创建紧急备份
   */
  private async createEmergencyBackup(): Promise<string> {
    const backupId = `emergency_${Date.now()}`;
    const backupPath = path.join(this.backupDir, backupId);
    
    // 备份重要文件
    const importantFiles = [
      'game-version.config.json',
      'package.json'
    ];

    await fs.ensureDir(backupPath);

    for (const file of importantFiles) {
      if (await fs.pathExists(file)) {
        await fs.copy(file, path.join(backupPath, file));
      }
    }

    return backupId;
  }

  /**
   * 回滚Git状态
   */
  private async rollbackGitState(commitHash: string): Promise<void> {
    if (!commitHash) {
      this.logger.warn('没有提交哈希，跳过Git回滚');
      return;
    }

    try {
      await this.git.reset(['--hard', commitHash]);
      this.logger.info('Git状态已回滚', { commitHash });
    } catch (error) {
      this.logger.error('Git回滚失败', error);
      throw new Error(`Git回滚失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 恢复配置文件
   */
  private async restoreConfiguration(rollbackPoint: RollbackPoint): Promise<string[]> {
    const restoredFiles: string[] = [];

    try {
      // 恢复主配置文件
      if (rollbackPoint.configSnapshot) {
        await fs.writeJSON('game-version.config.json', rollbackPoint.configSnapshot, { spaces: 2 });
        restoredFiles.push('game-version.config.json');
      }

      return restoredFiles;
    } catch (error) {
      this.logger.error('恢复配置文件失败', error);
      throw error;
    }
  }

  /**
   * 恢复构建产物
   */
  private async restoreBuildArtifacts(rollbackPoint: RollbackPoint): Promise<string[]> {
    const restoredFiles: string[] = [];

    try {
      const config = await this.configManager.loadConfig();
      const buildDir = config.build.outputDir;

      for (const artifact of rollbackPoint.buildArtifacts) {
        const sourcePath = path.join(this.backupDir, artifact);
        if (await fs.pathExists(sourcePath)) {
          await fs.copy(sourcePath, buildDir);
          restoredFiles.push(buildDir);
        }
      }

      return restoredFiles;
    } catch (error) {
      this.logger.error('恢复构建产物失败', error);
      throw error;
    }
  }

  /**
   * 回滚后重新部署
   */
  private async redeployAfterRollback(rollbackPoint: RollbackPoint): Promise<void> {
    try {
      const { environment, platforms } = rollbackPoint.deploymentInfo;
      
      for (const platform of platforms) {
        await this.deployManager.deployToEnvironment(environment as any, platform);
      }
    } catch (error) {
      this.logger.error('回滚后重新部署失败', error);
      throw error;
    }
  }

  /**
   * 验证回滚结果
   */
  private async verifyRollbackResult(rollbackPoint: RollbackPoint): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // 验证版本是否正确
      const currentVersion = await this.versionManager.getCurrentVersion();
      if (currentVersion !== rollbackPoint.version) {
        errors.push(`版本不匹配: 期望 ${rollbackPoint.version}, 实际 ${currentVersion}`);
      }

      // 验证配置文件
      try {
        await this.configManager.loadConfig();
      } catch (error) {
        errors.push(`配置文件无效: ${error instanceof Error ? error.message : String(error)}`);
      }

      return { success: errors.length === 0, errors };
    } catch (error) {
      errors.push(`验证失败: ${error instanceof Error ? error.message : String(error)}`);
      return { success: false, errors };
    }
  }

  /**
   * 紧急恢复
   */
  private async emergencyRestore(): Promise<void> {
    // 实现紧急恢复逻辑
    this.logger.info('执行紧急恢复...');
    // 这里可以实现从最近的紧急备份恢复
  }
}
