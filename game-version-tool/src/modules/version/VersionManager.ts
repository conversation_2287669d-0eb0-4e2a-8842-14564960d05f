import * as fs from 'fs-extra';
import * as path from 'path';
import * as semver from 'semver';
import { simpleGit, SimpleGit } from 'simple-git';
import { VersionInfo, VersionType, PrereleaseType, ChangelogEntry } from '../../types/version';
import { ConfigManager } from '../config/ConfigManager';
import { SecurityUtils } from '../../utils/SecurityUtils';
import { Logger } from '../../utils/Logger';

export class VersionManager {
  private git: SimpleGit;
  private configManager: ConfigManager;
  private projectRoot: string;
  private logger: Logger;

  constructor(projectRoot: string = process.cwd()) {
    this.projectRoot = projectRoot;
    this.git = simpleGit(projectRoot);
    this.configManager = new ConfigManager(projectRoot);
    this.logger = Logger.getInstance();
  }

  /**
   * 获取当前版本信息
   */
  async getCurrentVersion(): Promise<string> {
    // 首先尝试从配置文件获取
    try {
      const config = await this.configManager.loadConfig();
      return config.project.version;
    } catch {
      // 如果配置文件不存在，尝试从 package.json 获取
      const packagePath = path.join(this.projectRoot, 'package.json');
      if (await fs.pathExists(packagePath)) {
        const packageJson = await fs.readJSON(packagePath);
        return packageJson.version || '0.1.0';
      }
      return '0.1.0';
    }
  }

  /**
   * 计算下一个版本号
   */
  async getNextVersion(type: VersionType, prerelease?: PrereleaseType): Promise<VersionInfo> {
    const current = await this.getCurrentVersion();
    let next: string;

    if (type === 'prerelease' && prerelease) {
      // 处理预发布版本
      if (semver.prerelease(current)) {
        // 如果当前已是预发布版本，增加预发布号
        next = semver.inc(current, 'prerelease', prerelease) || current;
      } else {
        // 如果当前是正式版本，创建新的预发布版本
        next = semver.inc(current, 'patch') + `-${prerelease}.0`;
      }
    } else {
      next = semver.inc(current, type) || current;
    }

    return {
      current,
      next,
      type,
      prerelease
    };
  }

  /**
   * 升级版本号
   */
  async bumpVersion(type: VersionType, prerelease?: PrereleaseType): Promise<VersionInfo> {
    const versionInfo = await this.getNextVersion(type, prerelease);
    
    // 更新配置文件
    try {
      await this.configManager.updateConfig({
        project: {
          ...(await this.configManager.loadConfig()).project,
          version: versionInfo.next
        }
      });
    } catch {
      // 如果配置文件不存在，尝试更新 package.json
      const packagePath = path.join(this.projectRoot, 'package.json');
      if (await fs.pathExists(packagePath)) {
        const packageJson = await fs.readJSON(packagePath);
        packageJson.version = versionInfo.next;
        await fs.writeJSON(packagePath, packageJson, { spaces: 2 });
      }
    }

    // 更新 web/package.json (如果存在)
    const webPackagePath = path.join(this.projectRoot, 'web', 'package.json');
    if (await fs.pathExists(webPackagePath)) {
      const webPackageJson = await fs.readJSON(webPackagePath);
      webPackageJson.version = versionInfo.next;
      await fs.writeJSON(webPackagePath, webPackageJson, { spaces: 2 });
    }

    return versionInfo;
  }

  /**
   * 创建 Git 标签
   */
  async createTag(version: string, message?: string): Promise<void> {
    try {
      // 验证版本格式
      if (!semver.valid(version)) {
        throw new Error(`无效的版本格式: ${version}`);
      }

      const config = await this.configManager.loadConfig();
      const tagName = `${config.git.tagPrefix}${version}`;
      const tagMessage = message || `Release version ${version}`;

      // 使用安全的Git命令参数
      const sanitizedArgs = SecurityUtils.sanitizeCommandArgs(['-a', tagName, '-m', tagMessage]);

      await this.git.addAnnotatedTag(tagName, tagMessage);
      this.logger.info('Git标签创建成功', { tagName, message: tagMessage });
    } catch (error) {
      this.logger.error('创建标签失败', { version, error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 生成变更日志
   */
  async generateChangelog(fromVersion?: string): Promise<ChangelogEntry> {
    const currentVersion = await this.getCurrentVersion();
    const config = await this.configManager.loadConfig();
    
    // 获取Git提交记录
    const from = fromVersion || await this.getLastTagVersion();
    const commits = await this.getCommitsSince(from);
    
    // 解析提交信息，分类变更
    const changes = this.parseCommitMessages(commits);
    
    const changelogEntry: ChangelogEntry = {
      version: currentVersion,
      date: new Date().toISOString().split('T')[0],
      changes
    };

    // 更新 CHANGELOG.md
    if (config.git.generateChangelog) {
      await this.updateChangelogFile(changelogEntry);
    }

    return changelogEntry;
  }

  /**
   * 获取最后一个标签版本
   */
  private async getLastTagVersion(): Promise<string> {
    try {
      const tags = await this.git.tags(['--sort=-version:refname']);
      const config = await this.configManager.loadConfig();
      const prefix = config.git.tagPrefix;
      
      for (const tag of tags.all) {
        if (tag.startsWith(prefix)) {
          return tag.substring(prefix.length);
        }
      }
      return '0.0.0';
    } catch {
      return '0.0.0';
    }
  }

  /**
   * 获取指定版本之后的提交记录
   */
  private async getCommitsSince(version: string): Promise<string[]> {
    try {
      const config = await this.configManager.loadConfig();
      const tagName = `${config.git.tagPrefix}${version}`;
      const log = await this.git.log({ from: tagName, to: 'HEAD' });
      return log.all.map(commit => commit.message);
    } catch {
      // 如果没有找到标签，获取所有提交
      const log = await this.git.log();
      return log.all.map(commit => commit.message);
    }
  }

  /**
   * 解析提交信息，分类变更
   */
  private parseCommitMessages(commits: string[]): ChangelogEntry['changes'] {
    const changes = {
      added: [] as string[],
      changed: [] as string[],
      deprecated: [] as string[],
      removed: [] as string[],
      fixed: [] as string[],
      security: [] as string[]
    };

    for (const commit of commits) {
      const message = commit.toLowerCase();
      
      if (message.includes('add') || message.includes('新增') || message.includes('feat')) {
        changes.added.push(commit);
      } else if (message.includes('fix') || message.includes('修复') || message.includes('bug')) {
        changes.fixed.push(commit);
      } else if (message.includes('remove') || message.includes('delete') || message.includes('删除')) {
        changes.removed.push(commit);
      } else if (message.includes('security') || message.includes('安全')) {
        changes.security.push(commit);
      } else if (message.includes('deprecate') || message.includes('废弃')) {
        changes.deprecated.push(commit);
      } else {
        changes.changed.push(commit);
      }
    }

    return changes;
  }

  /**
   * 更新 CHANGELOG.md 文件
   */
  private async updateChangelogFile(entry: ChangelogEntry): Promise<void> {
    const config = await this.configManager.loadConfig();
    const changelogPath = path.join(this.projectRoot, config.git.changelogPath);
    
    let content = `## [${entry.version}] - ${entry.date}\n\n`;
    
    if (entry.changes.added.length > 0) {
      content += '### 新增\n';
      entry.changes.added.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }
    
    if (entry.changes.changed.length > 0) {
      content += '### 变更\n';
      entry.changes.changed.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }
    
    if (entry.changes.fixed.length > 0) {
      content += '### 修复\n';
      entry.changes.fixed.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }
    
    if (entry.changes.removed.length > 0) {
      content += '### 移除\n';
      entry.changes.removed.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }

    // 如果文件存在，将新内容插入到现有内容之前
    if (await fs.pathExists(changelogPath)) {
      const existingContent = await fs.readFile(changelogPath, 'utf-8');
      const headerIndex = existingContent.indexOf('\n## ');
      if (headerIndex > -1) {
        const beforeHeader = existingContent.substring(0, headerIndex + 1);
        const afterHeader = existingContent.substring(headerIndex + 1);
        content = beforeHeader + content + afterHeader;
      } else {
        content = content + '\n' + existingContent;
      }
    } else {
      // 创建新的 CHANGELOG.md
      content = `# 变更日志\n\n所有重要的变更都将记录在此文件中。\n\n` + content;
    }

    await fs.writeFile(changelogPath, content, 'utf-8');
  }

  /**
   * 获取所有可用版本
   */
  async getAvailableVersions(): Promise<string[]> {
    try {
      const tags = await this.git.tags(['--sort=-version:refname']);
      const config = await this.configManager.loadConfig();
      const prefix = config.git.tagPrefix;
      
      return tags.all
        .filter(tag => tag.startsWith(prefix))
        .map(tag => tag.substring(prefix.length));
    } catch {
      return [];
    }
  }
}