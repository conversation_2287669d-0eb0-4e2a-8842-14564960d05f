import * as fs from 'fs-extra';
import * as path from 'path';
import * as semver from 'semver';
import { simpleGit, SimpleGit } from 'simple-git';
import { VersionInfo, VersionType, PrereleaseType, ChangelogEntry } from '../../types/version';
import { ConfigManager } from '../config/ConfigManager';
import { SecurityUtils } from '../../utils/SecurityUtils';
import { Logger } from '../../utils/Logger';
import { VersionValidator } from '../../utils/VersionValidator';
import { VersionHistory } from '../../utils/VersionHistory';

export class VersionManager {
  private git: SimpleGit;
  private configManager: ConfigManager;
  private projectRoot: string;
  private logger: Logger;

  constructor(projectRoot: string = process.cwd()) {
    this.projectRoot = projectRoot;
    this.git = simpleGit(projectRoot);
    this.configManager = new ConfigManager(projectRoot);
    this.logger = Logger.getInstance();
  }

  /**
   * 获取当前版本信息
   */
  async getCurrentVersion(): Promise<string> {
    // 首先尝试从配置文件获取
    try {
      const config = await this.configManager.loadConfig();
      return config.project.version;
    } catch {
      // 如果配置文件不存在，尝试从 package.json 获取
      const packagePath = path.join(this.projectRoot, 'package.json');
      if (await fs.pathExists(packagePath)) {
        const packageJson = await fs.readJSON(packagePath);
        return packageJson.version || '0.1.0';
      }
      return '0.1.0';
    }
  }

  /**
   * 计算下一个版本号
   * 严格按照VERSION_MAINTENANCE_RULES.md规范实现
   */
  async getNextVersion(type: VersionType, prerelease?: PrereleaseType): Promise<VersionInfo> {
    const current = await this.getCurrentVersion();
    let next: string;

    // 初始化版本验证器和历史管理器
    VersionValidator.initialize(this.projectRoot);
    VersionHistory.initialize(this.projectRoot);

    // 获取正确的升级基准版本（处理回滚后的情况）
    const baselineVersion = await VersionHistory.getUpgradeBaseline(current);
    const versionToUpgrade = baselineVersion !== current ? baselineVersion : current;

    if (type === 'prerelease' && prerelease) {
      // 处理预发布版本 - 修复原有逻辑错误
      const versionParsed = semver.parse(versionToUpgrade);
      if (!versionParsed) {
        throw new Error(`版本格式无效: ${versionToUpgrade}`);
      }

      if (versionParsed.prerelease.length > 0) {
        // 基准版本是预发布版本
        const basePrereleaseType = versionParsed.prerelease[0] as string;

        if (prerelease === basePrereleaseType) {
          // 同类型预发布版本递增
          next = semver.inc(versionToUpgrade, 'prerelease') || versionToUpgrade;
        } else {
          // 不同类型预发布版本升级
          const baseVersion = `${versionParsed.major}.${versionParsed.minor}.${versionParsed.patch}`;
          next = semver.inc(`${baseVersion}-${basePrereleaseType}.0`, 'prerelease', prerelease) || versionToUpgrade;
        }
      } else {
        // 基准版本是正式版本，创建新的预发布版本
        // 使用安全的semver.inc方法，不使用字符串拼接
        next = semver.inc(versionToUpgrade, 'prepatch', prerelease) || versionToUpgrade;
      }

      // 验证预发布版本规范
      const prereleaseValidation = VersionValidator.validatePrereleaseVersion(current, next, prerelease);
      if (!prereleaseValidation.valid) {
        if (prereleaseValidation.correctedVersion) {
          this.logger.warn('预发布版本已自动修正', {
            original: next,
            corrected: prereleaseValidation.correctedVersion,
            reason: prereleaseValidation.error
          });
          next = prereleaseValidation.correctedVersion;
        } else {
          throw new Error(prereleaseValidation.error);
        }
      }
    } else {
      // 处理正式版本升级 - 使用正确的基准版本
      next = semver.inc(versionToUpgrade, type) || versionToUpgrade;
    }

    // 验证版本递增规则
    const incrementValidation = VersionValidator.validateVersionIncrement(current, next);
    if (!incrementValidation.valid) {
      throw new Error(incrementValidation.error);
    }

    // 检查版本冲突
    const conflictCheck = await VersionValidator.checkVersionConflict(next);
    if (conflictCheck.hasConflict) {
      throw new Error(`版本冲突: ${conflictCheck.conflicts.join(', ')}`);
    }

    this.logger.info('版本号计算完成', { current, next, type, prerelease });

    return {
      current,
      next,
      type,
      prerelease
    };
  }

  /**
   * 升级版本号
   * 严格按照VERSION_MAINTENANCE_RULES.md规范实现多文件同步
   */
  async bumpVersion(type: VersionType, prerelease?: PrereleaseType): Promise<VersionInfo> {
    const versionInfo = await this.getNextVersion(type, prerelease);

    try {
      // 更新所有相关文件中的版本号
      await this.updateAllVersionFiles(versionInfo.next);

      // 验证多文件版本同步
      const syncValidation = await VersionValidator.validateVersionSync(this.projectRoot, versionInfo.next);
      if (!syncValidation.synced) {
        this.logger.warn('版本同步检查发现不一致', { inconsistencies: syncValidation.inconsistencies });

        // 尝试修复不一致的文件
        for (const inconsistency of syncValidation.inconsistencies) {
          await this.fixVersionInconsistency(inconsistency.file, versionInfo.next);
        }

        // 重新验证
        const revalidation = await VersionValidator.validateVersionSync(this.projectRoot, versionInfo.next);
        if (!revalidation.synced) {
          throw new Error(`版本同步失败: ${revalidation.inconsistencies.map(i => i.file).join(', ')}`);
        }
      }

      // 创建Git标签 - 按照规范要求
      await this.createTag(versionInfo.next, `Release version ${versionInfo.next}`);

      // 记录版本变更历史
      await VersionHistory.recordVersionChange(versionInfo.current, versionInfo.next, 'bump', {
        type,
        prerelease,
        timestamp: new Date().toISOString()
      });

      this.logger.info('版本升级完成', {
        from: versionInfo.current,
        to: versionInfo.next,
        type,
        prerelease
      });

      console.log(`版本已升级: ${versionInfo.current} → ${versionInfo.next}`);
      console.log(`Git标签已创建: v${versionInfo.next}`);
      return versionInfo;

    } catch (error) {
      this.logger.error('版本升级失败', error);
      throw error;
    }
  }

  /**
   * 从预发布版本升级到正式版本
   * 按照VERSION_MAINTENANCE_RULES.md规范实现
   */
  async releaseFromPrerelease(): Promise<VersionInfo> {
    const current = await this.getCurrentVersion();
    const currentParsed = semver.parse(current);

    if (!currentParsed || !currentParsed.prerelease.length) {
      throw new Error(`当前版本不是预发布版本: ${current}`);
    }

    // 生成正式版本号
    const releaseVersion = `${currentParsed.major}.${currentParsed.minor}.${currentParsed.patch}`;

    // 验证版本递增
    const incrementValidation = VersionValidator.validateVersionIncrement(current, releaseVersion);
    if (!incrementValidation.valid) {
      throw new Error(incrementValidation.error);
    }

    // 检查版本冲突
    const conflictCheck = await VersionValidator.checkVersionConflict(releaseVersion);
    if (conflictCheck.hasConflict) {
      throw new Error(`版本冲突: ${conflictCheck.conflicts.join(', ')}`);
    }

    const versionInfo: VersionInfo = {
      current,
      next: releaseVersion,
      type: 'patch',
      prerelease: undefined
    };

    try {
      // 更新所有版本文件
      await this.updateAllVersionFiles(releaseVersion);

      // 验证多文件版本同步
      const syncValidation = await VersionValidator.validateVersionSync(this.projectRoot, releaseVersion);
      if (!syncValidation.synced) {
        this.logger.warn('版本同步检查发现不一致', { inconsistencies: syncValidation.inconsistencies });

        for (const inconsistency of syncValidation.inconsistencies) {
          await this.fixVersionInconsistency(inconsistency.file, releaseVersion);
        }
      }

      // 创建Git标签
      await this.createTag(releaseVersion, `Release version ${releaseVersion} from prerelease ${current}`);

      // 记录版本变更历史
      await VersionHistory.recordVersionChange(current, releaseVersion, 'bump', {
        type: 'release',
        fromPrerelease: true,
        timestamp: new Date().toISOString()
      });

      this.logger.info('预发布版本已发布为正式版本', {
        from: current,
        to: releaseVersion
      });

      console.log(`预发布版本已发布: ${current} → ${releaseVersion}`);
      console.log(`Git标签已创建: v${releaseVersion}`);

      return versionInfo;

    } catch (error) {
      this.logger.error('预发布版本发布失败', error);
      throw error;
    }
  }

  /**
   * 创建 Git 标签
   */
  async createTag(version: string, message?: string): Promise<void> {
    try {
      // 验证版本格式
      if (!semver.valid(version)) {
        throw new Error(`无效的版本格式: ${version}`);
      }

      const config = await this.configManager.loadConfig();
      const tagName = `${config.git.tagPrefix}${version}`;
      const tagMessage = message || `Release version ${version}`;

      // 使用安全的Git命令参数
      const sanitizedArgs = SecurityUtils.sanitizeCommandArgs(['-a', tagName, '-m', tagMessage]);

      await this.git.addAnnotatedTag(tagName, tagMessage);
      this.logger.info('Git标签创建成功', { tagName, message: tagMessage });
    } catch (error) {
      this.logger.error('创建标签失败', { version, error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 生成变更日志
   */
  async generateChangelog(fromVersion?: string): Promise<ChangelogEntry> {
    const currentVersion = await this.getCurrentVersion();
    const config = await this.configManager.loadConfig();
    
    // 获取Git提交记录
    const from = fromVersion || await this.getLastTagVersion();
    const commits = await this.getCommitsSince(from);
    
    // 解析提交信息，分类变更
    const changes = this.parseCommitMessages(commits);
    
    const changelogEntry: ChangelogEntry = {
      version: currentVersion,
      date: new Date().toISOString().split('T')[0],
      changes
    };

    // 更新 CHANGELOG.md
    if (config.git.generateChangelog) {
      await this.updateChangelogFile(changelogEntry);
    }

    return changelogEntry;
  }

  /**
   * 获取最后一个标签版本
   */
  private async getLastTagVersion(): Promise<string> {
    try {
      const tags = await this.git.tags(['--sort=-version:refname']);
      const config = await this.configManager.loadConfig();
      const prefix = config.git.tagPrefix;
      
      for (const tag of tags.all) {
        if (tag.startsWith(prefix)) {
          return tag.substring(prefix.length);
        }
      }
      return '0.0.0';
    } catch {
      return '0.0.0';
    }
  }

  /**
   * 获取指定版本之后的提交记录
   */
  private async getCommitsSince(version: string): Promise<string[]> {
    try {
      const config = await this.configManager.loadConfig();
      const tagName = `${config.git.tagPrefix}${version}`;
      const log = await this.git.log({ from: tagName, to: 'HEAD' });
      return log.all.map(commit => commit.message);
    } catch {
      // 如果没有找到标签，获取所有提交
      const log = await this.git.log();
      return log.all.map(commit => commit.message);
    }
  }

  /**
   * 解析提交信息，分类变更
   */
  private parseCommitMessages(commits: string[]): ChangelogEntry['changes'] {
    const changes = {
      added: [] as string[],
      changed: [] as string[],
      deprecated: [] as string[],
      removed: [] as string[],
      fixed: [] as string[],
      security: [] as string[]
    };

    for (const commit of commits) {
      const message = commit.toLowerCase();
      
      if (message.includes('add') || message.includes('新增') || message.includes('feat')) {
        changes.added.push(commit);
      } else if (message.includes('fix') || message.includes('修复') || message.includes('bug')) {
        changes.fixed.push(commit);
      } else if (message.includes('remove') || message.includes('delete') || message.includes('删除')) {
        changes.removed.push(commit);
      } else if (message.includes('security') || message.includes('安全')) {
        changes.security.push(commit);
      } else if (message.includes('deprecate') || message.includes('废弃')) {
        changes.deprecated.push(commit);
      } else {
        changes.changed.push(commit);
      }
    }

    return changes;
  }

  /**
   * 更新 CHANGELOG.md 文件
   */
  private async updateChangelogFile(entry: ChangelogEntry): Promise<void> {
    const config = await this.configManager.loadConfig();
    const changelogPath = path.join(this.projectRoot, config.git.changelogPath);
    
    let content = `## [${entry.version}] - ${entry.date}\n\n`;
    
    if (entry.changes.added.length > 0) {
      content += '### 新增\n';
      entry.changes.added.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }
    
    if (entry.changes.changed.length > 0) {
      content += '### 变更\n';
      entry.changes.changed.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }
    
    if (entry.changes.fixed.length > 0) {
      content += '### 修复\n';
      entry.changes.fixed.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }
    
    if (entry.changes.removed.length > 0) {
      content += '### 移除\n';
      entry.changes.removed.forEach((change: string) => {
        content += `- ${change}\n`;
      });
      content += '\n';
    }

    // 如果文件存在，将新内容插入到现有内容之前
    if (await fs.pathExists(changelogPath)) {
      const existingContent = await fs.readFile(changelogPath, 'utf-8');
      const headerIndex = existingContent.indexOf('\n## ');
      if (headerIndex > -1) {
        const beforeHeader = existingContent.substring(0, headerIndex + 1);
        const afterHeader = existingContent.substring(headerIndex + 1);
        content = beforeHeader + content + afterHeader;
      } else {
        content = content + '\n' + existingContent;
      }
    } else {
      // 创建新的 CHANGELOG.md
      content = `# 变更日志\n\n所有重要的变更都将记录在此文件中。\n\n` + content;
    }

    await fs.writeFile(changelogPath, content, 'utf-8');
  }

  /**
   * 获取所有可用版本
   */
  async getAvailableVersions(): Promise<string[]> {
    try {
      const tags = await this.git.tags(['--sort=-version:refname']);
      const config = await this.configManager.loadConfig();
      const prefix = config.git.tagPrefix;

      return tags.all
        .filter(tag => tag.startsWith(prefix))
        .map(tag => tag.substring(prefix.length));
    } catch {
      return [];
    }
  }

  /**
   * 更新所有版本相关文件
   * 按照VERSION_MAINTENANCE_RULES.md规范同步所有文件
   */
  private async updateAllVersionFiles(version: string): Promise<void> {
    const filesToUpdate = [
      {
        path: 'game-version.config.json',
        updater: async (filePath: string) => {
          const config = await this.configManager.loadConfig();
          config.project.version = version;
          await this.configManager.saveConfig(config);
        }
      },
      {
        path: 'package.json',
        updater: async (filePath: string) => {
          if (await fs.pathExists(filePath)) {
            const packageJson = await fs.readJSON(filePath);
            packageJson.version = version;
            await fs.writeJSON(filePath, packageJson, { spaces: 2 });
          }
        }
      },
      {
        path: 'web/package.json',
        updater: async (filePath: string) => {
          if (await fs.pathExists(filePath)) {
            const webPackageJson = await fs.readJSON(filePath);
            webPackageJson.version = version;
            await fs.writeJSON(filePath, webPackageJson, { spaces: 2 });
          }
        }
      },
      {
        path: 'assets/scripts/config/VersionConfig.ts',
        updater: async (filePath: string) => {
          const fullPath = path.join(this.projectRoot, 'assets/scripts/config/VersionConfig.ts');
          if (await fs.pathExists(fullPath)) {
            let content = await fs.readFile(fullPath, 'utf8');
            content = content.replace(
              /CLIENT_VERSION\s*=\s*['"`][^'"`]+['"`]/,
              `CLIENT_VERSION = '${version}'`
            );
            await fs.writeFile(fullPath, content, 'utf8');
          } else {
            // 如果文件不存在，创建它
            await fs.ensureDir(path.dirname(fullPath));
            const versionConfigContent = `// 自动生成的版本配置文件
export const CLIENT_VERSION = '${version}';
export const BUILD_TIME = '${new Date().toISOString()}';
`;
            await fs.writeFile(fullPath, versionConfigContent, 'utf8');
          }
        }
      }
    ];

    for (const fileInfo of filesToUpdate) {
      try {
        const fullPath = path.join(this.projectRoot, fileInfo.path);
        await fileInfo.updater(fullPath);
        this.logger.info('版本文件已更新', { file: fileInfo.path, version });
      } catch (error) {
        this.logger.warn('更新版本文件失败', {
          file: fileInfo.path,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * 修复版本不一致的文件
   */
  private async fixVersionInconsistency(filePath: string, expectedVersion: string): Promise<void> {
    try {
      const fullPath = path.join(this.projectRoot, filePath);

      if (filePath.endsWith('.json')) {
        const content = await fs.readJSON(fullPath);
        if (filePath.includes('game-version.config.json')) {
          content.project.version = expectedVersion;
        } else {
          content.version = expectedVersion;
        }
        await fs.writeJSON(fullPath, content, { spaces: 2 });
      } else if (filePath.endsWith('.ts')) {
        let content = await fs.readFile(fullPath, 'utf8');
        content = content.replace(
          /CLIENT_VERSION\s*=\s*['"`][^'"`]+['"`]/,
          `CLIENT_VERSION = '${expectedVersion}'`
        );
        await fs.writeFile(fullPath, content, 'utf8');
      }

      this.logger.info('版本不一致已修复', { file: filePath, version: expectedVersion });
    } catch (error) {
      this.logger.error('修复版本不一致失败', {
        file: filePath,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}