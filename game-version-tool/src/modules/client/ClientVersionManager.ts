import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { EventEmitter } from 'events';
import chalk from 'chalk';
import { Logger } from '../../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';

/**
 * 版本信息接口
 */
export interface VersionInfo {
  version: string;
  buildNumber: number;
  releaseDate: string;
  description: string;
  mandatory: boolean;
  downloadUrl: string;
  fileSize: number;
  checksum: string;
  resources: ResourceInfo[];
}

/**
 * 资源信息接口
 */
export interface ResourceInfo {
  path: string;
  url: string;
  size: number;
  checksum: string;
  version: string;
  priority: number; // 优先级，数字越小优先级越高
}

/**
 * 更新进度信息
 */
export interface UpdateProgress {
  phase: 'checking' | 'downloading' | 'installing' | 'complete';
  progress: number; // 0-100
  currentFile?: string;
  totalFiles?: number;
  completedFiles?: number;
  downloadSpeed?: number; // KB/s
  remainingTime?: number; // 秒
}

/**
 * 客户端版本管理器
 * 负责版本检测、热更新和资源加载
 */
export class ClientVersionManager extends EventEmitter {
  private logger: Logger;
  private configManager: ConfigManager;
  private currentVersion: string;
  private localVersionFile: string;
  private resourceCacheDir: string;
  private updateServerUrl: string;

  constructor(options: {
    projectRoot?: string;
    updateServerUrl?: string;
    resourceCacheDir?: string;
  } = {}) {
    super();
    
    this.logger = Logger.getInstance();
    this.configManager = new ConfigManager(options.projectRoot);
    this.localVersionFile = path.join(options.projectRoot || process.cwd(), 'version.json');
    this.resourceCacheDir = options.resourceCacheDir || path.join(options.projectRoot || process.cwd(), '.cache', 'resources');
    this.updateServerUrl = options.updateServerUrl || 'https://api.example.com/version';
    this.currentVersion = '0.0.0';

    this.initializeVersionManager();
  }

  /**
   * 初始化版本管理器
   */
  private async initializeVersionManager(): Promise<void> {
    try {
      // 确保缓存目录存在
      await fs.ensureDir(this.resourceCacheDir);
      
      // 加载本地版本信息
      await this.loadLocalVersion();
      
      this.logger.info('客户端版本管理器初始化完成', {
        currentVersion: this.currentVersion,
        cacheDir: this.resourceCacheDir
      });
    } catch (error) {
      this.logger.error('版本管理器初始化失败', error);
      throw error;
    }
  }

  /**
   * 加载本地版本信息
   */
  private async loadLocalVersion(): Promise<void> {
    try {
      if (await fs.pathExists(this.localVersionFile)) {
        const versionData = await fs.readJSON(this.localVersionFile);
        this.currentVersion = versionData.version || '0.0.0';
      } else {
        // 创建默认版本文件
        await this.saveLocalVersion({
          version: '0.0.0',
          buildNumber: 0,
          releaseDate: new Date().toISOString(),
          description: '初始版本',
          mandatory: false,
          downloadUrl: '',
          fileSize: 0,
          checksum: '',
          resources: []
        });
      }
    } catch (error) {
      this.logger.error('加载本地版本信息失败', error);
      this.currentVersion = '0.0.0';
    }
  }

  /**
   * 保存本地版本信息
   */
  private async saveLocalVersion(versionInfo: VersionInfo): Promise<void> {
    try {
      await fs.writeJSON(this.localVersionFile, versionInfo, { spaces: 2 });
      this.currentVersion = versionInfo.version;
      this.logger.info('本地版本信息已保存', { version: versionInfo.version });
    } catch (error) {
      this.logger.error('保存本地版本信息失败', error);
      throw error;
    }
  }

  /**
   * 检查版本更新
   */
  async checkForUpdates(): Promise<{
    hasUpdate: boolean;
    versionInfo?: VersionInfo;
    updateType: 'none' | 'optional' | 'mandatory';
  }> {
    try {
      this.emit('updateCheck', { phase: 'checking', progress: 0 });
      
      this.logger.info('检查版本更新...', { currentVersion: this.currentVersion });
      
      // 从服务器获取最新版本信息
      const response = await axios.get(`${this.updateServerUrl}/latest`, {
        params: {
          platform: process.platform,
          currentVersion: this.currentVersion
        },
        timeout: 10000
      });

      const latestVersion: VersionInfo = response.data;
      
      // 比较版本号
      const hasUpdate = this.compareVersions(latestVersion.version, this.currentVersion) > 0;
      
      if (!hasUpdate) {
        this.emit('updateCheck', { phase: 'complete', progress: 100 });
        return {
          hasUpdate: false,
          updateType: 'none'
        };
      }

      const updateType = latestVersion.mandatory ? 'mandatory' : 'optional';
      
      this.logger.info('发现新版本', {
        currentVersion: this.currentVersion,
        latestVersion: latestVersion.version,
        updateType
      });

      this.emit('updateCheck', { 
        phase: 'complete', 
        progress: 100,
        hasUpdate: true,
        versionInfo: latestVersion
      });

      return {
        hasUpdate: true,
        versionInfo: latestVersion,
        updateType
      };

    } catch (error) {
      this.logger.error('检查版本更新失败', error);
      this.emit('updateError', error);
      return {
        hasUpdate: false,
        updateType: 'none'
      };
    }
  }

  /**
   * 执行热更新
   */
  async performHotUpdate(versionInfo: VersionInfo): Promise<boolean> {
    try {
      this.logger.info('开始热更新', { 
        fromVersion: this.currentVersion,
        toVersion: versionInfo.version 
      });

      // 1. 下载资源文件
      await this.downloadResources(versionInfo.resources);

      // 2. 验证文件完整性
      await this.verifyResources(versionInfo.resources);

      // 3. 应用更新
      await this.applyUpdate(versionInfo);

      // 4. 更新本地版本信息
      await this.saveLocalVersion(versionInfo);

      this.logger.info('热更新完成', { version: versionInfo.version });
      this.emit('updateComplete', { version: versionInfo.version });

      return true;

    } catch (error) {
      this.logger.error('热更新失败', error);
      this.emit('updateError', error);
      return false;
    }
  }

  /**
   * 下载资源文件
   */
  private async downloadResources(resources: ResourceInfo[]): Promise<void> {
    const totalFiles = resources.length;
    let completedFiles = 0;

    // 按优先级排序
    const sortedResources = resources.sort((a, b) => a.priority - b.priority);

    this.emit('updateProgress', {
      phase: 'downloading',
      progress: 0,
      totalFiles,
      completedFiles: 0
    });

    for (const resource of sortedResources) {
      try {
        await this.downloadSingleResource(resource);
        completedFiles++;

        const progress = Math.round((completedFiles / totalFiles) * 100);
        this.emit('updateProgress', {
          phase: 'downloading',
          progress,
          currentFile: resource.path,
          totalFiles,
          completedFiles
        });

        this.logger.debug('资源下载完成', { 
          path: resource.path,
          progress: `${completedFiles}/${totalFiles}`
        });

      } catch (error) {
        this.logger.error('资源下载失败', { path: resource.path, error });
        throw error;
      }
    }
  }

  /**
   * 下载单个资源文件
   */
  private async downloadSingleResource(resource: ResourceInfo): Promise<void> {
    const localPath = path.join(this.resourceCacheDir, resource.path);
    
    // 检查文件是否已存在且校验和匹配
    if (await fs.pathExists(localPath)) {
      const existingChecksum = await this.calculateFileChecksum(localPath);
      if (existingChecksum === resource.checksum) {
        this.logger.debug('资源文件已存在，跳过下载', { path: resource.path });
        return;
      }
    }

    // 确保目录存在
    await fs.ensureDir(path.dirname(localPath));

    // 下载文件
    const response = await axios.get(resource.url, {
      responseType: 'stream',
      timeout: 30000
    });

    const writer = fs.createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  }

  /**
   * 验证资源文件完整性
   */
  private async verifyResources(resources: ResourceInfo[]): Promise<void> {
    this.emit('updateProgress', {
      phase: 'installing',
      progress: 0
    });

    for (let i = 0; i < resources.length; i++) {
      const resource = resources[i];
      const localPath = path.join(this.resourceCacheDir, resource.path);

      if (!await fs.pathExists(localPath)) {
        throw new Error(`资源文件不存在: ${resource.path}`);
      }

      const checksum = await this.calculateFileChecksum(localPath);
      if (checksum !== resource.checksum) {
        throw new Error(`资源文件校验失败: ${resource.path}`);
      }

      const progress = Math.round(((i + 1) / resources.length) * 100);
      this.emit('updateProgress', {
        phase: 'installing',
        progress
      });
    }
  }

  /**
   * 应用更新
   */
  private async applyUpdate(versionInfo: VersionInfo): Promise<void> {
    // 这里可以实现具体的更新逻辑
    // 例如：复制文件到游戏目录、重启游戏进程等
    this.logger.info('应用更新', { version: versionInfo.version });
    
    // 触发游戏重新加载资源
    this.emit('resourcesUpdated', {
      version: versionInfo.version,
      resources: versionInfo.resources
    });
  }

  /**
   * 加载版本化资源
   */
  async loadVersionedResource(resourcePath: string): Promise<string | null> {
    try {
      const cachedPath = path.join(this.resourceCacheDir, resourcePath);
      
      if (await fs.pathExists(cachedPath)) {
        this.logger.debug('加载缓存资源', { path: resourcePath });
        return cachedPath;
      }

      // 如果缓存中没有，尝试从原始路径加载
      const originalPath = path.join(process.cwd(), resourcePath);
      if (await fs.pathExists(originalPath)) {
        this.logger.debug('加载原始资源', { path: resourcePath });
        return originalPath;
      }

      this.logger.warn('资源文件不存在', { path: resourcePath });
      return null;

    } catch (error) {
      this.logger.error('加载资源失败', { path: resourcePath, error });
      return null;
    }
  }

  /**
   * 获取资源URL（支持CDN）
   */
  getResourceUrl(resourcePath: string, version?: string): string {
    const baseUrl = this.updateServerUrl.replace('/version', '/resources');
    const versionParam = version || this.currentVersion;
    return `${baseUrl}/${resourcePath}?v=${versionParam}`;
  }

  /**
   * 清理旧版本资源
   */
  async cleanupOldResources(keepVersions: number = 3): Promise<void> {
    try {
      // 实现清理逻辑
      this.logger.info('清理旧版本资源', { keepVersions });
      
      // 这里可以实现具体的清理逻辑
      // 例如：删除超过指定数量的旧版本资源文件
      
    } catch (error) {
      this.logger.error('清理资源失败', error);
    }
  }

  /**
   * 获取当前版本
   */
  getCurrentVersion(): string {
    return this.currentVersion;
  }

  /**
   * 比较版本号
   */
  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    
    return 0;
  }

  /**
   * 计算文件校验和
   */
  private async calculateFileChecksum(filePath: string): Promise<string> {
    const crypto = require('crypto');
    const hash = crypto.createHash('md5');
    const stream = fs.createReadStream(filePath);
    
    return new Promise((resolve, reject) => {
      stream.on('data', (data) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }
}
