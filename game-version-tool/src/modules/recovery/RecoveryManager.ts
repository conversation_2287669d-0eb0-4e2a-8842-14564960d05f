import * as fs from 'fs-extra';
import * as path from 'path';
import { Logger } from '../../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
import { RollbackManager } from '../rollback/RollbackManager';

export interface RecoveryPoint {
  id: string;
  timestamp: string;
  operation: string;
  state: 'before' | 'after' | 'failed';
  files: {
    path: string;
    backup: string;
    checksum: string;
  }[];
  config: any;
  metadata: {
    version: string;
    platform?: string;
    environment?: string;
    error?: string;
  };
}

export interface RecoveryResult {
  success: boolean;
  recoveredFiles: string[];
  errors: string[];
  recoveryTime: number;
}

/**
 * 错误恢复管理器
 * 负责在操作失败时自动恢复到安全状态
 */
export class RecoveryManager {
  private logger: Logger;
  private configManager: ConfigManager;
  private rollbackManager: RollbackManager;
  private recoveryDir: string;
  private activeRecoveryPoints: Map<string, RecoveryPoint> = new Map();

  constructor(projectRoot: string = process.cwd()) {
    this.logger = Logger.getInstance();
    this.configManager = new ConfigManager(projectRoot);
    this.rollbackManager = new RollbackManager(projectRoot);
    this.recoveryDir = path.join(projectRoot, '.recovery');
  }

  /**
   * 初始化恢复管理器
   */
  async initialize(): Promise<void> {
    await fs.ensureDir(this.recoveryDir);
    
    // 检查是否有未完成的恢复操作
    await this.checkPendingRecoveries();
    
    this.logger.info('错误恢复管理器初始化完成');
  }

  /**
   * 创建恢复点（操作前）
   */
  async createRecoveryPoint(operation: string, metadata: {
    version: string;
    platform?: string;
    environment?: string;
  }): Promise<string> {
    try {
      const recoveryId = this.generateRecoveryId(operation);
      const timestamp = new Date().toISOString();

      this.logger.info('创建恢复点', { recoveryId, operation, metadata });

      // 1. 备份关键文件
      const files = await this.backupCriticalFiles(recoveryId);

      // 2. 备份配置
      const config = await this.backupConfiguration();

      // 3. 创建恢复点
      const recoveryPoint: RecoveryPoint = {
        id: recoveryId,
        timestamp,
        operation,
        state: 'before',
        files,
        config,
        metadata
      };

      // 4. 保存恢复点
      await this.saveRecoveryPoint(recoveryPoint);
      this.activeRecoveryPoints.set(recoveryId, recoveryPoint);

      this.logger.info('恢复点创建完成', { recoveryId, filesCount: files.length });
      return recoveryId;

    } catch (error) {
      this.logger.error('创建恢复点失败', error);
      throw new Error(`创建恢复点失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 标记操作成功完成
   */
  async markOperationSuccess(recoveryId: string): Promise<void> {
    try {
      const recoveryPoint = this.activeRecoveryPoints.get(recoveryId);
      if (!recoveryPoint) {
        this.logger.warn('恢复点不存在', { recoveryId });
        return;
      }

      recoveryPoint.state = 'after';
      await this.saveRecoveryPoint(recoveryPoint);
      
      // 操作成功，可以清理恢复点
      setTimeout(() => {
        this.cleanupRecoveryPoint(recoveryId);
      }, 5 * 60 * 1000); // 5分钟后清理

      this.logger.info('操作标记为成功', { recoveryId });

    } catch (error) {
      this.logger.error('标记操作成功失败', error);
    }
  }

  /**
   * 执行错误恢复
   */
  async performErrorRecovery(recoveryId: string, error: Error): Promise<RecoveryResult> {
    const startTime = Date.now();
    const result: RecoveryResult = {
      success: false,
      recoveredFiles: [],
      errors: [],
      recoveryTime: 0
    };

    try {
      this.logger.error('开始错误恢复', { recoveryId, error: error instanceof Error ? error.message : String(error) });

      const recoveryPoint = this.activeRecoveryPoints.get(recoveryId) || 
                           await this.loadRecoveryPoint(recoveryId);

      if (!recoveryPoint) {
        throw new Error(`恢复点不存在: ${recoveryId}`);
      }

      // 标记为失败状态
      recoveryPoint.state = 'failed';
      recoveryPoint.metadata.error = error instanceof Error ? error.message : String(error);
      await this.saveRecoveryPoint(recoveryPoint);

      // 1. 恢复文件
      const fileRecoveryResult = await this.recoverFiles(recoveryPoint);
      result.recoveredFiles.push(...fileRecoveryResult.recoveredFiles);
      result.errors.push(...fileRecoveryResult.errors);

      // 2. 恢复配置
      const configRecoveryResult = await this.recoverConfiguration(recoveryPoint);
      if (!configRecoveryResult.success) {
        result.errors.push(...configRecoveryResult.errors);
      }

      // 3. 清理临时文件和状态
      await this.cleanupFailedOperation(recoveryPoint);

      // 4. 验证恢复结果
      const verificationResult = await this.verifyRecovery(recoveryPoint);
      if (!verificationResult.success) {
        result.errors.push(...verificationResult.errors);
      }

      result.success = result.errors.length === 0;
      result.recoveryTime = Date.now() - startTime;

      if (result.success) {
        this.logger.info('错误恢复成功', {
          recoveryId,
          recoveredFiles: result.recoveredFiles.length,
          recoveryTime: result.recoveryTime
        });
      } else {
        this.logger.error('错误恢复部分失败', {
          recoveryId,
          errors: result.errors,
          recoveryTime: result.recoveryTime
        });
      }

      return result;

    } catch (recoveryError) {
      result.errors.push(`恢复过程异常: ${recoveryError instanceof Error ? recoveryError.message : String(recoveryError)}`);
      result.recoveryTime = Date.now() - startTime;
      
      this.logger.error('错误恢复失败', {
        recoveryId,
        originalError: error instanceof Error ? error.message : String(error),
        recoveryError: recoveryError instanceof Error ? recoveryError.message : String(recoveryError)
      });

      return result;
    }
  }

  /**
   * 自动恢复构建失败
   */
  async recoverFromBuildFailure(platform: string, error: Error): Promise<RecoveryResult> {
    try {
      this.logger.info('开始构建失败恢复', { platform, error: error instanceof Error ? error.message : String(error) });

      const result: RecoveryResult = {
        success: false,
        recoveredFiles: [],
        errors: [],
        recoveryTime: 0
      };

      const startTime = Date.now();

      // 1. 清理构建输出目录
      await this.cleanupBuildOutput(platform);
      result.recoveredFiles.push(`build output for ${platform}`);

      // 2. 恢复构建前状态
      await this.restoreBuildEnvironment(platform);

      // 3. 清理临时文件
      await this.cleanupTempFiles();

      // 4. 重置构建状态
      await this.resetBuildState(platform);

      result.success = true;
      result.recoveryTime = Date.now() - startTime;

      this.logger.info('构建失败恢复完成', {
        platform,
        recoveryTime: result.recoveryTime
      });

      return result;

    } catch (recoveryError) {
      this.logger.error('构建失败恢复异常', recoveryError);
      return {
        success: false,
        recoveredFiles: [],
        errors: [`构建恢复失败: ${recoveryError instanceof Error ? recoveryError.message : String(recoveryError)}`],
        recoveryTime: 0
      };
    }
  }

  /**
   * 自动恢复部署失败
   */
  async recoverFromDeployFailure(platform: string, environment: string, error: Error): Promise<RecoveryResult> {
    try {
      this.logger.info('开始部署失败恢复', { platform, environment, error: error instanceof Error ? error.message : String(error) });

      // 尝试回滚到上一个稳定版本
      const rollbackPoints = await this.rollbackManager.listRollbackPoints();
      const lastStablePoint = rollbackPoints.find(point => 
        point.deploymentInfo.environment === environment &&
        point.deploymentInfo.platforms.includes(platform) &&
        point.deploymentInfo.status === 'success'
      );

      if (lastStablePoint) {
        this.logger.info('找到稳定回滚点，开始回滚', { 
          rollbackPointId: lastStablePoint.id,
          version: lastStablePoint.version 
        });

        const rollbackResult = await this.rollbackManager.rollbackToPoint(lastStablePoint.id, {
          restoreConfig: true,
          restoreBuild: true,
          redeploy: true
        });

        return {
          success: rollbackResult.success,
          recoveredFiles: rollbackResult.restoredFiles,
          errors: rollbackResult.errors,
          recoveryTime: rollbackResult.rollbackTime
        };
      } else {
        this.logger.warn('未找到稳定的回滚点', { platform, environment });
        
        return {
          success: false,
          recoveredFiles: [],
          errors: ['未找到可回滚的稳定版本'],
          recoveryTime: 0
        };
      }

    } catch (recoveryError) {
      this.logger.error('部署失败恢复异常', recoveryError);
      return {
        success: false,
        recoveredFiles: [],
        errors: [`部署恢复失败: ${recoveryError instanceof Error ? recoveryError.message : String(recoveryError)}`],
        recoveryTime: 0
      };
    }
  }

  /**
   * 恢复损坏的配置
   */
  async recoverCorruptedConfig(): Promise<RecoveryResult> {
    try {
      this.logger.info('开始配置恢复');

      const result: RecoveryResult = {
        success: false,
        recoveredFiles: [],
        errors: [],
        recoveryTime: 0
      };

      const startTime = Date.now();

      // 1. 尝试从最近的恢复点恢复
      const recentRecoveryPoints = await this.getRecentRecoveryPoints();
      
      for (const recoveryPoint of recentRecoveryPoints) {
        if (recoveryPoint.config && Object.keys(recoveryPoint.config).length > 0) {
          try {
            await this.configManager.saveConfig(recoveryPoint.config);
            result.recoveredFiles.push('game-version.config.json');
            result.success = true;
            
            this.logger.info('从恢复点恢复配置成功', { 
              recoveryPointId: recoveryPoint.id 
            });
            break;
          } catch (error) {
            this.logger.warn('从恢复点恢复配置失败', { 
              recoveryPointId: recoveryPoint.id,
              error: error instanceof Error ? error.message : String(error) 
            });
          }
        }
      }

      // 2. 如果恢复点恢复失败，尝试从默认配置恢复
      if (!result.success) {
        try {
          const defaultConfig = this.createDefaultConfig();
          await this.configManager.saveConfig(defaultConfig);
          result.recoveredFiles.push('game-version.config.json (default)');
          result.success = true;
          
          this.logger.info('使用默认配置恢复成功');
        } catch (error) {
          result.errors.push(`默认配置恢复失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      result.recoveryTime = Date.now() - startTime;

      return result;

    } catch (error) {
      this.logger.error('配置恢复异常', error);
      return {
        success: false,
        recoveredFiles: [],
        errors: [`配置恢复失败: ${error instanceof Error ? error.message : String(error)}`],
        recoveryTime: 0
      };
    }
  }

  /**
   * 检查待处理的恢复操作
   */
  private async checkPendingRecoveries(): Promise<void> {
    try {
      const recoveryFiles = await fs.readdir(this.recoveryDir);
      
      for (const file of recoveryFiles) {
        if (file.endsWith('.json')) {
          const recoveryPoint = await this.loadRecoveryPoint(path.basename(file, '.json'));
          
          if (recoveryPoint && recoveryPoint.state === 'before') {
            this.logger.warn('发现未完成的操作，可能需要恢复', {
              recoveryId: recoveryPoint.id,
              operation: recoveryPoint.operation,
              timestamp: recoveryPoint.timestamp
            });
            
            // 可以选择自动恢复或提示用户
            this.activeRecoveryPoints.set(recoveryPoint.id, recoveryPoint);
          }
        }
      }
    } catch (error) {
      this.logger.error('检查待处理恢复操作失败', error);
    }
  }

  /**
   * 生成恢复ID
   */
  private generateRecoveryId(operation: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `recovery_${operation}_${timestamp}_${random}`;
  }

  /**
   * 备份关键文件
   */
  private async backupCriticalFiles(recoveryId: string): Promise<RecoveryPoint['files']> {
    const files: RecoveryPoint['files'] = [];
    const backupDir = path.join(this.recoveryDir, recoveryId);
    
    await fs.ensureDir(backupDir);

    const criticalFiles = [
      'game-version.config.json',
      'package.json',
      'tsconfig.json'
    ];

    for (const file of criticalFiles) {
      if (await fs.pathExists(file)) {
        const backupPath = path.join(backupDir, file);
        await fs.ensureDir(path.dirname(backupPath));
        await fs.copy(file, backupPath);
        
        // 计算校验和
        const content = await fs.readFile(file);
        const checksum = require('crypto').createHash('md5').update(content).digest('hex');
        
        files.push({
          path: file,
          backup: backupPath,
          checksum
        });
      }
    }

    return files;
  }

  /**
   * 备份配置
   */
  private async backupConfiguration(): Promise<any> {
    try {
      return await this.configManager.loadConfig();
    } catch (error) {
      this.logger.warn('备份配置失败', error);
      return {};
    }
  }

  /**
   * 保存恢复点
   */
  private async saveRecoveryPoint(recoveryPoint: RecoveryPoint): Promise<void> {
    const filePath = path.join(this.recoveryDir, `${recoveryPoint.id}.json`);
    await fs.writeJSON(filePath, recoveryPoint, { spaces: 2 });
  }

  /**
   * 加载恢复点
   */
  private async loadRecoveryPoint(recoveryId: string): Promise<RecoveryPoint | null> {
    try {
      const filePath = path.join(this.recoveryDir, `${recoveryId}.json`);
      
      if (!await fs.pathExists(filePath)) {
        return null;
      }

      return await fs.readJSON(filePath);
    } catch (error) {
      this.logger.error('加载恢复点失败', { recoveryId, error });
      return null;
    }
  }

  /**
   * 恢复文件
   */
  private async recoverFiles(recoveryPoint: RecoveryPoint): Promise<{
    recoveredFiles: string[];
    errors: string[];
  }> {
    const recoveredFiles: string[] = [];
    const errors: string[] = [];

    for (const file of recoveryPoint.files) {
      try {
        if (await fs.pathExists(file.backup)) {
          await fs.copy(file.backup, file.path);
          recoveredFiles.push(file.path);
          this.logger.info('文件恢复成功', { path: file.path });
        } else {
          errors.push(`备份文件不存在: ${file.backup}`);
        }
      } catch (error) {
        errors.push(`恢复文件失败 ${file.path}: ${error instanceof Error ? error.message : String(error)}`);
        this.logger.error('文件恢复失败', { path: file.path, error });
      }
    }

    return { recoveredFiles, errors };
  }

  /**
   * 恢复配置
   */
  private async recoverConfiguration(recoveryPoint: RecoveryPoint): Promise<{
    success: boolean;
    errors: string[];
  }> {
    try {
      if (recoveryPoint.config && Object.keys(recoveryPoint.config).length > 0) {
        await this.configManager.saveConfig(recoveryPoint.config);
        this.logger.info('配置恢复成功');
        return { success: true, errors: [] };
      } else {
        return { success: false, errors: ['没有可恢复的配置'] };
      }
    } catch (error) {
      this.logger.error('配置恢复失败', error);
      return { success: false, errors: [`配置恢复失败: ${error instanceof Error ? error.message : String(error)}`] };
    }
  }

  /**
   * 清理失败操作的残留
   */
  private async cleanupFailedOperation(recoveryPoint: RecoveryPoint): Promise<void> {
    try {
      // 根据操作类型执行特定的清理
      switch (recoveryPoint.operation) {
        case 'build':
          if (recoveryPoint.metadata.platform) {
            await this.cleanupBuildOutput(recoveryPoint.metadata.platform);
          }
          break;
        case 'deploy':
          // 清理部署相关的临时文件
          break;
        default:
          // 通用清理
          await this.cleanupTempFiles();
      }
    } catch (error) {
      this.logger.warn('清理失败操作残留时出错', error);
    }
  }

  /**
   * 验证恢复结果
   */
  private async verifyRecovery(recoveryPoint: RecoveryPoint): Promise<{
    success: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      // 验证文件完整性
      for (const file of recoveryPoint.files) {
        if (await fs.pathExists(file.path)) {
          const content = await fs.readFile(file.path);
          const checksum = require('crypto').createHash('md5').update(content).digest('hex');
          
          if (checksum !== file.checksum) {
            errors.push(`文件校验失败: ${file.path}`);
          }
        } else {
          errors.push(`恢复的文件不存在: ${file.path}`);
        }
      }

      // 验证配置有效性
      try {
        await this.configManager.loadConfig();
      } catch (error) {
        errors.push(`配置文件无效: ${error instanceof Error ? error.message : String(error)}`);
      }

      return { success: errors.length === 0, errors };
    } catch (error) {
      errors.push(`验证过程异常: ${error instanceof Error ? error.message : String(error)}`);
      return { success: false, errors };
    }
  }

  /**
   * 清理恢复点
   */
  private async cleanupRecoveryPoint(recoveryId: string): Promise<void> {
    try {
      const recoveryDir = path.join(this.recoveryDir, recoveryId);
      const recoveryFile = path.join(this.recoveryDir, `${recoveryId}.json`);

      if (await fs.pathExists(recoveryDir)) {
        await fs.remove(recoveryDir);
      }

      if (await fs.pathExists(recoveryFile)) {
        await fs.remove(recoveryFile);
      }

      this.activeRecoveryPoints.delete(recoveryId);
      this.logger.info('恢复点已清理', { recoveryId });

    } catch (error) {
      this.logger.error('清理恢复点失败', { recoveryId, error });
    }
  }

  /**
   * 获取最近的恢复点
   */
  private async getRecentRecoveryPoints(): Promise<RecoveryPoint[]> {
    try {
      const recoveryFiles = await fs.readdir(this.recoveryDir);
      const recoveryPoints: RecoveryPoint[] = [];

      for (const file of recoveryFiles) {
        if (file.endsWith('.json')) {
          const recoveryPoint = await this.loadRecoveryPoint(path.basename(file, '.json'));
          if (recoveryPoint) {
            recoveryPoints.push(recoveryPoint);
          }
        }
      }

      // 按时间戳排序，最新的在前
      return recoveryPoints.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ).slice(0, 5); // 只返回最近5个

    } catch (error) {
      this.logger.error('获取最近恢复点失败', error);
      return [];
    }
  }

  /**
   * 创建默认配置
   */
  private createDefaultConfig(): any {
    return {
      project: {
        name: "game-project",
        type: "cocos-creator",
        version: "1.0.0",
        description: "游戏项目"
      },
      build: {
        platforms: ["web-mobile"],
        outputDir: "./dist",
        optimization: {
          compress: true,
          minify: true,
          sourcemap: false
        }
      },
      deploy: {
        staging: {},
        production: {}
      }
    };
  }

  /**
   * 清理构建输出
   */
  private async cleanupBuildOutput(platform: string): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      const buildDir = path.join(config.build.outputDir, platform);
      
      if (await fs.pathExists(buildDir)) {
        await fs.remove(buildDir);
        this.logger.info('构建输出已清理', { platform, buildDir });
      }
    } catch (error) {
      this.logger.warn('清理构建输出失败', { platform, error });
    }
  }

  /**
   * 恢复构建环境
   */
  private async restoreBuildEnvironment(platform: string): Promise<void> {
    // 实现构建环境恢复逻辑
    this.logger.info('恢复构建环境', { platform });
  }

  /**
   * 清理临时文件
   */
  private async cleanupTempFiles(): Promise<void> {
    try {
      const tempDirs = ['temp', '.tmp', 'node_modules/.cache'];
      
      for (const dir of tempDirs) {
        if (await fs.pathExists(dir)) {
          await fs.remove(dir);
          this.logger.info('临时目录已清理', { dir });
        }
      }
    } catch (error) {
      this.logger.warn('清理临时文件失败', error);
    }
  }

  /**
   * 重置构建状态
   */
  private async resetBuildState(platform: string): Promise<void> {
    // 实现构建状态重置逻辑
    this.logger.info('构建状态已重置', { platform });
  }
}
