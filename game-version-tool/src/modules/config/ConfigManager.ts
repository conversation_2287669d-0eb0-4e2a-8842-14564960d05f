import * as fs from 'fs-extra';
import * as path from 'path';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { GameVersionConfig } from '../../types/config';
import configSchema from '../../schemas/config-schema.json';
import { SecurityUtils } from '../../utils/SecurityUtils';
import { Logger } from '../../utils/Logger';

export class ConfigManager {
  private ajv: Ajv;
  private configPath: string;
  private config: GameVersionConfig | null = null;

  constructor(projectRoot: string = process.cwd()) {
    this.ajv = new Ajv({ allErrors: true });
    addFormats(this.ajv);
    this.ajv.addSchema(configSchema, 'config');
    
    this.configPath = path.join(projectRoot, 'game-version.config.json');
  }

  /**
   * 初始化配置文件
   */
  async initConfig(options: Partial<GameVersionConfig> = {}): Promise<void> {
    const defaultConfig: GameVersionConfig = {
      project: {
        name: options.project?.name || 'LuckyCoin',
        type: 'cocos-creator',
        version: '0.1.0',
        description: options.project?.description || 'LuckyCoin game project'
      },
      build: {
        platforms: ['web-mobile'],
        outputDir: './dist',
        cocosCreator: {
          projectPath: process.cwd(),
          builderPath: undefined
        },
        optimization: {
          compress: true,
          minify: true,
          sourcemap: false
        },
        excludeFiles: ['*.log', 'node_modules/**']
      },
      deploy: {
        web: {
          staging: 'https://staging.example.com',
          production: 'https://production.example.com'
        }
      },
      environments: {
        dev: './config/dev',
        test: './config/test',
        prod: './config/prod'
      },
      git: {
        autoTag: true,
        tagPrefix: 'v',
        generateChangelog: true,
        changelogPath: './CHANGELOG.md'
      },
      notification: {
        enabled: false
      },
      ...options
    };

    await fs.writeJSON(this.configPath, defaultConfig, { spaces: 2 });
    this.config = defaultConfig;
  }

  /**
   * 加载配置文件
   */
  async loadConfig(): Promise<GameVersionConfig> {
    if (!await fs.pathExists(this.configPath)) {
      throw new Error(`配置文件不存在: ${this.configPath}`);
    }

    const configData = await fs.readJSON(this.configPath);
    
    // 验证配置文件
    const valid = this.ajv.validate('config', configData);
    if (!valid) {
      const errors = this.ajv.errors?.map(err => 
        `${err.instancePath}: ${err.message}`
      ).join('\n');
      throw new Error(`配置文件验证失败:\n${errors}`);
    }

    this.config = configData;
    
    // 类型断言：此时 this.config 不可能为 null
    if (!this.config) {
      throw new Error('配置加载失败');
    }
    
    return this.config;
  }

  /**
   * 更新配置文件
   */
  async updateConfig(updates: Partial<GameVersionConfig>): Promise<void> {
    if (!this.config) {
      await this.loadConfig();
    }

    const updatedConfig = { ...this.config!, ...updates };
    
    // 验证更新后的配置
    const valid = this.ajv.validate('config', updatedConfig);
    if (!valid) {
      const errors = this.ajv.errors?.map(err => 
        `${err.instancePath}: ${err.message}`
      ).join('\n');
      throw new Error(`配置更新验证失败:\n${errors}`);
    }

    await fs.writeJSON(this.configPath, updatedConfig, { spaces: 2 });
    this.config = updatedConfig;
  }

  /**
   * 获取当前配置
   */
  getConfig(): GameVersionConfig {
    if (!this.config) {
      throw new Error('配置未加载，请先调用 loadConfig()');
    }
    return this.config;
  }

  /**
   * 检查配置文件是否存在
   */
  async configExists(): Promise<boolean> {
    return fs.pathExists(this.configPath);
  }

  /**
   * 获取环境配置路径
   */
  getEnvironmentConfigPath(env: string): string {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    
    const envPath = this.config.environments[env];
    if (!envPath) {
      throw new Error(`环境 ${env} 的配置路径未定义`);
    }
    
    return path.resolve(envPath);
  }

  /**
   * 验证环境配置
   */
  async validateEnvironmentConfig(env: string): Promise<boolean> {
    try {
      const envConfigPath = this.getEnvironmentConfigPath(env);
      return await fs.pathExists(envConfigPath);
    } catch {
      return false;
    }
  }
}