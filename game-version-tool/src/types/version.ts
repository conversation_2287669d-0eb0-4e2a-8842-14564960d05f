export type VersionType = 'major' | 'minor' | 'patch' | 'prerelease';

export type PrereleaseType = 'alpha' | 'beta' | 'rc';

export interface VersionInfo {
  current: string;
  next: string;
  type: VersionType;
  prerelease?: PrereleaseType;
}

export interface BuildResult {
  platform: string;
  success: boolean;
  outputPath?: string;
  buildTime: number;
  fileSize?: number;
  error?: string;
}

export interface DeployResult {
  platform: string;
  environment: string;
  success: boolean;
  url?: string;
  deployTime: number;
  error?: string;
  verified?: boolean;
  rollbackTime?: number;
  backupId?: string;
  healthCheckUrl?: string;
  deploymentId?: string;
}

export interface DeploymentVerification {
  success: boolean;
  errors: string[];
  warnings: string[];
  healthChecks: {
    name: string;
    status: 'pass' | 'fail' | 'warn';
    message: string;
    responseTime?: number;
  }[];
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: {
    added: string[];
    changed: string[];
    deprecated: string[];
    removed: string[];
    fixed: string[];
    security: string[];
  };
}

export interface RollbackInfo {
  version: string;
  date: string;
  commitHash: string;
  buildExists: boolean;
}