import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { VersionManager } from '../modules/version/VersionManager';
import { BuildManager } from '../modules/build/BuildManager';
import { DeployManager } from '../modules/deploy/DeployManager';
import { RollbackInfo } from '../types/version';

export class RollbackCommand {
  private versionManager: VersionManager;
  private buildManager: BuildManager;
  private deployManager: DeployManager;

  constructor() {
    this.versionManager = new VersionManager();
    this.buildManager = new BuildManager();
    this.deployManager = new DeployManager();
  }

  /**
   * 注册所有回滚相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 列出可回滚版本
    program
      .command('rollback-list')
      .description('列出可回滚的版本')
      .option('-l, --limit <number>', '限制显示数量', '10')
      .action(async (options) => {
        await this.listRollbackVersions(options);
      });

    // 回滚到指定版本
    program
      .command('rollback-to <version>')
      .description('回滚到指定版本')
      .option('-p, --platform <platform>', '指定平台')
      .option('-e, --environment <environment>', '指定环境', 'staging')
      .option('-f, --force', '强制回滚，跳过确认')
      .action(async (version: string, options) => {
        await this.rollbackToVersion(version, options);
      });

    // 快速回滚到上一个版本
    program
      .command('rollback-last')
      .description('回滚到上一个版本')
      .option('-p, --platform <platform>', '指定平台')
      .option('-e, --environment <environment>', '指定环境', 'staging')
      .action(async (options) => {
        await this.rollbackToLast(options);
      });

    // 检查回滚状态
    program
      .command('rollback-status')
      .description('检查回滚状态')
      .action(async () => {
        await this.checkRollbackStatus();
      });

    // 创建回滚点
    program
      .command('rollback-checkpoint [name]')
      .description('创建回滚点')
      .action(async (name?: string) => {
        await this.createCheckpoint(name);
      });
  }

  private async listRollbackVersions(options?: any): Promise<void> {
    try {
      const spinner = ora('获取可回滚版本...').start();
      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();
      spinner.stop();

      if (versions.length === 0) {
        console.log(chalk.yellow('暂无可回滚版本'));
        return;
      }

      const limit = parseInt(options?.limit || '10');
      const limitedVersions = versions.slice(0, limit);

      console.log(chalk.blue('📚 可回滚版本:'));
      console.log('');

      for (const version of limitedVersions) {
        const rollbackInfo = await this.getRollbackInfo(version);
        
        if (version === currentVersion) {
          console.log(chalk.green(`  ✓ ${version} (当前版本)`));
        } else {
          console.log(chalk.gray(`    ${version}`));
        }
        
        console.log(chalk.gray(`      日期: ${rollbackInfo.date}`));
        console.log(chalk.gray(`      构建: ${rollbackInfo.buildExists ? '✓ 可用' : '✗ 不可用'}`));
        console.log(chalk.gray(`      提交: ${rollbackInfo.commitHash.substring(0, 8)}`));
        console.log('');
      }

      if (versions.length > limit) {
        console.log(chalk.gray(`... 还有 ${versions.length - limit} 个版本 (使用 --limit 查看更多)`));
      }

    } catch (error) {
      console.error(chalk.red('获取版本列表失败:'), error);
    }
  }

  private async rollbackToVersion(version: string, options?: any): Promise<void> {
    try {
      console.log(chalk.blue(`🔄 回滚到版本: ${version}`));

      // 检查版本是否存在
      const versions = await this.versionManager.getAvailableVersions();
      if (!versions.includes(version)) {
        console.log(chalk.red(`版本 ${version} 不存在`));
        return;
      }

      const currentVersion = await this.versionManager.getCurrentVersion();
      if (version === currentVersion) {
        console.log(chalk.yellow('已经是当前版本，无需回滚'));
        return;
      }

      // 获取回滚信息
      const rollbackInfo = await this.getRollbackInfo(version);

      // 显示回滚信息
      console.log(chalk.gray(`目标版本: ${version}`));
      console.log(chalk.gray(`创建日期: ${rollbackInfo.date}`));
      console.log(chalk.gray(`构建状态: ${rollbackInfo.buildExists ? '可用' : '不可用'}`));

      // 确认回滚
      if (!options?.force) {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: chalk.yellow(`确定要从 ${currentVersion} 回滚到 ${version} 吗？`),
            default: false
          }
        ]);

        if (!confirm) {
          console.log(chalk.gray('回滚已取消'));
          return;
        }
      }

      // 执行回滚
      await this.executeRollback(version, options);

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error);
    }
  }

  private async rollbackToLast(options?: any): Promise<void> {
    try {
      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();

      // 找到上一个版本
      const currentIndex = versions.indexOf(currentVersion);
      if (currentIndex === -1 || currentIndex === versions.length - 1) {
        console.log(chalk.yellow('没有可回滚的上一个版本'));
        return;
      }

      const lastVersion = versions[currentIndex + 1];
      console.log(chalk.blue(`🔄 回滚到上一个版本: ${lastVersion}`));

      await this.rollbackToVersion(lastVersion, { ...options, force: false });

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error);
    }
  }

  private async checkRollbackStatus(): Promise<void> {
    try {
      const spinner = ora('检查回滚状态...').start();
      
      const currentVersion = await this.versionManager.getCurrentVersion();
      const versions = await this.versionManager.getAvailableVersions();
      const rollbackInfo = await this.getRollbackInfo(currentVersion);

      spinner.stop();

      console.log(chalk.blue('🔍 回滚状态检查:'));
      console.log('');
      console.log(chalk.gray(`当前版本: ${currentVersion}`));
      console.log(chalk.gray(`版本日期: ${rollbackInfo.date}`));
      console.log(chalk.gray(`构建状态: ${rollbackInfo.buildExists ? '✓ 可用' : '✗ 不可用'}`));
      console.log(chalk.gray(`可回滚版本数: ${versions.length - 1}`));
      console.log('');

      // 检查最近的回滚操作
      const deployHistory = await this.deployManager.getDeploymentHistory();
      if (deployHistory.length > 0) {
        const lastDeploy = deployHistory[0];
        console.log(chalk.gray(`最后部署: ${lastDeploy.version} (${lastDeploy.timestamp})`));
      }

    } catch (error) {
      console.error(chalk.red('检查回滚状态失败:'), error);
    }
  }

  private async createCheckpoint(name?: string): Promise<void> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();
      const checkpointName = name || `checkpoint-${Date.now()}`;

      console.log(chalk.blue(`📍 创建回滚点: ${checkpointName}`));

      // 创建 Git 标签作为回滚点
      const tagMessage = `Rollback checkpoint: ${checkpointName}`;
      const spinner = ora('创建回滚点...').start();

      await this.versionManager.createTag(`${currentVersion}-${checkpointName}`, tagMessage);
      
      spinner.succeed(chalk.green(`回滚点 ${checkpointName} 创建成功`));
      console.log(chalk.gray(`版本: ${currentVersion}`));
      console.log(chalk.gray(`标签: ${currentVersion}-${checkpointName}`));

    } catch (error) {
      console.error(chalk.red('创建回滚点失败:'), error);
    }
  }

  private async executeRollback(version: string, options?: any): Promise<void> {
    const steps = [
      { name: '更新版本号', action: () => this.updateVersionTo(version) },
      { name: '重新构建', action: () => this.rebuildVersion(version, options?.platform) },
      { name: '重新部署', action: () => this.redeployVersion(version, options) }
    ];

    for (const step of steps) {
      const spinner = ora(step.name).start();
      try {
        await step.action();
        spinner.succeed(chalk.green(`${step.name} 完成`));
      } catch (error) {
        spinner.fail(chalk.red(`${step.name} 失败`));
        throw error;
      }
    }

    console.log(chalk.green(`\n🎉 回滚到版本 ${version} 完成！`));
  }

  private async updateVersionTo(version: string): Promise<void> {
    try {
      // 1. 检出到指定版本的Git标签
      const tagName = version.startsWith('v') ? version : `v${version}`;
      await this.git.checkout([tagName]);
      console.log(`已切换到标签: ${tagName}`);

      // 2. 更新配置文件中的版本号
      const config = await this.configManager.loadConfig();
      config.project.version = version;
      await this.configManager.saveConfig(config);
      console.log(`配置文件版本已更新: ${version}`);

      // 3. 更新package.json中的版本号（如果存在）
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJSON(packageJsonPath);
        packageJson.version = version;
        await fs.writeJSON(packageJsonPath, packageJson, { spaces: 2 });
        console.log(`package.json版本已更新: ${version}`);
      }

    } catch (error) {
      console.error('更新版本失败:', error);
      throw error;
    }
  }

  private async rebuildVersion(version: string, platform?: string): Promise<void> {
    if (platform) {
      await this.buildManager.buildPlatform(platform);
    } else {
      await this.buildManager.buildAllPlatforms();
    }
  }

  private async redeployVersion(version: string, options?: any): Promise<void> {
    const environment = options?.environment || 'staging';
    const platform = options?.platform;

    await this.deployManager.deployToEnvironment(environment, platform);
  }

  private async getRollbackInfo(version: string): Promise<RollbackInfo> {
    // 这里应该实现获取版本详细信息的逻辑
    // 包括日期、提交哈希、构建状态等
    return {
      version,
      date: new Date().toISOString().split('T')[0],
      commitHash: 'abcd1234567890abcd1234567890abcd12345678',
      buildExists: true
    };
  }
}