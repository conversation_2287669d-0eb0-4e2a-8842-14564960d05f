// API 入口文件 - 提供编程接口
export { ConfigManager } from './modules/config/ConfigManager';
export { VersionManager } from './modules/version/VersionManager';
export { BuildManager } from './modules/build/BuildManager';
export { DeployManager } from './modules/deploy/DeployManager';

// 导入模块用于内部使用
import { ConfigManager } from './modules/config/ConfigManager';
import { VersionManager } from './modules/version/VersionManager';
import { BuildManager } from './modules/build/BuildManager';
import { DeployManager } from './modules/deploy/DeployManager';
import { BuildResult, DeployResult } from './types/version';

// 类型定义
export * from './types/config';
export * from './types/version';

// 工具类
export { Logger, LogLevel, logger } from './utils/Logger';
export { FileUtils } from './utils/FileUtils';

// 主要功能类
export class GameVersionTool {
  public config: ConfigManager;
  public version: VersionManager;
  public build: BuildManager;
  public deploy: DeployManager;

  constructor(projectRoot?: string) {
    this.config = new ConfigManager(projectRoot);
    this.version = new VersionManager(projectRoot);
    this.build = new BuildManager(projectRoot);
    this.deploy = new DeployManager(projectRoot);
  }

  /**
   * 初始化工具
   */
  async initialize(): Promise<void> {
    // 检查配置文件是否存在
    if (!await this.config.configExists()) {
      await this.config.initConfig();
    }
    
    // 加载配置
    await this.config.loadConfig();
  }

  /**
   * 完整的发布流程
   */
  async release(options: {
    versionType: 'major' | 'minor' | 'patch';
    platforms?: string[];
    environment?: 'staging' | 'production';
    createTag?: boolean;
    generateChangelog?: boolean;
  }): Promise<void> {
    const { versionType, platforms, environment = 'staging', createTag = true, generateChangelog = true } = options;

    // 1. 升级版本
    const versionInfo = await this.version.bumpVersion(versionType);
    console.log(`版本已升级: ${versionInfo.current} → ${versionInfo.next}`);

    // 2. 生成变更日志
    if (generateChangelog) {
      await this.version.generateChangelog();
      console.log('变更日志已生成');
    }

    // 3. 创建标签
    if (createTag) {
      await this.version.createTag(versionInfo.next);
      console.log(`Git 标签已创建: v${versionInfo.next}`);
    }

    // 4. 构建项目
    const buildResults = platforms 
      ? await Promise.all(platforms.map(p => this.build.buildPlatform(p)))
      : await this.build.buildAllPlatforms();

    const successfulBuilds = buildResults.filter((r: BuildResult) => r.success);
    if (successfulBuilds.length === 0) {
      throw new Error('所有平台构建都失败了');
    }

    console.log(`构建完成: ${successfulBuilds.length}/${buildResults.length} 成功`);

    // 5. 部署
    const deployResults = await this.deploy.deployToEnvironment(environment);
    const successfulDeploys = deployResults.filter((r: DeployResult) => r.success);

    console.log(`部署完成: ${successfulDeploys.length}/${deployResults.length} 成功`);

    if (successfulDeploys.length > 0) {
      console.log(`🎉 发布完成！版本: ${versionInfo.next}`);
    }
  }

  /**
   * 快速构建和部署
   */
  async quickDeploy(platform?: string, environment: 'staging' | 'production' = 'staging'): Promise<void> {
    // 1. 构建
    const buildResult = platform 
      ? await this.build.buildPlatform(platform)
      : await this.build.buildAllPlatforms();

    if (Array.isArray(buildResult)) {
      const successful = buildResult.filter(r => r.success);
      if (successful.length === 0) {
        throw new Error('构建失败');
      }
    } else if (!buildResult.success) {
      throw new Error(`${platform} 构建失败: ${buildResult.error}`);
    }

    // 2. 部署
    const deployResults = await this.deploy.deployToEnvironment(environment, platform);
    const successful = deployResults.filter((r: DeployResult) => r.success);

    if (successful.length > 0) {
      console.log(`🚀 快速部署完成！`);
    } else {
      throw new Error('部署失败');
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    config: boolean;
    git: boolean;
    build: boolean;
    deploy: boolean;
  }> {
    const health = {
      config: false,
      git: false,
      build: false,
      deploy: false
    };

    try {
      // 检查配置
      await this.config.loadConfig();
      health.config = true;
    } catch {}

    try {
      // 检查 Git
      await this.version.getCurrentVersion();
      health.git = true;
    } catch {}

    try {
      // 检查构建环境
      const stats = await this.build.getBuildStats();
      health.build = true;
    } catch {}

    try {
      // 检查部署配置
      const history = await this.deploy.getDeploymentHistory();
      health.deploy = true;
    } catch {}

    return health;
  }
}