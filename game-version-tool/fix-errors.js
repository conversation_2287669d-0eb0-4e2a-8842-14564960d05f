const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/modules/deploy/DeployManager.ts',
  'src/modules/recovery/RecoveryManager.ts',
  'src/modules/rollback/RollbackManager.ts',
  'src/utils/SecurityUtils.ts'
];

// 修复error.message的正则表达式
const errorMessageRegex = /error\.message/g;
const errorMessageReplacement = 'error instanceof Error ? error.message : String(error)';

// 修复其他错误类型的正则表达式
const otherErrorRegex = /(\w+Error)\.message/g;
const otherErrorReplacement = '$1 instanceof Error ? $1.message : String($1)';

filesToFix.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`修复文件: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 修复error.message
    content = content.replace(errorMessageRegex, errorMessageReplacement);
    
    // 修复其他错误类型
    content = content.replace(otherErrorRegex, otherErrorReplacement);
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✓ ${filePath} 修复完成`);
  } else {
    console.log(`✗ 文件不存在: ${filePath}`);
  }
});

console.log('所有文件修复完成！');
