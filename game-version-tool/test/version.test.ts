import * as fs from 'fs-extra';
import * as path from 'path';
import { VersionManager } from '../src/modules/version/VersionManager';
import { ConfigManager } from '../src/modules/config/ConfigManager';

// Mock simpleGit
jest.mock('simple-git', () => {
  return {
    simpleGit: jest.fn(() => ({
      addAnnotatedTag: jest.fn(),
      tags: jest.fn(() => ({ all: ['v0.1.0', 'v0.2.0', 'v1.0.0'] })),
      log: jest.fn(() => ({
        all: [
          { message: 'feat: add new feature' },
          { message: 'fix: fix critical bug' },
          { message: 'docs: update readme' }
        ]
      }))
    }))
  };
});

describe('VersionManager', () => {
  let testDir: string;
  let versionManager: VersionManager;
  let configManager: ConfigManager;

  beforeEach(async () => {
    testDir = await (global as any).createTestDir();
    versionManager = new VersionManager(testDir);
    configManager = new ConfigManager(testDir);

    // 创建测试配置
    await configManager.initConfig({
      project: {
        name: 'TestProject',
        type: 'cocos-creator',
        version: '0.1.0'
      }
    });
  });

  describe('getCurrentVersion', () => {
    it('should get version from config file', async () => {
      const version = await versionManager.getCurrentVersion();
      expect(version).toBe('0.1.0');
    });

    it('should get version from package.json if config not exists', async () => {
      // 删除配置文件
      const configPath = path.join(testDir, 'game-version.config.json');
      await fs.remove(configPath);

      // 创建 package.json
      const packageJson = {
        name: 'test-project',
        version: '1.2.3'
      };
      await fs.writeJSON(path.join(testDir, 'package.json'), packageJson);

      const version = await versionManager.getCurrentVersion();
      expect(version).toBe('1.2.3');
    });

    it('should return default version if no config or package.json', async () => {
      // 删除配置文件
      const configPath = path.join(testDir, 'game-version.config.json');
      await fs.remove(configPath);

      const version = await versionManager.getCurrentVersion();
      expect(version).toBe('0.1.0');
    });
  });

  describe('getNextVersion', () => {
    it('should calculate next patch version', async () => {
      const versionInfo = await versionManager.getNextVersion('patch');
      
      expect(versionInfo.current).toBe('0.1.0');
      expect(versionInfo.next).toBe('0.1.1');
      expect(versionInfo.type).toBe('patch');
    });

    it('should calculate next minor version', async () => {
      const versionInfo = await versionManager.getNextVersion('minor');
      
      expect(versionInfo.current).toBe('0.1.0');
      expect(versionInfo.next).toBe('0.2.0');
      expect(versionInfo.type).toBe('minor');
    });

    it('should calculate next major version', async () => {
      const versionInfo = await versionManager.getNextVersion('major');
      
      expect(versionInfo.current).toBe('0.1.0');
      expect(versionInfo.next).toBe('1.0.0');
      expect(versionInfo.type).toBe('major');
    });

    it('should calculate prerelease version', async () => {
      const versionInfo = await versionManager.getNextVersion('prerelease', 'alpha');
      
      expect(versionInfo.current).toBe('0.1.0');
      expect(versionInfo.next).toBe('0.1.1-alpha.0');
      expect(versionInfo.type).toBe('prerelease');
      expect(versionInfo.prerelease).toBe('alpha');
    });
  });

  describe('bumpVersion', () => {
    it('should bump version and update config', async () => {
      const versionInfo = await versionManager.bumpVersion('patch');
      
      expect(versionInfo.current).toBe('0.1.0');
      expect(versionInfo.next).toBe('0.1.1');

      // 验证配置文件已更新
      const config = await configManager.loadConfig();
      expect(config.project.version).toBe('0.1.1');
    });

    it('should update package.json if exists', async () => {
      // 创建 package.json
      const packageJson = {
        name: 'test-project',
        version: '0.1.0'
      };
      const packagePath = path.join(testDir, 'package.json');
      await fs.writeJSON(packagePath, packageJson);

      await versionManager.bumpVersion('minor');

      // 验证 package.json 已更新
      const updatedPackage = await fs.readJSON(packagePath);
      expect(updatedPackage.version).toBe('0.2.0');
    });

    it('should update web/package.json if exists', async () => {
      // 创建 web/package.json
      const webPackageJson = {
        name: 'web-project',
        version: '0.1.0'
      };
      const webDir = path.join(testDir, 'web');
      await fs.ensureDir(webDir);
      const webPackagePath = path.join(webDir, 'package.json');
      await fs.writeJSON(webPackagePath, webPackageJson);

      await versionManager.bumpVersion('major');

      // 验证 web/package.json 已更新
      const updatedWebPackage = await fs.readJSON(webPackagePath);
      expect(updatedWebPackage.version).toBe('1.0.0');
    });
  });

  describe('generateChangelog', () => {
    it('should generate changelog from git commits', async () => {
      const changelog = await versionManager.generateChangelog();
      
      expect(changelog.version).toBe('0.1.0');
      expect(changelog.date).toBeDefined();
      expect(changelog.changes.added).toContain('feat: add new feature');
      expect(changelog.changes.fixed).toContain('fix: fix critical bug');
      expect(changelog.changes.changed).toContain('docs: update readme');
    });

    it('should create CHANGELOG.md file', async () => {
      await versionManager.generateChangelog();
      
      const changelogPath = path.join(testDir, 'CHANGELOG.md');
      expect(await fs.pathExists(changelogPath)).toBe(true);
      
      const content = await fs.readFile(changelogPath, 'utf-8');
      expect(content).toContain('# 变更日志');
      expect(content).toContain('## [0.1.0]');
    });
  });

  describe('createTag', () => {
    it('should create git tag', async () => {
      const mockGit = require('simple-git').simpleGit();
      
      await versionManager.createTag('0.1.0', 'Release version 0.1.0');
      
      expect(mockGit.addAnnotatedTag).toHaveBeenCalledWith(
        'v0.1.0',
        'Release version 0.1.0'
      );
    });
  });

  describe('getAvailableVersions', () => {
    it('should return list of available versions from git tags', async () => {
      const versions = await versionManager.getAvailableVersions();
      
      expect(versions).toEqual(['0.1.0', '0.2.0', '1.0.0']);
    });

    it('should return empty array if no tags exist', async () => {
      // Mock empty tags
      const mockGit = require('simple-git').simpleGit();
      mockGit.tags.mockReturnValueOnce({ all: [] });
      
      const versions = await versionManager.getAvailableVersions();
      expect(versions).toEqual([]);
    });
  });
});