import * as fs from 'fs-extra';
import * as path from 'path';
import { ConfigManager } from '../src/modules/config/ConfigManager';
import { GameVersionConfig } from '../src/types/config';

describe('ConfigManager', () => {
  let testDir: string;
  let configManager: ConfigManager;

  beforeEach(async () => {
    testDir = await (global as any).createTestDir();
    configManager = new ConfigManager(testDir);
  });

  describe('initConfig', () => {
    it('should create default config file', async () => {
      await configManager.initConfig();
      
      const configPath = path.join(testDir, 'game-version.config.json');
      expect(await fs.pathExists(configPath)).toBe(true);
      
      const config = await fs.readJSON(configPath);
      expect(config.project.name).toBe('LuckyCoin');
      expect(config.project.type).toBe('cocos-creator');
      expect(config.build.platforms).toContain('web-mobile');
    });

    it('should create config with custom options', async () => {
      const customOptions: Partial<GameVersionConfig> = {
        project: {
          name: 'TestGame',
          type: 'cocos-creator',
          version: '1.0.0',
          description: 'Test game project'
        }
      };

      await configManager.initConfig(customOptions);
      
      const config = await configManager.loadConfig();
      expect(config.project.name).toBe('TestGame');
      expect(config.project.version).toBe('1.0.0');
      expect(config.project.description).toBe('Test game project');
    });
  });

  describe('loadConfig', () => {
    it('should load existing config file', async () => {
      const testConfig: GameVersionConfig = {
        project: {
          name: 'TestProject',
          type: 'cocos-creator',
          version: '0.2.0'
        },
        build: {
          platforms: ['web-mobile', 'android'],
          outputDir: './build',
          optimization: {
            compress: false,
            minify: false,
            sourcemap: true
          }
        },
        deploy: {},
        environments: {
          dev: './config/dev'
        },
        git: {
          autoTag: false,
          tagPrefix: 'release-',
          generateChangelog: false,
          changelogPath: './CHANGES.md'
        }
      };

      const configPath = path.join(testDir, 'game-version.config.json');
      await fs.writeJSON(configPath, testConfig);

      const loadedConfig = await configManager.loadConfig();
      expect(loadedConfig.project.name).toBe('TestProject');
      expect(loadedConfig.build.platforms).toEqual(['web-mobile', 'android']);
      expect(loadedConfig.build.optimization.compress).toBe(false);
    });

    it('should throw error if config file does not exist', async () => {
      await expect(configManager.loadConfig()).rejects.toThrow('配置文件不存在');
    });

    it('should validate config file against schema', async () => {
      const invalidConfig = {
        project: {
          name: '',  // 无效：名称不能为空
          type: 'invalid-type',  // 无效：不支持的类型
          version: 'invalid-version'  // 无效：版本格式错误
        }
      };

      const configPath = path.join(testDir, 'game-version.config.json');
      await fs.writeJSON(configPath, invalidConfig);

      await expect(configManager.loadConfig()).rejects.toThrow('配置文件验证失败');
    });
  });

  describe('updateConfig', () => {
    beforeEach(async () => {
      await configManager.initConfig();
    });

    it('should update config properties', async () => {
      const updates: Partial<GameVersionConfig> = {
        project: {
          name: 'UpdatedName',
          type: 'cocos-creator',
          version: '0.2.0',
          description: 'Updated description'
        }
      };

      await configManager.updateConfig(updates);
      
      const config = await configManager.loadConfig();
      expect(config.project.name).toBe('UpdatedName');
      expect(config.project.version).toBe('0.2.0');
      expect(config.project.description).toBe('Updated description');
    });

    it('should validate updated config', async () => {
      const invalidUpdates: Partial<GameVersionConfig> = {
        project: {
          name: '',
          type: 'cocos-creator',
          version: 'invalid'
        }
      };

      await expect(configManager.updateConfig(invalidUpdates))
        .rejects.toThrow('配置更新验证失败');
    });
  });

  describe('configExists', () => {
    it('should return true if config file exists', async () => {
      await configManager.initConfig();
      expect(await configManager.configExists()).toBe(true);
    });

    it('should return false if config file does not exist', async () => {
      expect(await configManager.configExists()).toBe(false);
    });
  });

  describe('environment config', () => {
    beforeEach(async () => {
      await configManager.initConfig();
    });

    it('should get environment config path', async () => {
      const config = await configManager.loadConfig();
      const devPath = configManager.getEnvironmentConfigPath('dev');
      
      expect(devPath).toBe(path.resolve(config.environments.dev));
    });

    it('should throw error for undefined environment', async () => {
      expect(() => configManager.getEnvironmentConfigPath('nonexistent'))
        .toThrow('环境 nonexistent 的配置路径未定义');
    });

    it('should validate environment config', async () => {
      // 创建环境配置目录
      const envDir = path.join(testDir, 'config', 'dev');
      await fs.ensureDir(envDir);

      const isValid = await configManager.validateEnvironmentConfig('dev');
      expect(isValid).toBe(true);

      const isInvalid = await configManager.validateEnvironmentConfig('nonexistent');
      expect(isInvalid).toBe(false);
    });
  });
});