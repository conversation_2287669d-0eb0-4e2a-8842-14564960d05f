#!/usr/bin/env node

/**
 * 依赖更新和兼容性修复脚本
 * 解决 ESM/CommonJS 兼容性问题
 */

console.log('🔧 开始修复依赖兼容性问题...');

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

try {
  // 1. 删除 node_modules 和 package-lock.json
  console.log('📦 清理现有依赖...');
  
  if (fs.existsSync('node_modules')) {
    execSync('rm -rf node_modules', { stdio: 'inherit' });
    console.log('✅ node_modules 已删除');
  }
  
  if (fs.existsSync('package-lock.json')) {
    fs.unlinkSync('package-lock.json');
    console.log('✅ package-lock.json 已删除');
  }

  // 2. 重新安装依赖
  console.log('📥 安装兼容版本的依赖...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ 依赖安装完成');

  // 3. 重新构建项目
  console.log('🔨 重新构建项目...');
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ 项目构建完成');

  console.log('\n🎉 依赖兼容性问题修复完成！');
  console.log('\n📋 版本信息:');
  console.log('- ora: 5.4.1 (支持 CommonJS)');
  console.log('- execa: 5.1.1 (支持 CommonJS)');
  console.log('- 其他依赖保持最新兼容版本');
  
  console.log('\n🚀 现在可以运行命令了:');
  console.log('node dist/cli.js config init');

} catch (error) {
  console.error('❌ 修复过程中出错:', error.message);
  console.log('\n📝 手动修复步骤:');
  console.log('1. rm -rf node_modules package-lock.json');
  console.log('2. npm install');
  console.log('3. npm run build');
  process.exit(1);
}