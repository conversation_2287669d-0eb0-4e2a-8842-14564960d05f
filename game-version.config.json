{"project": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "cocos-creator", "version": "0.1.4-beta.0", "description": "LuckyCoin game project"}, "build": {"platforms": ["web-mobile"], "outputDir": "./dist", "cocosCreator": {"projectPath": "D:\\Desktop\\LuckyCoin"}, "optimization": {"compress": true, "minify": true, "sourcemap": false}, "excludeFiles": ["*.log", "node_modules/**"]}, "deploy": {"web": {"staging": "https://staging.example.com", "production": "https://production.example.com"}}, "environments": {"dev": "./config/dev", "test": "./config/test", "prod": "./config/prod"}, "git": {"autoTag": true, "tagPrefix": "v", "generateChangelog": true, "changelogPath": "./CHANGELOG.md"}, "notification": {"enabled": false}}