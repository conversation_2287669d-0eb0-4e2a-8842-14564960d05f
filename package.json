{"name": "locky coins", "version": "0.1.0", "description": "locky coins", "main": "index.js", "scripts": {"build": "cocos-creator build -p web-mobile", "dev": "cocos-creator --project .", "version:bump": "cd game-version-tool && npm run build && node dist/cli.js version bump", "version:bump:patch": "cd game-version-tool && npm run build && node dist/cli.js version bump patch --tag --changelog", "version:bump:minor": "cd game-version-tool && npm run build && node dist/cli.js version bump minor --tag --changelog", "version:bump:major": "cd game-version-tool && npm run build && node dist/cli.js version bump major --tag --changelog", "build:all": "cd game-version-tool && npm run build && node dist/cli.js build all", "build:web": "cd game-version-tool && npm run build && node dist/cli.js build web", "deploy:staging": "cd game-version-tool && npm run build && node dist/cli.js deploy staging", "deploy:production": "cd game-version-tool && npm run build && node dist/cli.js deploy production", "game-config": "cd game-version-tool && npm run build && node dist/cli.js config", "game-config:init": "cd game-version-tool && npm run build && node dist/cli.js config init", "game-config:show": "cd game-version-tool && npm run build && node dist/cli.js config show", "game-config:validate": "cd game-version-tool && npm run build && node dist/cli.js config validate", "game-version:current": "cd game-version-tool && npm run build && node dist/cli.js version current", "game-version:list": "cd game-version-tool && npm run build && node dist/cli.js version list", "release:patch": "npm run version:bump:patch && npm run build:all && npm run deploy:staging", "release:minor": "npm run version:bump:minor && npm run build:all && npm run deploy:staging", "release:major": "npm run version:bump:major && npm run build:all && npm run deploy:production"}, "keywords": ["cocos-creator", "coins", "casino", "slot"], "author": "", "license": "MIT", "dependencies": {"fs-extra": "^11.3.0", "plist": "^3.1.0", "sharp": "^0.34.2"}, "devDependencies": {"./game-version-tool": "file:./game-version-tool"}, "creator": {"version": "3.8.6"}, "uuid": "be838956-5c2a-4ee1-8b04-b41dbb110665"}