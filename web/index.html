<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="宇雷科技 - 专注精品小游戏开发的科技团队，创造极致游戏体验" />
    <meta name="keywords" content="宇雷科技,游戏开发,小游戏,LuckyCoin,幸运硬币" />
    <title>宇雷科技 - 创造极致游戏体验</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/images/logo/logo1.png" as="image">
    
    <style>
      /* Critical CSS for loading */
      body {
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        overflow-x: hidden;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-logo {
        height: 80px;
        width: auto;
        object-fit: contain;
        animation: pulse 2s ease-in-out infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.8; }
        50% { transform: scale(1.1); opacity: 1; }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <img src="/images/logo/logo1.png" alt="宇雷科技" class="loading-logo">
    </div>
    
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    
    <script>
      // Hide loading screen when page is loaded
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
