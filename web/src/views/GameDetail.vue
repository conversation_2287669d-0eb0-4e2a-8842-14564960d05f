<template>
  <div class="min-h-screen pt-20 pb-20 pixel-bg relative">
    <!-- Pixel Decorations -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <!-- Floating Pixels -->
      <div class="absolute top-1/4 left-1/4 w-4 h-4 bg-green-400 opacity-60 animate-pulse pixel-square"></div>
      <div class="absolute top-1/3 right-1/4 w-3 h-3 bg-cyan-400 opacity-40 animate-pulse pixel-square" style="animation-delay: 1s;"></div>
      <div class="absolute bottom-1/3 left-1/3 w-2 h-2 bg-yellow-400 opacity-50 animate-pulse pixel-square" style="animation-delay: 2s;"></div>
      <div class="absolute bottom-1/4 right-1/3 w-3 h-3 bg-magenta-400 opacity-30 animate-pulse pixel-square" style="animation-delay: 3s;"></div>

      <!-- Corner Decorations -->
      <div class="absolute top-4 left-4 w-8 h-8 border-2 border-green-400 opacity-30 pixel-corner"></div>
      <div class="absolute top-4 right-4 w-8 h-8 border-2 border-cyan-400 opacity-30 pixel-corner"></div>
      <div class="absolute bottom-4 left-4 w-8 h-8 border-2 border-yellow-400 opacity-30 pixel-corner"></div>
      <div class="absolute bottom-4 right-4 w-8 h-8 border-2 border-magenta-400 opacity-30 pixel-corner"></div>
    </div>

    <!-- Header -->
    <section class="max-w-6xl mx-auto px-6 py-16 text-center">
      <div class="pixel-border bg-gradient-to-r from-green-400 to-blue-400 p-1 rounded-lg inline-block mb-4">
        <div class="bg-black px-4 py-2 rounded">
          <span class="pixel-text text-green-400">GAME LOADED</span>
        </div>
      </div>
      <h1 class="text-5xl md:text-6xl font-bold pixel-glow mb-4 tracking-wider">
        <span class="text-green-400">挣它一个</span><span class="text-yellow-400">亿</span>
      </h1>
      <p class="text-xl text-green-300 mb-8 font-pixel">
        幸运硬币 - 像素风放置经营游戏
      </p>
      <div class="flex justify-center space-x-4 text-sm font-pixel">
        <span class="pixel-badge bg-red-500">ACTION</span>
        <span class="pixel-badge bg-blue-500">STRATEGY</span>
        <span class="pixel-badge bg-purple-500">RETRO</span>
      </div>
    </section>

    <!-- Main Content -->
    <section class="max-w-6xl mx-auto px-6 mb-16">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Game Preview Carousel -->
        <div class="pixel-container rounded-lg overflow-hidden border-4 border-green-400 bg-black">
          <div class="relative">
            <!-- Image Container -->
            <div class="aspect-[764/1365] relative overflow-hidden">
              <div
                ref="carouselContainer"
                class="flex transition-transform duration-500 ease-in-out h-full"
                :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
              >
                <div
                  v-for="(screenshot, index) in screenshots"
                  :key="index"
                  class="w-full h-full flex-shrink-0"
                >
                  <img
                    :src="screenshot.src"
                    :alt="screenshot.alt"
                    class="w-full h-full object-cover"
                  >
                </div>
              </div>
            </div>

            <!-- Navigation Arrows -->
            <button
              @click="previousImage"
              class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-green-500 border-2 border-green-300 rounded flex items-center justify-center hover:bg-green-400 transition-all duration-300 group pixel-button"
            >
              <span class="text-black font-bold text-xl group-hover:scale-110 transition-transform duration-300">‹</span>
            </button>

            <button
              @click="nextImage"
              class="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-green-500 border-2 border-green-300 rounded flex items-center justify-center hover:bg-green-400 transition-all duration-300 group pixel-button"
            >
              <span class="text-black font-bold text-xl group-hover:scale-110 transition-transform duration-300">›</span>
            </button>

            <!-- Dots Indicator -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              <button
                v-for="(_, index) in screenshots"
                :key="index"
                @click="goToSlide(index)"
                class="w-4 h-4 border-2 transition-all duration-300 pixel-dot"
                :class="{
                  'bg-yellow-400 border-yellow-300 scale-125': index === currentIndex,
                  'bg-gray-600 border-gray-500 hover:bg-gray-500': index !== currentIndex
                }"
              />
            </div>

            <!-- Image Counter -->
            <div class="absolute top-4 right-4 bg-blue-500 border-2 border-blue-300 px-3 py-1 rounded text-white text-sm font-pixel font-bold">
              {{ currentIndex + 1 }}/{{ screenshots.length }}
            </div>
          </div>
        </div>

        <!-- Game Info -->
        <div class="space-y-8">
          <!-- Tags -->
          <div class="flex flex-wrap gap-3">
            <span class="pixel-badge bg-cyan-500 border-2 border-cyan-300">CASUAL</span>
            <span class="pixel-badge bg-magenta-500 border-2 border-magenta-300">IDLE</span>
            <span class="pixel-badge bg-yellow-500 border-2 border-yellow-300">STRATEGY</span>
          </div>

          <!-- Description -->
          <div class="pixel-container bg-black border-2 border-green-400 p-6 rounded">
            <h3 class="text-2xl font-pixel font-bold text-green-400 mb-4 pixel-glow">GAME FEATURES</h3>
            <p class="text-green-300 leading-relaxed mb-6 font-pixel">
              经典像素风格的硬币翻转游戏。体验复古游戏的魅力，享受简单而上瘾的游戏机制。
            </p>

            <ul class="space-y-3 text-green-300 font-pixel">
              <li class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-cyan-400 border border-cyan-300"></div>
                <span>► 像素风硬币翻转动画和物理效果</span>
              </li>
              <li class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-yellow-400 border border-yellow-300"></div>
                <span>► 自主工作的雇员系统，实现自动化收益</span>
              </li>
              <li class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-magenta-400 border border-magenta-300"></div>
                <span>► 多样化的视觉场景体验</span>
              </li>
            </ul>
          </div>

          <!-- Stats -->
          <div class="grid grid-cols-3 gap-4">
            <div class="pixel-stat-card bg-blue-500 border-2 border-blue-300">
              <div class="text-2xl font-pixel font-bold text-white mb-1">4.8</div>
              <div class="text-xs text-blue-100 font-pixel">RATING</div>
            </div>
            <div class="pixel-stat-card bg-purple-500 border-2 border-purple-300">
              <div class="text-2xl font-pixel font-bold text-white mb-1">12MIN</div>
              <div class="text-xs text-purple-100 font-pixel">PLAYTIME</div>
            </div>
            <div class="pixel-stat-card bg-red-500 border-2 border-red-300">
              <div class="text-2xl font-pixel font-bold text-white mb-1">35%</div>
              <div class="text-xs text-red-100 font-pixel">SHARE</div>
            </div>
          </div>

          <!-- CTA -->
          <div class="flex gap-4">
            <button class="flex-1 px-6 py-3 bg-green-500 hover:bg-green-400 text-black border-2 border-green-300 rounded font-bold font-pixel transition-all duration-300 pixel-button hover:scale-105">
              ► PLAY NOW
            </button>
            <button class="flex-1 px-6 py-3 bg-yellow-500 hover:bg-yellow-400 text-black border-2 border-yellow-300 rounded font-bold font-pixel transition-all duration-300 pixel-button hover:scale-105">
              ♦ SHARE
            </button>
          </div>
        </div>
      </div>
    </section>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentIndex = ref(0)
const carouselContainer = ref<HTMLElement>()
let autoPlayInterval: ReturnType<typeof setInterval>

const screenshots = [
  { src: '/images/shotscreen/screen_ (1).jpg', alt: 'LuckyCoin游戏截图1' },
  { src: '/images/shotscreen/screen_ (2).jpg', alt: 'LuckyCoin游戏截图2' },
  { src: '/images/shotscreen/screen_ (3).jpg', alt: 'LuckyCoin游戏截图3' },
  { src: '/images/shotscreen/screen_ (4).jpg', alt: 'LuckyCoin游戏截图4' },
  { src: '/images/shotscreen/screen_ (5).jpg', alt: 'LuckyCoin游戏截图5' }
]

const nextImage = () => {
  currentIndex.value = (currentIndex.value + 1) % screenshots.length
}

const previousImage = () => {
  currentIndex.value = currentIndex.value === 0 ? screenshots.length - 1 : currentIndex.value - 1
}

const goToSlide = (index: number) => {
  currentIndex.value = index
}

const startAutoPlay = () => {
  autoPlayInterval = setInterval(() => {
    nextImage()
  }, 4000) // 4秒自动切换
}

const stopAutoPlay = () => {
  if (autoPlayInterval) {
    clearInterval(autoPlayInterval)
  }
}

onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>

<style scoped>
/* 导入像素字体 */
@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

/* 像素字体类 */
.font-pixel {
  font-family: 'Press Start 2P', 'Courier New', monospace;
  font-size: 0.9em; /* 稍微缩小以提高可读性 */
  line-height: 1.4;
  letter-spacing: 0.05em;
}

/* 像素风格背景 */
.pixel-bg {
  background-color: #0a0a0f;
  background-image:
    /* 大网格 */
    linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px),
    /* 小点阵 */
    radial-gradient(circle at 50% 50%, rgba(0, 255, 255, 0.2) 1px, transparent 1px),
    /* 扫描线 */
    linear-gradient(0deg, transparent 50%, rgba(0, 255, 0, 0.02) 50%);
  background-size:
    40px 40px,
    40px 40px,
    8px 8px,
    100% 2px;
  background-position:
    0 0,
    0 0,
    0 0,
    0 0;
  min-height: 100vh;
  position: relative;
}

/* 像素发光效果 */
.pixel-glow {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

/* 像素边框 */
.pixel-border {
  box-shadow:
    inset 0 0 0 2px currentColor,
    0 0 10px rgba(0, 255, 0, 0.3);
}

/* 像素容器 */
.pixel-container {
  box-shadow:
    0 0 20px rgba(0, 255, 0, 0.2),
    inset 0 0 20px rgba(0, 0, 0, 0.5);
}

/* 像素徽章 */
.pixel-badge {
  @apply px-3 py-1 text-black text-xs rounded border-2;
  font-family: 'Press Start 2P', 'Courier New', monospace;
  font-size: 0.6em;
  box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.5);
  text-shadow: none;
}

/* 像素按钮 */
.pixel-button {
  box-shadow:
    2px 2px 0px rgba(0, 0, 0, 0.5),
    0 0 10px rgba(0, 255, 0, 0.3);
  text-shadow: none;
}

.pixel-button:hover {
  box-shadow:
    1px 1px 0px rgba(0, 0, 0, 0.5),
    0 0 15px rgba(0, 255, 0, 0.5);
}

/* 像素圆点 */
.pixel-dot {
  box-shadow: 1px 1px 0px rgba(0, 0, 0, 0.5);
}

/* 像素统计卡片 */
.pixel-stat-card {
  @apply text-center p-4 rounded;
  box-shadow:
    2px 2px 0px rgba(0, 0, 0, 0.5),
    0 0 10px rgba(255, 255, 255, 0.1);
}

/* 像素文字 */
.pixel-text {
  font-family: 'Press Start 2P', 'Courier New', monospace;
  font-weight: normal; /* Press Start 2P 已经是粗体 */
  letter-spacing: 1px;
  font-size: 0.8em;
}

/* 8位风格动画 */
@keyframes pixelPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.pixel-glow {
  animation: pixelPulse 2s ease-in-out infinite;
}

/* 扫描线效果 */
.pixel-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    transparent 50%,
    rgba(0, 255, 0, 0.03) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 1;
}

/* 自定义颜色 */
.bg-magenta-500 { background-color: #ff00ff; }
.border-magenta-300 { border-color: #ff99ff; }
.bg-cyan-500 { background-color: #00ffff; }
.border-cyan-300 { border-color: #99ffff; }

/* 像素装饰元素 */
.pixel-square {
  box-shadow:
    0 0 10px currentColor,
    inset 0 0 5px rgba(255, 255, 255, 0.2);
}

.pixel-corner {
  background: linear-gradient(45deg, transparent 30%, currentColor 30%, currentColor 70%, transparent 70%);
  animation: pixelRotate 4s linear infinite;
}

@keyframes pixelRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 增强背景动画 */
.pixel-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(90deg, transparent 98%, rgba(0, 255, 0, 0.1) 100%),
    linear-gradient(0deg, transparent 98%, rgba(0, 255, 0, 0.1) 100%);
  background-size: 20px 20px;
  animation: pixelMove 10s linear infinite;
  pointer-events: none;
}

@keyframes pixelMove {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

/* 确保背景覆盖整个页面 */
.pixel-bg {
  background-attachment: fixed;
}
</style>
