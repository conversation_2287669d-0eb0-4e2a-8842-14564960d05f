<template>
  <div class="min-h-screen pt-20">
    <!-- Header -->
    <section class="max-w-6xl mx-auto px-6 py-16 text-center">
      <h1 class="text-5xl md:text-6xl font-display font-bold text-gradient mb-4">
        挣它一个亿
      </h1>
      <p class="text-xl text-gray-400 mb-8">
        幸运硬币 - 创新放置点击游戏
      </p>
    </section>

    <!-- Main Content -->
    <section class="max-w-6xl mx-auto px-6 mb-16">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Game Preview Carousel -->
        <div class="glass rounded-2xl overflow-hidden">
          <div class="relative">
            <!-- Image Container -->
            <div class="aspect-[764/1365] relative overflow-hidden">
              <div
                ref="carouselContainer"
                class="flex transition-transform duration-500 ease-in-out h-full"
                :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
              >
                <div
                  v-for="(screenshot, index) in screenshots"
                  :key="index"
                  class="w-full h-full flex-shrink-0"
                >
                  <img
                    :src="screenshot.src"
                    :alt="screenshot.alt"
                    class="w-full h-full object-cover"
                  >
                </div>
              </div>
            </div>

            <!-- Navigation Arrows -->
            <button
              @click="previousImage"
              class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-black/70 transition-all duration-300 group"
            >
              <svg class="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
              </svg>
            </button>

            <button
              @click="nextImage"
              class="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-black/70 transition-all duration-300 group"
            >
              <svg class="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </button>

            <!-- Dots Indicator -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              <button
                v-for="(_, index) in screenshots"
                :key="index"
                @click="goToSlide(index)"
                class="w-3 h-3 rounded-full transition-all duration-300"
                :class="{
                  'bg-white scale-125': index === currentIndex,
                  'bg-white/50 hover:bg-white/75': index !== currentIndex
                }"
              />
            </div>

            <!-- Image Counter -->
            <div class="absolute top-4 right-4 bg-black/50 backdrop-blur-sm px-3 py-1 rounded-full text-white text-sm">
              {{ currentIndex + 1 }} / {{ screenshots.length }}
            </div>
          </div>
        </div>

        <!-- Game Info -->
        <div class="space-y-8">
          <!-- Tags -->
          <div class="flex flex-wrap gap-3">
            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">休闲益智</span>
            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">放置点击</span>
            <span class="px-3 py-1 bg-pink-500/20 text-pink-300 rounded-full text-sm">策略经营</span>
          </div>

          <!-- Description -->
          <div>
            <h3 class="text-2xl font-display font-bold text-white mb-4">游戏特色</h3>
            <p class="text-gray-400 leading-relaxed mb-6">
              创新的物理化硬币翻转机制，智能AI雇员系统，多层次升级策略。体验前所未有的放置类游戏乐趣，每一次翻转都充满惊喜。
            </p>

            <ul class="space-y-3 text-gray-400">
              <li class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>物理化硬币翻转动画</span>
              </li>
              <li class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span>智能AI雇员系统</span>
              </li>
              <li class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-pink-400 rounded-full"></div>
                <span>技能冷却机制</span>
              </li>
            </ul>
          </div>

          <!-- Stats -->
          <div class="grid grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-3xl font-display font-bold text-white mb-1">4.8</div>
              <div class="text-sm text-gray-500">用户评分</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-display font-bold text-white mb-1">12分钟</div>
              <div class="text-sm text-gray-500">平均时长</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-display font-bold text-white mb-1">35%</div>
              <div class="text-sm text-gray-500">分享率</div>
            </div>
          </div>

          <!-- CTA -->
          <div class="flex gap-4">
            <button class="flex-1 px-6 py-3 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-semibold transition-colors duration-300">
              立即体验
            </button>
            <button class="flex-1 px-6 py-3 glass text-white rounded-lg font-semibold transition-colors duration-300">
              分享游戏
            </button>
          </div>
        </div>
      </div>
    </section>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentIndex = ref(0)
const carouselContainer = ref<HTMLElement>()
let autoPlayInterval: number

const screenshots = [
  { src: '/images/shotscreen/screen_ (1).jpg', alt: 'LuckyCoin游戏截图1' },
  { src: '/images/shotscreen/screen_ (2).jpg', alt: 'LuckyCoin游戏截图2' },
  { src: '/images/shotscreen/screen_ (3).jpg', alt: 'LuckyCoin游戏截图3' },
  { src: '/images/shotscreen/screen_ (4).jpg', alt: 'LuckyCoin游戏截图4' },
  { src: '/images/shotscreen/screen_ (5).jpg', alt: 'LuckyCoin游戏截图5' }
]

const nextImage = () => {
  currentIndex.value = (currentIndex.value + 1) % screenshots.length
}

const previousImage = () => {
  currentIndex.value = currentIndex.value === 0 ? screenshots.length - 1 : currentIndex.value - 1
}

const goToSlide = (index: number) => {
  currentIndex.value = index
}

const startAutoPlay = () => {
  autoPlayInterval = setInterval(() => {
    nextImage()
  }, 4000) // 4秒自动切换
}

const stopAutoPlay = () => {
  if (autoPlayInterval) {
    clearInterval(autoPlayInterval)
  }
}

onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>
