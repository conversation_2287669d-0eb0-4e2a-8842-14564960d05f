<template>
  <nav class="fixed top-0 left-0 right-0 z-50 glass-dark border-b border-white/10">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <router-link to="/" class="flex items-center group">
          <img
            src="/images/logo/logo1.png"
            alt="宇雷科技"
            class="h-12 w-auto object-contain group-hover:scale-105 transition-transform duration-300"
          >
        </router-link>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            to="/"
            class="nav-link"
            active-class="nav-link-active"
          >
            首页
          </router-link>
<!--          <router-link
            to="/game/luckycoin"
            class="nav-link"
            active-class="nav-link-active"
          >
            产品
          </router-link>-->
          <router-link
            to="/about"
            class="nav-link"
            active-class="nav-link-active"
          >
            关于
          </router-link>
          <router-link
            to="/contact"
            class="px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
          >
            联系
          </router-link>
        </div>

        <!-- Mobile Menu Button -->
        <button 
          @click="mobileMenuOpen = !mobileMenuOpen"
          class="md:hidden p-2 text-gray-300 hover:text-white transition-colors duration-300"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>

      <!-- Mobile Menu -->
      <div 
        v-if="mobileMenuOpen"
        class="md:hidden mt-4 py-4 border-t border-white/10"
      >
        <div class="flex flex-col space-y-4">
          <router-link 
            to="/" 
            class="text-gray-300 hover:text-white transition-colors duration-300 font-medium"
            @click="mobileMenuOpen = false"
          >
            首页
          </router-link>
          <router-link 
            to="/game/luckycoin" 
            class="text-gray-300 hover:text-white transition-colors duration-300 font-medium"
            @click="mobileMenuOpen = false"
          >
            产品
          </router-link>
          <router-link 
            to="/about" 
            class="text-gray-300 hover:text-white transition-colors duration-300 font-medium"
            @click="mobileMenuOpen = false"
          >
            关于
          </router-link>
          <router-link 
            to="/contact" 
            class="text-gray-300 hover:text-white transition-colors duration-300 font-medium"
            @click="mobileMenuOpen = false"
          >
            联系
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const mobileMenuOpen = ref(false)
</script>

<style scoped>
.nav-link {
  @apply text-gray-400 hover:text-white transition-all duration-300 font-medium relative;
}

.nav-link::after {
  content: '';
  @apply absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 to-purple-400 transition-all duration-300;
}

.nav-link:hover::after {
  @apply w-full;
}

.nav-link-active {
  @apply text-white;
}

.nav-link-active::after {
  @apply w-full;
}
</style>
