<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>horse_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,278},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>horse_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,186},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>horse_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,186},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>horse_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,94},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>horse_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,94},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>horse_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,2},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>horse_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>employee-horse.png</string>
            <key>size</key>
            <string>{256,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:3289b28d4f10d3a7e1719b512356cfe4$</string>
            <key>textureFileName</key>
            <string>employee-horse.png</string>
        </dict>
    </dict>
</plist>
