<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.png</key>
            <dict>
                <key>frame</key>
                <string>{{874,648},{42,54}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{42,54}}</string>
                <key>sourceSize</key>
                <string>{42,54}</string>
            </dict>
            <key>2.png</key>
            <dict>
                <key>frame</key>
                <string>{{930,604},{42,54}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{42,54}}</string>
                <key>sourceSize</key>
                <string>{42,54}</string>
            </dict>
            <key>3.png</key>
            <dict>
                <key>frame</key>
                <string>{{874,604},{42,54}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{42,54}}</string>
                <key>sourceSize</key>
                <string>{42,54}</string>
            </dict>
            <key>BG.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{668,1045}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{668,1045}}</string>
                <key>sourceSize</key>
                <string>{668,1045}</string>
            </dict>
            <key>BG1.png</key>
            <dict>
                <key>frame</key>
                <string>{{672,702},{600,99}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{600,99}}</string>
                <key>sourceSize</key>
                <string>{600,99}</string>
            </dict>
            <key>BG2.png</key>
            <dict>
                <key>frame</key>
                <string>{{773,604},{600,99}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{600,99}}</string>
                <key>sourceSize</key>
                <string>{600,99}</string>
            </dict>
            <key>BG3.png</key>
            <dict>
                <key>frame</key>
                <string>{{889,2},{600,99}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{600,99}}</string>
                <key>sourceSize</key>
                <string>{600,99}</string>
            </dict>
            <key>BG4.png</key>
            <dict>
                <key>frame</key>
                <string>{{672,100},{600,99}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{600,99}}</string>
                <key>sourceSize</key>
                <string>{600,99}</string>
            </dict>
            <key>BG_ME.png</key>
            <dict>
                <key>frame</key>
                <string>{{788,2},{600,99}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{600,99}}</string>
                <key>sourceSize</key>
                <string>{600,99}</string>
            </dict>
            <key>badge.png</key>
            <dict>
                <key>frame</key>
                <string>{{672,2},{114,96}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{114,96}}</string>
                <key>sourceSize</key>
                <string>{114,96}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>rank.png</string>
            <key>size</key>
            <string>{1024,2048}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:6d741f8d62ad7527bd5e205a93a343c7$</string>
            <key>textureFileName</key>
            <string>rank.png</string>
        </dict>
    </dict>
</plist>
