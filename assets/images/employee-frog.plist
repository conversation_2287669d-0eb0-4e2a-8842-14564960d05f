<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>frog_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,278},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>frog_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,186},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>frog_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,186},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>frog_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,94},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>frog_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,94},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>frog_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,2},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
            <key>frog_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{90,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,120}}</string>
                <key>sourceSize</key>
                <string>{90,120}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>employee-frog.png</string>
            <key>size</key>
            <string>{256,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d9fdfe7e5d54f0184903fed1f34c3970$</string>
            <key>textureFileName</key>
            <string>employee-frog.png</string>
        </dict>
    </dict>
</plist>
