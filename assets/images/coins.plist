<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>copper_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{156,369},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>copper_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,369},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>copper_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,363},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>copper_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{156,292},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>copper_front.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,292},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>copper_shadow.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{57,45}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{57,45}}</string>
                <key>sourceSize</key>
                <string>{57,45}</string>
            </dict>
            <key>gold_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,286},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>gold_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{156,215},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>gold_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,215},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>gold_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,209},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>gold_front.png</key>
            <dict>
                <key>frame</key>
                <string>{{156,138},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>gold_shadow.png</key>
            <dict>
                <key>frame</key>
                <string>{{126,2},{69,57}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{69,57}}</string>
                <key>sourceSize</key>
                <string>{69,57}</string>
            </dict>
            <key>silver_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,138},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>silver_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,132},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>silver_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{156,61},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>silver_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{79,61},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>silver_front.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,55},{75,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,75}}</string>
                <key>sourceSize</key>
                <string>{75,75}</string>
            </dict>
            <key>silver_shadow.png</key>
            <dict>
                <key>frame</key>
                <string>{{61,2},{63,51}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{63,51}}</string>
                <key>sourceSize</key>
                <string>{63,51}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>coins.png</string>
            <key>size</key>
            <string>{233,446}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:3f0db087881fdfda5db295d66dd12f3a$</string>
            <key>textureFileName</key>
            <string>coins.png</string>
        </dict>
    </dict>
</plist>
