import { _decorator, But<PERSON>, Component, Label, Node, <PERSON>B<PERSON>, Slider, Sprite, Vec3 } from 'cc';
import { StatsContext } from '../contexts/StatsContext';
import { FloatingText } from './floating_text/FloatingText';
const { ccclass, property } = _decorator;

@ccclass('OfflineIncomeUI')
export class OfflineIncomeUI extends Component {

    @property(Label)
    labelOfflineIncome: Label = null;

    private offlineMoney: number = 0;


    setData(value: number) {
        this.offlineMoney = value;

        this.labelOfflineIncome.string = "$" + value.toLocaleString();

    }

    onGet() {
        this.node.active = false;

        // TODO: 调用FloatingText.create显示收益
        // 使用FloatingText显示收益
        const textPos = new Vec3(
            this.node.position.x,
            this.node.position.y + 50,
            this.node.position.z
        );
        FloatingText.create(this.node.parent, textPos, `+${this.offlineMoney.toLocaleString()}`);
        StatsContext.money += this.offlineMoney;

    }
}


