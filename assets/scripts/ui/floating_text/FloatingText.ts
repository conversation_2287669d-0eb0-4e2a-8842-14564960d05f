import { _decorator, Component, Node, Label, Vec3, tween, UIOpacity, resources, Prefab, instantiate, Color } from 'cc';
import { UIManager } from '../../managers/UIManager';

const { ccclass, property } = _decorator;

/**
 * 浮动文本组件 - 用于显示收益或提示文本
 */
@ccclass('FloatingText')
export class FloatingText extends Component {
    @property(Label)
    private label: Label | null = null;

    @property
    private floatDuration: number = 2.0;

    @property
    private floatDistance: number = 100;

    @property(UIOpacity)
    private uiOpacity: UIOpacity | null = null;

    private static prefab: Prefab | null = null;

    /**
     * 设置文本内容
     * @param text 要显示的文本
     */
    setText(text: string, color:string='#FDEA0C') {
        if (this.label) {
            this.label.string = text;
            this.label.color = new Color(color);
        }
    }

    /**
     * 设置数字内容
     * @param value 要显示的数值
     * @param prefix 前缀（例如+或-）
     */
    setNumber(value: number, prefix: string = '+') {
        let valueText = value.toFixed(0);

        if (this.label) {
            this.label.string = `${prefix}${valueText}`;
        }
    }

    /**
     * 开始浮动动画
     */
    startFloating() {
        const startPos = this.node.position.clone();
        const endPos = new Vec3(startPos.x, startPos.y + this.floatDistance, startPos.z);

        // 确保组件初始化完成
        if (!this.uiOpacity) {
            this.uiOpacity = this.getComponent(UIOpacity);
            if (!this.uiOpacity) {
                this.uiOpacity = this.addComponent(UIOpacity);
            }
        }

        // 重置透明度
        this.uiOpacity.opacity = 255;

        // 创建浮动和淡出动画
        tween(this.node)
            .to(this.floatDuration, { position: endPos }, { easing: 'cubicOut' })
            .start();

        tween(this.uiOpacity)
            .delay(this.floatDuration * 0.5)
            .to(this.floatDuration * 0.5, { opacity: 0 })
            .call(() => {
                // 动画完成后回收节点
                this.node.destroy();
            })
            .start();
    }

    /**
     * 创建并启动浮动文本
     * @param parent 父节点
     * @param position 显示位置
     * @param text 显示文本
     * @param duration 持续时间(可选)
     * @param distance 浮动距离(可选)
     */
    static create(parent: Node, position: Vec3, text: string, duration?: number, distance?: number, color:string='#FDEA0C'): FloatingText | null {
        // 加载预制体 (实际应用中应该使用资源管理器)
        if(!this.prefab)this.prefab = UIManager.instance.floatingTextPrefab;

        // 创建节点
        const node = instantiate(this.prefab);
        node.parent = parent;
        node.position = position;

        // 添加组件
        const floatingText = node.getComponent(FloatingText);

        // 设置属性
        floatingText.setText(text, color);
        if (duration !== undefined) floatingText.floatDuration = duration;
        if (distance !== undefined) floatingText.floatDistance = distance;

        // 开始动画
        floatingText.startFloating();

        return floatingText;
    }

    /**
     * 创建并启动浮动数字
     * @param parent 父节点
     * @param position 显示位置
     * @param value 显示数值
     * @param prefix 前缀(可选)
     * @param duration 持续时间(可选)
     * @param distance 浮动距离(可选)
     */
    static createNumber(parent: Node, position: Vec3, value: number, prefix: string = '+', duration?: number, distance?: number): FloatingText | null {
        // 加载预制体 (实际应用中应该使用资源管理器)
        const prefab = null; // resources.load('prefabs/FloatingText');
        if (!prefab) {
            console.error('无法加载浮动文本预制体');
            return null;
        }

        // 创建节点
        const node = new Node('FloatingText');
        node.parent = parent;
        node.position = position;

        // 添加组件
        const floatingText = node.addComponent(FloatingText);

        // 设置属性
        floatingText.setNumber(value, prefix);
        if (duration !== undefined) floatingText.floatDuration = duration;
        if (distance !== undefined) floatingText.floatDistance = distance;

        // 开始动画
        floatingText.startFloating();

        return floatingText;
    }
}