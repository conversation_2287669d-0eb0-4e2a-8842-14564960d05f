import { _decorator, Button, Component, Label, Node, ProgressBar, resources, Sprite, SpriteAtlas, UIComponent, Widget } from 'cc';
import { PurchasableItemConfig } from '../../contexts/ConfigContext';
import { PurchaseContext } from '../../contexts/PurchaseContext';
import { StatsContext } from '../../contexts/StatsContext';
import { GameEvent } from '../../event/GameEvent';
import { AudioManager } from '../../managers/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('ShopItemIntroduce')
export class ShopItemIntroduce extends Component {

    // 物品图片
    @property(Node)
    itemIcon: Node = null;

    // 物品名称
    @property(Label)
    labelName: Label = null;

    // 物品描述
    @property(Label)
    labelDes: Label = null;

    // 等级
    @property(Label)
    labelGrade: Label = null;
    // 等级当前值
    @property(Label)
    labelGradeCur: Label = null;
    // 等级下个值
    @property(Label)
    labelGradeNext: Label = null;

    // 升级
    @property(Label)
    labelUpGrade: Label = null;
    // 升级当前数值
    @property(Label)
    labelUpGradeCurValue: Label = null;
    // 升级下个数值
    @property(Label)
    labelUpGradeNextValue: Label = null;

    // CD
    @property(Label)
    labelCD: Label = null;
    // 升级当前数值
    @property(Label)
    labelCDCurValue: Label = null;
    // 升级下个数值
    @property(Label)
    labelCDNextValue: Label = null;

    // 关闭按钮
    @property(Button)
    buttonClose: Button = null;

    // 购买/升级按钮
    @property(Button)
    buttonOk: Button = null;

    // 金币图片
    @property(Node)
    moneyIcon: Node = null;

    // 金币数值
    @property(Label)
    moneyValue: Label = null;

    // 进度条
    @property(ProgressBar)
    framentProgressBar: ProgressBar = null;

    // 进度条数值
    @property(Label)
    labelFragment: Label = null;

    private idKey:string = "";
    private _can:boolean = false;

    setData(idKey: string, config: PurchasableItemConfig, canBuy:boolean){
        this.idKey = idKey;
        this._can = canBuy;
        this.loadSpriteFromAtlas("image/icons", config.spriteName);
        this.labelName.string = config.name;
        this.labelDes.string = config.desc;

        let curLevel = 0;
        let nextLevel = 0;
        if(config.category=="skill"){
            this.setLabelCD();

            curLevel = StatsContext.getSkill(this.idKey);
            nextLevel = Math.min(curLevel+1, config.max_purchase_amounts);

            const validity_time = config.skill.validity_time + config.skill.validity_time_multiplier*curLevel;
            const nextLevel_validity_time = config.skill.validity_time + config.skill.validity_time_multiplier*nextLevel;
            this.labelUpGradeCurValue.string = validity_time.toString()+"秒";
            this.labelUpGradeNextValue.string = nextLevel_validity_time.toString()+"秒";

            const cd_time = config.skill.cd - curLevel*config.skill.cd_addition;
            const next_cd_time = config.skill.cd - nextLevel*config.skill.cd_addition;
            this.labelCDCurValue.string = cd_time.toString() + "秒";
            this.labelCDNextValue.string = next_cd_time.toString() + "秒";

            //进度条
            const frament = StatsContext.getSkillFragment(this.idKey+"_fragment");
            const needFragment = config.price_expressions.base + curLevel*config.price_expressions.multiplier + config.price_expressions.flat_offset;
            this.framentProgressBar.progress = frament/needFragment;
            this.labelFragment.string = frament.toString()+" / "+needFragment.toString();
        }else{
            curLevel = PurchaseContext.getPurchaseAmount(this.idKey);
            nextLevel = Math.min(curLevel+1, config.max_purchase_amounts);
            this.labelUpGradeCurValue.string = (curLevel*config.increase_amounts).toString();
            this.labelUpGradeNextValue.string = (nextLevel*config.increase_amounts).toString();
            if(this.moneyValue)this.moneyValue.string = PurchaseContext.getItemPrice(this.idKey).toString();
        }
        
        this.setLabelGrade();
        this.setLabelUpgrade();

        this.labelGradeCur.string = curLevel.toString();
        this.labelGradeNext.string = nextLevel.toString();
        
        //是否达到上限
        if(curLevel >= config.max_purchase_amounts){
            this.buttonOk.node.active = false;
            if(this.labelFragment)this.labelFragment.string = "max";
        }else{
            this.buttonOk.node.active = true;
        }
        this.buttonOk.interactable = this._can;
    }
    setLabelGrade(){
        switch(this.idKey){
            case "small_coin":
                this.labelGrade.string  = "等级";
                break;
            default:
                this.labelGrade.string  = "等级";
                break;
        }
    }
    setLabelUpgrade(){
        switch(this.idKey.toLowerCase()){
            case "small_coin":
                this.labelUpGrade.string  = "升级铜币数";
                break;
                case "change_small_to_middle":
            this.labelUpGrade.string  = "升级银币数";
                break;
            case "change_middle_to_large":
                this.labelUpGrade.string  = "升级金币数";
                break;
            case "additional_coin_value_small":
            case "additional_coin_value_medium":
            case "additional_coin_value_large":
                this.labelUpGrade.string  = "额外获得$";
                break;
            case "skill_temp_value_add":
                this.labelUpGrade.string  = "增值有效时长";
                break;
            case "skill_immediate_production":
                this.labelUpGrade.string  = "立即生效";
                break;
            case "skill_high_efficient_work":
                this.labelUpGrade.string  = "高效工作时长";
                break;
            case "skill_emergency_mobilization":
                this.labelUpGrade.string  = "持续工作时长";
                break;
            default:
                this.labelUpGrade.string  = "";
                break;
        }
    }
    setLabelCD(){
        if(this.labelCD)this.labelCD.string  = "冷却时长";
    }
    async loadSpriteFromAtlas(atlasPath: string, spriteName: string) {
        try {
            // 加载图集
            const atlas = await new Promise<SpriteAtlas>((resolve, reject) => {
                resources.load(atlasPath, (err, atlas: SpriteAtlas) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(atlas);
                    }
                });
            });

            // 获取指定名称的 SpriteFrame
            const spriteFrame = atlas.getSpriteFrame(spriteName);
            if (spriteFrame) {
                // 获取 Sprite 组件并设置 SpriteFrame
                const spriteComponent = this.itemIcon.getComponent(Sprite);
                if (spriteComponent) {
                    spriteComponent.spriteFrame = spriteFrame;
                } else {
                    console.warn('物品图片节点上没有 Sprite 组件');
                }
            } else {
                console.warn(`图集中未找到名为 ${spriteName} 的 SpriteFrame`);
            }
        } catch (error) {
            console.error('加载图集失败:', error);
        }
    }

    onClose(){
        this.node.active = false;
        AudioManager.playSoundEffect('button_close');
    }
    onBuy(){
        this.onClose();
        StatsContext.events.emit(GameEvent.SHOP_ITEM_INTRODUCE_BUY, this.idKey);
    }
}


