import { _decorator, Button, Color, Component, Enum, Label, Node, resources, RichText, Sprite, SpriteAtlas, SpriteFrame, Texture2D } from 'cc';
import { ConfigContext, PURCHASABLE_ITEM_ID, PurchasableItemConfig, SHOP_ITEM_TYPE } from '../../contexts/ConfigContext';
import { StatsContext } from '../../contexts/StatsContext';
import { UIManager } from '../../managers/UIManager';
import { GameEvent } from '../../event/GameEvent';

const { ccclass, property } = _decorator;

@ccclass('SkillFragmentItem')
export class SkillFragmentItem extends Component {
    //背景
    @property(Sprite)
    spriteBg1: Sprite = null;
    @property(Sprite)
    spriteBg2: Sprite = null;
    @property(Sprite)
    spriteBg3: Sprite = null;
    @property(Sprite)
    spriteBg4: Sprite = null;

    // 物品图片
    @property(Node)
    itemIcon: Node = null;

    // 图标按钮
    @property(Button)
    buttonIcon: Button = null;

    // 名称
    @property(Label)
    labelName: Label = null;

    // 等级
    @property(Label)
    labelLevel: Label = null;

    // 数量
    @property(Label)
    labelAmount: Label = null;

    // 选择按钮
    @property(Button)
    buttonSelect: Button = null;

    // 事件回调
    onBought: Function = null;
    onFailedBuy: Function = null;

    // 配置
    private idKey: string = '';
    private config: PurchasableItemConfig = null;
    public fragmentCount: number = 1;

    start() {
        this.buttonSelect?.node.on(Button.EventType.CLICK, this.onButtonSelectPressed, this);
        this.buttonIcon?.node.on(Button.EventType.CLICK, this.onButtonIconPressed, this);
    }

    onButtonSelectPressed(){
        //选择
        const count = StatsContext.getSkillFragment(this.idKey+"_fragment") + this.fragmentCount;
        StatsContext.setSkillFragment(this.idKey+"_fragment", count);
        StatsContext.events.emit(GameEvent.SELECT_SKILL_FRAGMENT);
    }
    onButtonIconPressed(){
        const level = StatsContext.getSkill(this.idKey);
        const hasFragment = StatsContext.getSkillFragment(this.idKey+"_fragment");
        const needFragment = this.config.price_expressions.base + level*this.config.price_expressions.multiplier + this.config.price_expressions.flat_offset;
        let can = false;
        if(hasFragment>0 && hasFragment >= needFragment){
            can = true;
        }
        UIManager.instance.showShopItemSkillFragmentIntroduce(this.idKey, this.config, can);
    }

    setData(idKey: string){
        this.idKey = idKey;
        console.log(`[SkillFragmentItem] setData() > idKey: ${this.idKey}`);
        this.config = ConfigContext.getPurchasableItemConfig(this.idKey.toLowerCase() as PURCHASABLE_ITEM_ID);
        if (!this.config) {
            console.warn(`[SkillFragmentItem] 配置不存在: ${this.idKey} (尝试使用 ${this.idKey.toLowerCase()})`);
        } else {
            console.log(`[SkillFragmentItem] 配置初始化成功: ${this.idKey}`);
        }

        this.loadSpriteFromAtlas("image/icons", this.config.spriteName);
        this.labelName.string = this.config.name;

        this.fragmentCount = Math.floor(Math.random() * 3) + 1;
        this.labelAmount.string = "x " + this.fragmentCount.toString(); 

        const level = StatsContext.getSkill(idKey);
        this.labelLevel.string = "Lv." + level.toString();

        if(level>=10){
            this.spriteBg4.node.active = true;
        }else if(level>=7){
            this.spriteBg3.node.active = true;
        }else if(level>=4){
            this.spriteBg2.node.active = true;
        }else if(level>=1){
            this.spriteBg1.node.active = true;
        }
    }

    async loadSpriteFromAtlas(atlasPath: string, spriteName: string) {
            try {
                // 加载图集
                const atlas = await new Promise<SpriteAtlas>((resolve, reject) => {
                    resources.load(atlasPath, (err, atlas: SpriteAtlas) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(atlas);
                        }
                    });
                });
    
                // 获取指定名称的 SpriteFrame
                const spriteFrame = atlas.getSpriteFrame(spriteName);
                if (spriteFrame) {
                    // 获取 Sprite 组件并设置 SpriteFrame
                    const spriteComponent = this.itemIcon.getComponent(Sprite);
                    if (spriteComponent) {
                        spriteComponent.spriteFrame = spriteFrame;
                    } else {
                        console.warn('物品图片节点上没有 Sprite 组件');
                    }
                } else {
                    console.warn(`图集中未找到名为 ${spriteName} 的 SpriteFrame`);
                }
            } catch (error) {
                console.error('加载图集失败:', error);
            }
        }
        
} 