import { _decorator, Button, Color, Component, Enum, Label, Node, RichText, Sprite, SpriteFrame, Texture2D, animation, Animation, AnimationClip, Vec3 } from 'cc';
import { ConfigContext, PURCHASABLE_ITEM_ID, PurchasableItemConfig, SHOP_ITEM_TYPE } from '../../contexts/ConfigContext';
import { StatsContext } from '../../contexts/StatsContext';
import { PurchaseContext } from '../../contexts/PurchaseContext';
import { GameEvent } from '../../event/GameEvent';
import { NumberHelper } from '../../utils/NumberHelper';
import { TimeHelper } from '../../utils/TimeHelper';
import { SaveContext } from '../../contexts/SaveContext';
import { FloatingText } from '../floating_text/FloatingText';
import { UIManager } from '../../managers/UIManager';
import { GuideManager } from '../../managers/GuideManager';

const { ccclass, property } = _decorator;

@ccclass('ShopItem')
export class ShopItem extends Component {
    //背景
    @property(Sprite)
    spriteBg1: Sprite = null;
    @property(Sprite)
    spriteBg2: Sprite = null;
    @property(Sprite)
    spriteBg3: Sprite = null;
    @property(Sprite)
    spriteBg4: Sprite = null;

    @property(Node)
    nodeAnimation: Node = null;

    // 名称
    @property(Label)
    labelName: Label = null;

    // 价格
    @property(Label)
    labelPrice: Label = null;

    // 数量
    @property(Label)
    labelAmount: Label = null;

    // 图标按钮
    @property(Button)
    buttonIcon: Button = null;

    // 购买按钮
    @property(Button)
    buttonBuy: Button = null;

    // 广告按钮
    @property(Button)
    buttonAD: Button = null;

    // 升级按钮
    @property(Button)
    buttonUpgrade: Button = null;

    // 选择按钮
    @property(Button)
    buttonSelect: Button = null;

    // 上限/已上场
    @property(Label)
    labelMax: Label = null;

    // 剩余时间
    @property(Label)
    labelTime: Label = null;

    // 碎片数量
    @property(Label)
    labelFrament: Label = null;

    // 配置ID
    @property
    idKey: string = '';

    // 配置类型
    @property
    idType: string = '';

    // 动画前缀
    @property
    animKey: string = '';

    // 增加量是否为百分比
    @property
    increaseIsPercentage: boolean = false;

    // 事件回调
    onBought: Function = null;
    onFailedBuy: Function = null;

    // 配置
    private _config: PurchasableItemConfig = null;

    get config(): PurchasableItemConfig {
        return this._config;
    }

    //倒计时
    private countDownTimer: number = 0;

    //动画
    private animationIcon: Animation = null;

    onLoad() {
        this._config = ConfigContext.getPurchasableItemConfig(this.idKey.toLowerCase() as PURCHASABLE_ITEM_ID);
        if (!this._config) {
            console.warn(`[Purchasable] 配置不存在: ${this.idKey} (尝试使用 ${this.idKey.toLowerCase()})`);
        } else {
            console.log(`[Purchasable] 配置初始化成功: ${this.idKey}`);
        }
        switch (this.idType) {
            case SHOP_ITEM_TYPE.EMPLOYEE:
                break;
            case SHOP_ITEM_TYPE.SCENE:
                if (StatsContext.currentScene != PURCHASABLE_ITEM_ID.SCENE_GRASSLAND.toUpperCase()) {
                    this.checkSceneValidity();
                    if (this.countDownTimer == 0 && StatsContext.currentScene == this.idKey.toUpperCase()) {
                        //使用到期
                        StatsContext.setScene(this.idKey as PURCHASABLE_ITEM_ID, 0);
                        StatsContext.currentScene = PURCHASABLE_ITEM_ID.SCENE_GRASSLAND.toUpperCase();
                        StatsContext.events.emit(GameEvent.SCENE_CHANGED, PURCHASABLE_ITEM_ID.SCENE_GRASSLAND);
                        SaveContext.saveGame();
                    }
                }
                break;
        }
    }

    start() {
        if (this.buttonAD) this.buttonAD.node.active = false;
        if (this.buttonBuy) this.buttonBuy.node.active = false;
        if (this.buttonSelect) this.buttonSelect.node.active = false;
        if (this.labelTime) this.labelTime.node.active = false;
        if (this.labelAmount) this.labelAmount.node.active = false;
        // if(this.labelMax)this.labelMax.node.active = false;
        if (this.labelFrament) this.labelFrament.node.active = false;

        this.labelName.string = this.config.name;

        switch (this.idType) {
            case SHOP_ITEM_TYPE.UPGRADE:
                this.labelAmount.node.active = true;
                StatsContext.events.on(GameEvent.RESET_GAME, this.setUpgradeItem, this);
                this.setUpgradeItem();
                break;
            case SHOP_ITEM_TYPE.EMPLOYEE:
                StatsContext.events.on(GameEvent.EMPLOYEE_CHANGED, this.onEmployeeChanged, this);
                StatsContext.events.on(GameEvent.RESET_GAME, this.setEmployeeItem, this);
                StatsContext.events.on(GameEvent.EMPLOYEE_EXIT_COMPLETE, this.onEmployeeExitComplete, this);

                this.animationIcon = this.nodeAnimation.getComponent(Animation);
                this.setEmployeeItem();
                break;
            case SHOP_ITEM_TYPE.SKILL:
                StatsContext.events.on(GameEvent.RESET_GAME, this.setSkillItem, this);
                this.setSkillItem();
                break;
            case SHOP_ITEM_TYPE.SCENE:
                StatsContext.events.on(GameEvent.SCENE_CHANGED, this.setSceneItem, this);
                StatsContext.events.on(GameEvent.RESET_GAME, this.setSceneItem, this);
                this.setSceneItem();
                break;
        }
        // 注册按钮事件
        if (this.buttonBuy) {
            this.buttonBuy.node.on(Button.EventType.CLICK, this.onButtonPressed, this);
        }
        this.buttonAD?.node.on(Button.EventType.CLICK, this.onButtonADPressed, this);
        this.buttonUpgrade?.node.on(Button.EventType.CLICK, this.onButtonUpgradePressed, this);
        this.buttonSelect?.node.on(Button.EventType.CLICK, this.onButtonSelectPressed, this);
        this.buttonIcon?.node.on(Button.EventType.CLICK, this.onButtonIconPressed, this);

        StatsContext.events.on(GameEvent.SHOP_ITEM_INTRODUCE_BUY, this.onShopItemBuy, this);
        StatsContext.events.on(GameEvent.MAIN_TASK_COMPLETE_REWARD, this.onMainTaskCompleteReward, this);


        //新手指引
        this.setGuide();
        this.onMainTaskCompleteReward();

    }
    setUpgradeItem() {
        //数量是否达到上限
        if (PurchaseContext.getPurchaseAmount(this.idKey) < this.config.max_purchase_amounts) {
            //只能金币购买
            this.buttonBuy.node.active = true;
        }
    }

    setEmployeeItem() {
        if (this.config) {
            if (StatsContext.getEmployee(this.idKey) > 0) {
                //已上场(已拥有)
                this.spriteBg2.node.active = true;
                this.spriteBg3.node.active = false;
                if (this.buttonAD) this.buttonAD.node.active = false;

                this.onEmployeeChanged(this.idKey, StatsContext.getEmployee(this.idKey), 0);
                this.playWalkAnimation();
            } else {
                //广告领取
                if (this.buttonAD) this.buttonAD.node.active = this.config.sellType == 1;
                //金币购买
                if (this.buttonBuy) this.buttonBuy.node.active = this.config.sellType == 2;

                if (this.spriteBg2) this.spriteBg2.node.active = false;
                if (this.spriteBg3) this.spriteBg3.node.active = this.config.sellType == 1;

                this.playIdleAnimation();
            }

            //永久 or 限时
            this.labelName.string = this.config.expiration_data == 0 ? '永久' : this.config.expiration_data + '小时';
        }
    }
    setSkillItem() {
        this.buttonUpgrade.node.active = true;
        this.labelAmount.node.active = true;
        this.labelFrament.node.active = true;

        const level = StatsContext.getSkill(this.idKey);
        const hasFragment = StatsContext.getSkillFragment(this.idKey + "_fragment");
        const needFragment = this.config.price_expressions.base + level * this.config.price_expressions.multiplier + this.config.price_expressions.flat_offset;

        if (level >= 10) {
            this.spriteBg4.node.active = true;
        } else if (level >= 7) {
            this.spriteBg3.node.active = true;
        } else if (level >= 4) {
            this.spriteBg2.node.active = true;
        } else if (level >= 1) {
            this.spriteBg1.node.active = true;
        }

        this.labelAmount.string = "Lv." + level;
        this.labelFrament.string = hasFragment + "/" + needFragment;
        //碎片数量可升级
        if (hasFragment > 0 && hasFragment >= needFragment) {
            //显示红点
            //激活升级按钮
            this.buttonUpgrade.interactable = true;
            this.labelFrament.color = Color.GREEN;
        } else {
            //不可升级
            this.buttonUpgrade.interactable = false;
            this.labelFrament.color = Color.WHITE;
        }
    }

    setSceneItem() {
        if (this.config) {
            if (StatsContext.getScene(this.idKey) > 0) {
                //已拥有
                if (this.idKey.toUpperCase() == StatsContext.currentScene) {
                    //使用中
                    // this.labelMax.node.active = true;
                    this.buttonSelect.node.active = false;
                } else {
                    //可选择
                    this.buttonSelect.node.active = true;
                }
                if (this.buttonAD) this.buttonAD.node.active = false;
                if (this.config.expiration_data == 0) {
                    //永久使用
                    this.labelName.node.active = true;
                    this.labelTime.node.active = false;
                } else {
                    this.labelName.node.active = false;
                    this.labelTime.node.active = true;
                    //有效期检测
                    // this.buyTime = StatsContext.getScene(this.idKey);
                }
            } else {
                //广告领取
                if (this.buttonAD) this.buttonAD.node.active = this.config.sellType == 1;
                //钻石购买
                this.buttonBuy.node.active = this.config.sellType == 2;
            }

            //永久 or 限时
            this.labelName.string = this._config.expiration_data == 0 ? '永久' : this._config.expiration_data + '天';
        }
    }
    update(deltaTime: number) {
        if (!this._config) {
            // 如果没有 Purchasable 或其配置，可能需要禁用或隐藏此 ShopItem
            // 或者显示默认的加载/不可用状态
            this.labelName.string = '---';
            this.labelPrice.string = '-';
            this.labelAmount.string = '-';
            if (this.buttonBuy) this.buttonBuy.interactable = false;
            if (this.buttonAD) this.buttonAD.interactable = false;
            if (this.buttonUpgrade) this.buttonUpgrade.interactable = false;
            return;
        }

        const price = this.getPrice(); // 从 PurchaseContext 获取价格
        if (this.labelPrice) {
            this.labelPrice.string = NumberHelper.convert_number_to_text(price);//price.toString();
        }

        // 更新按钮的可交互状态
        if (this.buttonBuy) {
            // canBuy 方法会检查所有条件，包括货币
            this.buttonBuy.interactable = this.canBuy();
        }

        if (this.idType == SHOP_ITEM_TYPE.EMPLOYEE) {
            //有效期倒计时
            if (this.config && this.config.expiration_data > 0 && StatsContext.getEmployee(this.idKey) > 0) {
                this.checkEmployeeValidity();
                this.labelTime.string = TimeHelper.secondsToDHMS(this.countDownTimer);
                if (this.countDownTimer <= 0) {

                    //使用到期
                    StatsContext.setEmployee(this.idKey as PURCHASABLE_ITEM_ID, 0);

                    //通知角色离场
                    StatsContext.events.emit(GameEvent.EMPLOYEE_EXPIRED, this.idKey);
                }
            }
        } else if (this.idType == SHOP_ITEM_TYPE.SCENE) {
            //有效期倒计时
            if (this.config && this.config.expiration_data > 0 && StatsContext.getScene(this.idKey) > 0) {
                this.checkSceneValidity();
                this.labelTime.string = TimeHelper.secondsToDHMS(this.countDownTimer);
                if (this.countDownTimer <= 0) {
                    this.labelTime.node.active = false;
                    this.labelName.node.active = true;

                    //使用到期
                    StatsContext.setScene(this.idKey as PURCHASABLE_ITEM_ID, 0);

                    //切换到绿地场景
                    StatsContext.currentScene = PURCHASABLE_ITEM_ID.SCENE_GRASSLAND.toUpperCase();
                    StatsContext.events.emit(GameEvent.SCENE_CHANGED, PURCHASABLE_ITEM_ID.SCENE_GRASSLAND);
                    SaveContext.saveGame();
                }
            }
        } else if (this.idType == SHOP_ITEM_TYPE.SKILL) {
            this.setSkillItem();
            // countStr = 'Lv.' + StatsContext.getSkill(this.idKey);
        } else {
            // 是否达到最大购买量 (使用 _config.max_purchase_amounts)
            const currentAmount = this.getPurchaseAmount();
            const maxAmount = this._config.max_purchase_amounts;
            let countStr = `${currentAmount}/${maxAmount}`;
            this.labelAmount.string = countStr;
        }
        // this.labelAmount.string = `<color=#444444>${labelAmountText}</color>`; // 考虑颜色配置化

    }

    onButtonPressed() {
        // 尝试购买
        this.tryPurchase();
        if (StatsContext.newFingerGuide == 2 && this.idKey == PURCHASABLE_ITEM_ID.SMALL_COIN) {
            // StatsContext.newFingerGuide = 3;
            GuideManager.instance.nextGuide();
            GuideManager.instance.hideGuide_2();
        }
        if (StatsContext.newFingerGuide == 4 && this.idKey == PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_SMALL) {
            // StatsContext.newFingerGuide = 5;
            GuideManager.instance.nextGuide();
            GuideManager.instance.hideGuide_3();
        }
    }
    onButtonADPressed() {
        if (StatsContext.newFingerGuide == 0) {
            // TODO: 调用FloatingText.create显示收益
            FloatingText.create(this.node, new Vec3(0, 0), `请完成新手指引`);
            return;
        }
        //观看广告
        if (this.idType === SHOP_ITEM_TYPE.EMPLOYEE) {
            StatsContext.setEmployee(this.idKey as PURCHASABLE_ITEM_ID, TimeHelper.getCurrentTimestampInSeconds());
            this.setEmployeeItem();
        } else if (this.idType === SHOP_ITEM_TYPE.SCENE) {
            StatsContext.setScene(this.idKey as PURCHASABLE_ITEM_ID, TimeHelper.getCurrentTimestampInSeconds());
            this.setSceneItem();
        }
        SaveContext.saveGame();
    }
    onButtonUpgradePressed() {
        //升级
        this.tryUpgradePurchase();
    }
    onButtonSelectPressed() {
        //选择
        StatsContext.currentScene = this.idKey.toUpperCase();
        StatsContext.events.emit(GameEvent.SCENE_CHANGED, this.idKey);
        SaveContext.saveGame();
    }
    onButtonIconPressed() {
        if (this.idType == "skill") {
            UIManager.instance.showShopItemSkillIntroduce(this.idKey, this._config, this.buttonUpgrade.interactable);
        } else {
            UIManager.instance.showShopItemIntroduce(this.idKey, this._config, this.buttonBuy.interactable);
        }
    }

    /**
     * 检查当前是否可以购买此物品。
     * 依赖 Purchasable 组件的方法，这些方法已重构为使用 PurchaseContext。
     * @returns boolean 是否可以购买
     */
    canBuy(): boolean {
        if (!this._config) return false;

        const price = this.getPrice(); // 通过 Purchasable -> PurchaseContext

        if (this.idType == SHOP_ITEM_TYPE.UPGRADE) {
            if (!this.checkChangeCoin()) {
                return false;
            }
        }
        // checkShowRequirement 和 checkBuyAgainRequirements 也通过 Purchasable -> PurchaseContext
        return this.checkShowRequirement() &&
            this.checkBuyAgainRequirements() && // 检查是否达到上限等
            StatsContext.diamond >= price && // 检查玩家是否有足够货币
            this.getPurchaseAmount() < this._config.max_purchase_amounts; // 再次确认未满级
    }

    /**
     * 执行购买尝试的逻辑。
     * 调用 Purchasable 的 onBuy 方法，该方法内部使用 PurchaseContext 进行购买。
     */
    tryPurchase() {
        if (!this._config) {
            console.warn('[ShopItem] tryPurchase: Purchasable 未设置');
            if (this.onFailedBuy) {
                this.onFailedBuy('Purchasable component not available');
            }
            return;
        }

        console.log('[ShopItem] 尝试购买:', this.idKey);

        // canBuy() 已经做了前置检查，但核心购买逻辑在 Purchasable.onBuy() 中
        // Purchasable.onBuy() 现在返回 boolean
        if (this.onBuy()) {
            console.log('[ShopItem] 购买成功:', this.idKey);
            if (this.onBought) {
                this.onBought(this.idKey);
            }
            if (this.idType == SHOP_ITEM_TYPE.EMPLOYEE) {
                this.setEmployeeItem();
            }
            // 可以在此播放成功音效等
            // AudioManager.playSoundEffect('purchase_success');
        } else {
            console.warn('[ShopItem] 购买失败:', this.idKey);
            if (this.onFailedBuy) {
                this.onFailedBuy(this.idKey, 'Failed via PurchaseContext');
            }
            // 可以在此播放失败音效等
            // AudioManager.playSoundEffect('purchase_fail');
        }

        // 购买操作后，UI状态会在下一帧的update中刷新
        // 如果需要立即刷新部分UI（例如按钮状态），可以手动调用一次更新相关UI的逻辑
        this.update(0); // 强制刷新UI，显示最新状态
    }

    /**
     * 执行升级尝试的逻辑。
     * 调用 Purchasable 的 onUpgrade 方法，该方法内部使用 PurchaseContext 进行购买。
     */
    tryUpgradePurchase() {
        if (!this._config) {
            console.warn('[ShopItem] tryUpgradePurchase: Purchasable 未设置');
            if (this.onFailedBuy) {
                this.onFailedBuy('Purchasable component not available');
            }
            return;
        }

        console.log('[ShopItem] 尝试升级:', this.idKey);

        // canBuy() 已经做了前置检查，但核心购买逻辑在 Purchasable.onBuy() 中
        // Purchasable.onUpgrade() 现在返回 boolean
        if (this.onUpgrade()) {
            console.log('[ShopItem] 升级成功:', this.idKey);
            if (this.onBought) {
                this.onBought(this.idKey);
            }
            // 可以在此播放成功音效等
            // AudioManager.playSoundEffect('purchase_success');
        } else {
            console.warn('[ShopItem] 升级失败:', this.idKey);
            if (this.onFailedBuy) {
                this.onFailedBuy(this.idKey, 'Failed via PurchaseContext');
            }
            // 可以在此播放失败音效等
            // AudioManager.playSoundEffect('purchase_fail');
        }

        // 升级操作后，UI状态会在下一帧的update中刷新
        // 如果需要立即刷新部分UI（例如按钮状态），可以手动调用一次更新相关UI的逻辑
        this.update(0); // 强制刷新UI，显示最新状态
    }

    // onMouseEntered 方法可以保留，它用于显示工具提示，不直接参与购买逻辑
    onMouseEntered() {
        if (!this._config || !this.checkShowRequirement()) {
            console.log('工具提示: ???');
            return;
        }

        let increaseAmountText: string;
        const increaseVal = this._config.increase_amounts; // 来自物品配置

        // 这个 increaseIsPercentage 应该来自 PurchasableItem config，而不是 Purchasable 实例
        // 假设 this._config 有 is_percentage 这样的字段
        const isPercentage = (this._config as any).is_percentage; // 假设配置中有这个字段

        if (isPercentage) {
            increaseAmountText = `${Math.floor(increaseVal * 100)}%`;
        } else {
            increaseAmountText = `${Math.floor(increaseVal)}`;
        }

        // 特定物品的 increase_amount 可能需要特殊处理或从配置中读取更具体的值
        // 以下的 switch-case 可能是为了覆盖特定物品的显示逻辑
        switch (this.idKey.toLowerCase()) { // 使用小写比较
            case 'large_coin_name': // 这些key需要与实际的idKey匹配
                increaseAmountText = ConfigContext.largeCoinBaseValue.toString();
                break;
            case 'medium_coin_name':
                increaseAmountText = ConfigContext.mediumCoinBaseValue.toString();
                break;
            case 'small_coin_name':
                increaseAmountText = ConfigContext.smallCoinBaseValue.toString();
                break;
        }

        // 实际应用中应该使用工具提示系统
        // config.desc 可能包含占位符 {increase_amount}
        const description = this._config.desc || '没有描述';
        console.log(`工具提示: ${description.replace('{increase_amount}', increaseAmountText)}`);
    }

    /**
     * 尝试购买此物品。
     * 实际的购买逻辑已移至 PurchaseContext。
     * @returns boolean 是否购买成功
     */
    onBuy(): boolean {
        // 调用 PurchaseContext 处理购买逻辑
        // idKey 会在 PurchaseContext 内部转换为小写
        const success = PurchaseContext.attemptPurchase(this.idKey);
        if (success) {
            console.log(`[Purchasable] 物品 ${this.idKey} 购买成功 (通过 PurchaseContext)`);
            // 可以在这里触发节点级别的购买成功事件，如果需要的话
            // this.node.emit('purchased-successfully', this.idKey);
            if (this.idType == SHOP_ITEM_TYPE.UPGRADE) {
                //数量是否达到上限
                if (PurchaseContext.getPurchaseAmount(this.idKey) >= this.config.max_purchase_amounts) {
                    //只能金币购买
                    this.buttonBuy.node.active = false;
                }
            }
            //如果是雇员||场景
            if (this.idType == SHOP_ITEM_TYPE.EMPLOYEE || this.idType == SHOP_ITEM_TYPE.SCENE) {
                if (this.buttonAD) this.buttonAD.node.active = false;
                this.buttonBuy.node.active = false;
                // this.labelMax.node.active = true;
            }
        } else {
            console.log(`[Purchasable] 物品 ${this.idKey} 购买失败 (通过 PurchaseContext)`);
            // 可以在这里触发节点级别的购买失败事件
            // this.node.emit('purchase-failed', this.idKey);
        }
        return success;
    }

    /**
     * 尝试升级技能。
     * 实际的升级逻辑已移至 PurchaseContext。
     * @returns boolean 是否升级成功
     */
    onUpgrade(): boolean {
        // 调用 PurchaseContext 处理购买逻辑
        // idKey 会在 PurchaseContext 内部转换为小写
        const success = PurchaseContext.attemptUpgradePurchase(this.idKey);
        if (success) {
            console.log(`[Purchasable] 技能 ${this.idKey} 升级成功 (通过 PurchaseContext)`);
            // 可以在这里触发节点级别的升级成功事件，如果需要的话
            // this.node.emit('purchased-successfully', this.idKey);
            //如果是技能||
            if (this.idType == SHOP_ITEM_TYPE.SKILL) {
                //等级是否达到上限
                if (StatsContext.getSkill(this.idKey) >= this.config.max_purchase_amounts) {
                    this.buttonUpgrade.node.active = false;
                }
            }
        } else {
            console.log(`[Purchasable] 技能 ${this.idKey} 购买失败 (通过 PurchaseContext)`);
            // 可以在这里触发节点级别的升级失败事件
            // this.node.emit('purchase-failed', this.idKey);
        }
        return success;
    }

    /**
     * 获取此物品已购买的数量。
     * @returns number 已购买数量
     */
    getPurchaseAmount(): number {
        // idKey 会在 PurchaseContext 内部转换为小写
        return PurchaseContext.getPurchaseAmount(this.idKey);
    }

    /**
     * 检查此物品是否满足显示条件。
     * @returns boolean 是否显示
     */
    checkShowRequirement(): boolean {
        // idKey 会在 PurchaseContext 内部转换为小写
        return PurchaseContext.canShowItem(this.idKey);
    }

    /**
     * 检查此物品是否满足基本购买条件 (不含货币、不含再次购买限制)。
     * 这是一个综合检查，UI层面可能需要更细分的条件。
     * @returns boolean 是否满足购买条件
     */
    checkBuyRequirement(): boolean {
        // idKey 会在 PurchaseContext 内部转换为小写
        // 这个方法综合了初始条件和再次购买条件
        return PurchaseContext.meetsInitialBuyRequirements(this.idKey) &&
            PurchaseContext.canBuyAgain(this.idKey);
    }

    /**
     * 检查此物品是否可以再次购买 (例如，未达到最大购买量)。
     * @returns boolean 是否可以再次购买
     */
    checkBuyAgainRequirements(): boolean {
        // idKey 会在 PurchaseContext 内部转换为小写
        return PurchaseContext.canBuyAgain(this.idKey);
    }

    /**
     * 获取此物品的当前价格。
     * @returns number 价格
     */
    getPrice(): number {
        // idKey 会在 PurchaseContext 内部转换为小写
        return PurchaseContext.getItemPrice(this.idKey);
    }
    /**
     * 铜币转银需要判断铜币的数量。
     * 点银成金需要判断银币的数量。
     */

    checkChangeCoin(): boolean {
        if (this.idKey == 'change_middle_to_large') {
            return StatsContext.getUpgrade('CHANGE_SMALL_TO_MIDDLE') > 0;//StatsContext.getUpgrade(this.idKey);

        } else if (this.idKey == 'change_small_to_middle') {
            return StatsContext.getUpgrade('SMALL_COIN') > 0;//StatsContext.getUpgrade(this.idKey);
        }
        return true;
    }

    onShopItemBuy(idkey: string) {
        if (this.idKey == idkey) {
            if (this.config.category == "skill") {
                this.onButtonUpgradePressed();
            } else {
                this.onButtonPressed();
            }
        }
    }

    // 红点检测
    checkRedDot(): boolean {
        if (this.idType == SHOP_ITEM_TYPE.SKILL) {
            if (this.buttonUpgrade) return this.buttonUpgrade.interactable;
        } else {
            if (this.config.sellType == 2) {
                if (this.buttonBuy) return this.buttonBuy.interactable;
            }
        }
        return false;
    }

    /**
     * 雇员数量变化处理
     */
    onEmployeeChanged(key: string, newValue: number, oldValue: number) {

        if (newValue == 0) return;

        console.log(`[ShopItem] 雇员数量变化: 旧=${oldValue}, 新=${newValue}`);
        if (key.toLowerCase() == this.idKey) {
            if (this.config.expiration_data == 0) {
                //永久
                this.labelTime.node.active = false;
                this.labelName.node.active = true;
            } else {
                //有时效
                this.labelTime.node.active = true;
                this.labelName.node.active = false;
            }
        }
    }
    checkEmployeeValidity() {
        const buyTime = StatsContext.getEmployee(this.idKey);
        const curTime = TimeHelper.getCurrentTimestampInSeconds();
        const useTime = curTime - buyTime;//使用了多长时间
        const validity_time = this.config.expiration_data * 60 * 60;//单位秒，this.config.expiration_data 配置的是小时数
        this.countDownTimer = validity_time - useTime;
        if (this.countDownTimer < 0) {
            this.countDownTimer = 0;
        }
    }

    checkSceneValidity() {
        const buyTime = StatsContext.getScene(this.idKey);
        const curTime = TimeHelper.getCurrentTimestampInSeconds();
        const useTime = curTime - buyTime;//使用了多长时间
        const validity_time = this.config.expiration_data * 24 * 60 * 60;//单位秒，this.config.expiration_data配置的是天数
        this.countDownTimer = validity_time - useTime;
        if (this.countDownTimer < 0) {
            this.countDownTimer = 0;
        }
    }
    playWalkAnimation() {
        if (this.animationIcon) {
            this.animationIcon.play(this.animKey + "_walk_side_up");
        }
    }
    playIdleAnimation() {
        if (this.animationIcon) {
            this.animationIcon.play(this.animKey + "_idle_up");
        }
    }

    onMainTaskCompleteReward() {
        if (StatsContext.newFingerGuide == 2 && this.idKey == PURCHASABLE_ITEM_ID.SMALL_COIN) {
            GuideManager.instance.showGuide_2();
        }
        if (StatsContext.newFingerGuide == 4 && this.idKey == PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_SMALL) {
            GuideManager.instance.showGuide_3();
        }
    }

    setGuide() {
        if (this.idKey == PURCHASABLE_ITEM_ID.SMALL_COIN) {
            GuideManager.instance.hideGuide_2();
        }
        if (this.idKey == PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_SMALL) {
            GuideManager.instance.hideGuide_3();
        }
    }

    onEmployeeExitComplete(key: string) {
        if (key == this.idKey) {
            this.labelTime.node.active = false;
            this.labelName.node.active = true;
            this.setEmployeeItem();
        }
    }
}  