import { _decorator, Component, Node, Prefab, instantiate, Label, Color, Button, Widget, Toggle, UITransform, UIOpacity, ScrollView, Sprite, Vec3 } from 'cc';
import { StatsContext } from '../../contexts/StatsContext';
import { SaveContext } from '../../contexts/SaveContext';
import { AudioManager } from '../../managers/AudioManager';
import { ShopItem } from './ShopItem';
import { GameEvent } from '../../event/GameEvent';
import { GuideManager } from '../../managers/GuideManager';
import { FloatingText } from '../floating_text/FloatingText';

const { ccclass, property } = _decorator;

/**
 * 商店物品类别
 */
export enum ShopItemCategory {
    PASSIVE_SKILL = "passive_skill", // 被动技能
    EMPLOYEE = "employee", // 员工
    ACTIVE_SKILL = "active_skill",     // 主动技能
    SCENE = "scene" // 场景
}

/**
 * 商店侧边栏组件 - 管理商店物品展示和购买
 */
@ccclass('ShopBar')
export class ShopBar extends Component {
    // 被动技能容器
    @property(Node)
    passiveSkillContainer: Node = null;
    // 雇员容器
    @property(Node)
    employeeContainer: Node = null;
    // 主动技能容器
    @property(Node)
    activeSkillContainer: Node = null;
    // 桌子容器
    @property(Node)
    sceneContainer: Node = null;

    // 滚动视图
    @property(ScrollView)
    scrollView: ScrollView = null;

    // 标签页容器
    @property(Node)
    tabContainer: Node = null;
    // 被动技能标签
    @property(Node)
    passiveSkillTab: Node = null;
    // 雇员标签
    @property(Node)
    employeeTab: Node = null;
    // 主动技能标签
    @property(Node)
    activeSkillTab: Node = null;
    // 桌子标签
    @property(Node)
    sceneTab: Node = null;

    // 被动技能预制体
    @property([Prefab])
    passiveSkillPrefabs: Prefab[] = [];
    // 雇员预制体
    @property([Prefab])
    employeePrefabs: Prefab[] = [];
    // 道具预制体   
    @property([Prefab])
    activeSkillPrefabs: Prefab[] = [];
    // 桌子预制体
    @property([Prefab])
    scenePrefabs: Prefab[] = [];

    // 红点
    @property(Sprite)
    redDotUpgrade: Sprite = null;
    @property(Sprite)
    redDotEmployee: Sprite = null;
    @property(Sprite)
    redDotSkill: Sprite = null;
    @property(Sprite)
    redDotScene: Sprite = null;

    // 商店物品列表
    private shopItems: ShopItem[] = [];

    // 当前显示的标签
    private currentCategory: ShopItemCategory = ShopItemCategory.ACTIVE_SKILL;

    // 更新时间
    private updateTimer: number = null;

    // ShopItemCategory与Tab的映射
    private categoryTabMap: Map<ShopItemCategory, Node> = new Map<ShopItemCategory, Node>();

    // ShopItemCategory与Container的映射
    private categoryContainerMap: Map<ShopItemCategory, Node> = new Map<ShopItemCategory, Node>();

    // ShopItemCategory与预制体的映射
    private categoryPrefabMap: Map<ShopItemCategory, Prefab[]> = new Map<ShopItemCategory, Prefab[]>();


    onLoad() {
        // 设置商店面板的宽度适应屏幕
        const widget = this.getComponent(Widget);
        if (widget) {
            widget.updateAlignment();
        }

        // 初始化标签页
        this.initializeTabs();

        // 从预制体创建Purchasable实例
        this.createShopItemFromPrefabs();

        this.onInitGame();

        StatsContext.events.on(GameEvent.RESET_GAME, this.onInitGame, this);
    }

    onInitGame() {
        // 设置默认标签页
        this.currentCategory = ShopItemCategory.PASSIVE_SKILL;

        // 默认显示道具页面
        this.switchTab(this.currentCategory);
    }

    /**
     * 初始化标签页
     */
    initializeTabs() {
        // 初始化Category与Tab的映射
        this.categoryTabMap.set(ShopItemCategory.PASSIVE_SKILL, this.passiveSkillTab);
        this.categoryTabMap.set(ShopItemCategory.EMPLOYEE, this.employeeTab);
        this.categoryTabMap.set(ShopItemCategory.ACTIVE_SKILL, this.activeSkillTab);
        this.categoryTabMap.set(ShopItemCategory.SCENE, this.sceneTab);

        // 初始化Category与Container的映射
        this.categoryContainerMap.set(ShopItemCategory.PASSIVE_SKILL, this.passiveSkillContainer);
        this.categoryContainerMap.set(ShopItemCategory.EMPLOYEE, this.employeeContainer);
        this.categoryContainerMap.set(ShopItemCategory.ACTIVE_SKILL, this.activeSkillContainer);
        this.categoryContainerMap.set(ShopItemCategory.SCENE, this.sceneContainer);

        // 初始化Category与Prefab的映射
        this.categoryPrefabMap.set(ShopItemCategory.PASSIVE_SKILL, this.passiveSkillPrefabs);
        this.categoryPrefabMap.set(ShopItemCategory.EMPLOYEE, this.employeePrefabs);
        this.categoryPrefabMap.set(ShopItemCategory.ACTIVE_SKILL, this.activeSkillPrefabs);
        this.categoryPrefabMap.set(ShopItemCategory.SCENE, this.scenePrefabs);
    }

    /**
     * Tab标签页点击事件
     */
    onTabClicked(event: Event, data: string) {
        const category = data as ShopItemCategory;

        //新手引导
        if (StatsContext.mainTaskID < 13 && category == ShopItemCategory.EMPLOYEE) {
            this.showLockTip();
            return;
        }
        if (StatsContext.mainTaskID < 14 && category == ShopItemCategory.ACTIVE_SKILL) {
            this.showLockTip();
            return;
        }
        if (StatsContext.mainTaskID < 18 && category == ShopItemCategory.SCENE) {
            this.showLockTip();
            return;
        }
        
        if (StatsContext.mainTaskID == 13 && StatsContext.newFingerGuide == 5 && category == ShopItemCategory.EMPLOYEE) {
            GuideManager.instance.nextGuide();
            GuideManager.instance.hideGuide_13();
        }
        else if (StatsContext.mainTaskID == 14 && StatsContext.newFingerGuide == 6 && category == ShopItemCategory.ACTIVE_SKILL) {
            GuideManager.instance.nextGuide();
            GuideManager.instance.hideGuide_14();
        }
        else if (StatsContext.mainTaskID == 18 && StatsContext.newFingerGuide == 7 && category == ShopItemCategory.SCENE) {
            GuideManager.instance.nextGuide();
            GuideManager.instance.hideGuide_18();
        }

        // 如果当前标签页与要切换的标签页相同，则不进行切换
        if (this.currentCategory === category) {
            return;
        }

        //新手判断


        this.currentCategory = category;
        this.switchTab(this.currentCategory);
    }
    showLockTip() {
        const textPos = new Vec3(
            this.node.position.x,
            this.node.position.y + 50,
            this.node.position.z
        );
        FloatingText.create(this.node.parent, textPos, `未解锁，请完成任务`);
    }

    /**
     * 切换到指定标签页
     * @param category 要显示的标签类别
     */
    switchTab(category: ShopItemCategory) {
        try {
            // 检查ScrollView是否存在
            if (!this.scrollView) {
                console.error("滚动视图未设置，无法切换标签页");
                return;
            }

            // 隐藏所有标签页中的名字为SelectBG的对象隐藏
            this.activeSkillTab.getChildByName('SelectBG').active = false;
            this.passiveSkillTab.getChildByName('SelectBG').active = false;
            this.employeeTab.getChildByName('SelectBG').active = false;
            this.sceneTab.getChildByName('SelectBG').active = false;

            // 设置当前标签页的SelectBG对象显示
            const currentTab: Node = this.categoryTabMap.get(category);
            currentTab.getChildByName('SelectBG').active = true;

            // 隐藏所有容器
            this.activeSkillContainer.active = false;
            this.passiveSkillContainer.active = false;
            this.employeeContainer.active = false;
            this.sceneContainer.active = false;

            // 设置当前标签页的容器显示
            const currentContainer: Node = this.categoryContainerMap.get(category);
            currentContainer.active = true;

            // 设置ScrollView的内容
            if (currentContainer) {
                // 确保ScrollView的内容被正确设置
                this.scrollView.content = currentContainer;

                // 重置滚动位置
                this.scrollView.scrollToLeft();

                // 排序列表项
                // this.sortTab(currentContainer);

                // 在下一帧刷新ScrollView布局
                this.scheduleOnce(() => {
                    this.scrollView.scrollToLeft();
                }, 0);
            }
        } catch (error) {
            console.error("切换标签页时发生错误:", error);
        }
    }

    /**
     * 显示指定标签页 (保留向后兼容)
     */
    showTab(category: ShopItemCategory) {
        this.switchTab(category);
    }

    /**
     * 从预制体创建Purchasable实例
     */
    createShopItemFromPrefabs() {
        // 遍历所有ShopItemCategory
        for (const [category, prefabs] of this.categoryPrefabMap) {
            // 确保容器节点存在
            const container = this.categoryContainerMap.get(category);
            if (!container) {
                console.error(`未找到与类别 ${category} 对应的容器节点`);
                continue;
            }

            // 清空容器
            container.removeAllChildren();

            // 序号
            let index = 0;
            // 遍历预制体数组
            for (const prefab of prefabs) {
                if (!prefab) {
                    console.warn(`类别 ${category} 的预制体数组中存在空元素`);
                    continue;
                }

                // 实例化预制体
                const itemNode = instantiate(prefab);

                const shopItem = itemNode.getComponent(ShopItem);
                this.shopItems.push(shopItem);

                // 设置回调
                shopItem.onBought = () => {
                    this.updateAllItems();
                    SaveContext.saveGame();
                    AudioManager.playSoundEffect('shop_gold');
                };

                shopItem.onFailedBuy = () => {
                    AudioManager.playSoundEffect('task_cant_reward');
                };

                // 设置ShopItem的尺寸
                const transform = itemNode.getComponent(UITransform);
                if (transform) {
                    transform.width = 190;
                }

                // 设置位置 - 列表布局
                itemNode.setPosition(index * 188 + 98, 0, 0);
                index++;

                container.addChild(itemNode);
            }
        }
    }

    start() {
        // 设置定时器
        this.updateTimer = setInterval(() => {
            this.updateAllItems();
        }, 1000);

        this.updateAllItems();
    }
    protected update(dt: number): void {
        this.checkRedDot();
    }

    onDestroy() {
        // 清除定时器
        if (this.updateTimer !== null) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    updateAllItems() {
        // 更新所有商店物品的显示和状态
        for (const shopItem of this.shopItems) {
            shopItem.update(0);
        }
    }

    sortTab(categoryContainer: Node) {
        if (!categoryContainer || !categoryContainer.active) return;

        // 按照成本排序商店物品
        const shopItemNodes = categoryContainer.children
            .filter(node => node.getComponent(ShopItem))
            .slice();

        shopItemNodes.sort((a, b) => {
            const shopItemA = a.getComponent(ShopItem);
            const shopItemB = b.getComponent(ShopItem);

            if (!shopItemA || !shopItemB) return 0;

            // 首先按解锁状态排序
            const unlockedA = shopItemA.checkShowRequirement();
            const unlockedB = shopItemB.checkShowRequirement();

            if (unlockedA && !unlockedB) return -1;
            if (!unlockedA && unlockedB) return 1;

            // 已解锁则按价格排序
            if (unlockedA && unlockedB) {
                const priceA = shopItemA.getPrice();
                const priceB = shopItemB.getPrice();
                return priceA - priceB;
            }

            return 0;
        });

        // 重新排列节点
        shopItemNodes.forEach((node, index) => {
            node.setSiblingIndex(index);
        });
    }

    // 红点检测
    checkRedDot() {
        let redDotUpgrade = false;
        let redDotEmployee = false;
        let redDotSkill = false;
        let redDotScene = false;

        for (const shopItem of this.shopItems) {
            if (shopItem.checkRedDot()) {
                if (shopItem.idType == "upgrade") {
                    redDotUpgrade = true;
                } else if (shopItem.idType == "employee") {
                    redDotEmployee = true;
                } else if (shopItem.idType == "skill") {
                    redDotSkill = true;
                } else if (shopItem.idType == "scene") {
                    redDotScene = true;
                }
            }
        }
        this.redDotUpgrade.node.active = redDotUpgrade;
        //是否达到了商店的最大数量
        if (StatsContext.getEmployeeCount() >= StatsContext.employeeMaxCount) {
            this.redDotEmployee.node.active = false;
        } else {
            this.redDotEmployee.node.active = redDotEmployee;
        }
        this.redDotSkill.node.active = redDotSkill;
        this.redDotScene.node.active = redDotScene;
    }

}