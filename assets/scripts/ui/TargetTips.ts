import { _decorator, Button, Component, Label, Node } from 'cc';
import { SaveContext } from '../contexts/SaveContext';
import { StatsContext } from '../contexts/StatsContext';
import { GameEvent } from '../event/GameEvent';
import { AudioManager } from '../managers/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('TargetTips')
export class TargetTips extends Component {

    @property(Label)
    labelTargetTips: Label = null;

    protected onEnable(): void {
        if (this.labelTargetTips) {
            let targetStr = "一亿";
            if (StatsContext.money >= 100000000) {
                targetStr = "十亿";
            } else if (StatsContext.money >= 1000000000) {
                targetStr = "百亿";
            } else if (StatsContext.money >= 10000000000) {
                targetStr = "千亿";
            } else if (StatsContext.money >= 100000000000) {
                targetStr = "万亿";
            }
            this.labelTargetTips.string = `赚到${targetStr}\n用时最短者可以上排行榜`;
        }

    }

    onClose(){
        this.node.active = false;
    }
}


