import { _decorator, Component, Node, Label, tween, Vec3, UIOpacity, Prefab, instantiate, Color, director } from 'cc';
import { ConfigContext } from '../contexts/ConfigContext';

const { ccclass, property } = _decorator;

/**
 * 浮动文本类型枚举
 */
export enum FloatingTextType {
    COIN_VALUE = 'coin_value',    // 硬币价值
    PRESTIGE = 'prestige',        // 声望点数
    ACHIEVEMENT = 'achievement',  // 成就
    LEVEL_UP = 'level_up',        // 等级提升
}

/**
 * 浮动文本来源枚举
 */
export enum FloatingTextSource {
    PLAYER_CLICK = 'player_click',  // 玩家点击
    HELPER = 'helper',              // 员工
    SYSTEM = 'system',              // 系统
}

/**
 * 浮动文本组件 - 用于在游戏中显示浮动的文本动画
 * 例如：获得硬币、成就解锁等提示
 */
@ccclass('FloatingText')
export class FloatingText extends Component {
    @property(Label)
    label: Label = null;
    
    @property(UIOpacity)
    opacity: UIOpacity = null;
    
    @property
    moveDistance: number = 100;
    
    @property
    moveDuration: number = 1.0;
    
    @property
    fadeStartDelay: number = 0.5;
    
    @property
    fadeDuration: number = 0.5;

    private _value: number = 0;
    private _type: FloatingTextType = FloatingTextType.COIN_VALUE;
    private _source: FloatingTextSource = FloatingTextSource.PLAYER_CLICK;
    private _isAnimating: boolean = false;
    
    // 用于所有浮动文本实例的预制体引用
    private static prefab: Prefab = null;
    // UI根节点引用
    private static uiRoot: Node = null;
    
    /**
     * 初始化浮动文本
     * @param value 显示的数值
     * @param type 文本类型
     * @param source 文本来源
     */
    init(value: number, type: FloatingTextType = FloatingTextType.COIN_VALUE, 
         source: FloatingTextSource = FloatingTextSource.PLAYER_CLICK): void {
        this._value = value;
        this._type = type;
        this._source = source;
        
        // 设置文本和颜色
        this.setupTextAndColor();
    }
    
    /**
     * 设置文本内容和颜色
     */
    setupTextAndColor(): void {
        if (!this.label) return;
        
        let prefix = '+';
        let valueText = this._value.toFixed(0);
        let textColor = new Color(255, 255, 255, 255); // 默认白色
        
        // 根据类型设置不同的格式
        switch (this._type) {
            case FloatingTextType.COIN_VALUE:
                // 普通硬币金额显示
                prefix = '+$';
                textColor = new Color(255, 215, 0, 255); // 金色
                break;
                
            case FloatingTextType.PRESTIGE:
                // 声望点数显示
                prefix = '+';
                valueText = `${valueText}❇️`; // 添加星星emoji表示声望
                textColor = new Color(148, 0, 211, 255); // 紫色
                break;
                
            case FloatingTextType.ACHIEVEMENT:
                // 成就解锁显示
                prefix = '';
                valueText = '成就解锁！';
                textColor = new Color(0, 191, 255, 255); // 蓝色
                break;
                
            case FloatingTextType.LEVEL_UP:
                // 等级提升显示
                prefix = '';
                valueText = '等级提升！';
                textColor = new Color(50, 205, 50, 255); // 绿色
                break;
        }
        
        // 设置标签文本
        this.label.string = `${prefix}${valueText}`;
        
        // 设置文本颜色
        this.label.color = textColor;
        
        // 根据来源调整大小
        if (this._source === FloatingTextSource.HELPER) {
            this.node.scale = new Vec3(0.7, 0.7, 0.7);
        } else {
            this.node.scale = new Vec3(1.0, 1.0, 1.0);
        }
    }
    
    /**
     * 开始浮动文本动画
     */
    startAnimation(): void {
        if (this._isAnimating) return;
        this._isAnimating = true;
        
        // 确保组件已初始化
        if (!this.opacity) {
            this.opacity = this.node.getComponent(UIOpacity) || this.node.addComponent(UIOpacity);
        }
        
        // 设置初始透明度
        this.opacity.opacity = 255;
        
        // 起始位置
        const startPos = this.node.position.clone();
        
        // 目标位置（向上移动）
        const targetPos = new Vec3(
            startPos.x,
            startPos.y + this.moveDistance,
            startPos.z
        );
        
        // 创建移动动画
        const moveTween = tween(this.node)
            .to(this.moveDuration, { position: targetPos }, {
                easing: 'quadOut'
            });
            
        // 创建淡出动画
        const fadeTween = tween(this.opacity)
            .delay(this.fadeStartDelay)
            .to(this.fadeDuration, { opacity: 0 }, {
                easing: 'quadIn'
            })
            .call(() => {
                // 动画完成后移除节点
                this._isAnimating = false;
                this.node.destroy();
            });
            
        // 同时执行两个动画
        moveTween.start();
        fadeTween.start();
    }
    
    /**
     * 创建浮动文本实例
     * @param position 显示位置
     * @param value 显示的数值
     * @param type 文本类型
     * @param source 文本来源
     */
    static create(position: Vec3, value: number, 
                 type: FloatingTextType = FloatingTextType.COIN_VALUE,
                 source: FloatingTextSource = FloatingTextSource.PLAYER_CLICK): FloatingText {
        // 检查预制体是否已加载
        if (!FloatingText.prefab) {
            console.error('FloatingText prefab is not set!');
            return null;
        }
        
        // 创建实例
        const node = instantiate(FloatingText.prefab);
        
        // 设置位置
        node.position = position.clone();
        
        // 获取FloatingText组件
        const floatingText = node.getComponent(FloatingText);
        if (!floatingText) {
            console.error('FloatingText component not found on prefab!');
            return null;
        }
        
        // 初始化文本
        floatingText.init(value, type, source);
        
        // 添加到场景
        // 尝试获取UI根节点
        if (!FloatingText.uiRoot) {
            // 查找Canvas节点作为UI根节点
            const scene = director.getScene();
            if (scene) {
                FloatingText.uiRoot = scene.getChildByName('Canvas') || 
                                     scene.getChildByName('UI') || 
                                     scene;
            }
        }
        
        if (FloatingText.uiRoot) {
            FloatingText.uiRoot.addChild(node);
        } else {
            console.warn('UI Root not found! Adding FloatingText directly to scene.');
            director.getScene().addChild(node);
        }
        
        // 开始动画
        floatingText.startAnimation();
        
        return floatingText;
    }
    
    /**
     * 设置浮动文本预制体
     */
    static setPrefab(prefab: Prefab): void {
        FloatingText.prefab = prefab;
    }
    
    /**
     * 设置UI根节点
     * @param root UI根节点
     */
    static setUIRoot(root: Node): void {
        FloatingText.uiRoot = root;
    }
} 