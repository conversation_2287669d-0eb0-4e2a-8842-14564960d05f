import { _decorator, Button, Component, instantiate, Label, Node, Prefab, ProgressBar, resources, ScrollView, Sprite, SpriteAtlas, UIComponent, Widget } from 'cc';
const { ccclass, property } = _decorator;

export enum TaskType{
    None = 0,
    // 1-点击铜币、2-点击银币、3-点击金币
    ClickSmallCoin = 1,
    ClickMiddleCoin = 2,
    ClickLargeCoin = 3,

    // 11-购买铜币、12-升级银币、13-升级金币
    BuySmallCoin = 11,
    BuyMiddleCoin = 12,
    BuyLargeCoin = 13,

    // 21-铜币增值、22-银币增值、23-金币增值
    AdditionalSmallCoinValue = 21,
    AdditionalMiddleCoinValue = 22,
    AdditionalLargeCoinValue = 23,

    // 10-获得雇员
    GetEmployee = 30,

    // 41-使用立即翻转、42-使用紧急动员、43-使用高效工作、44-使用临时增幅
    SKILL_IMMEDIATE_PRODUCTION = 41,
    SKILL_EMERGENCY_MOBILIZATION = 42,
    SKILL_HIGH_EFFICIENT_WORK = 43,
    SKILL_TEMP_VALUE_ADD = 44,

    // 51-获得海边场景、52-获得篮球场场景、53获得仓库场景
    GetSeaScene = 51,
    GetBasketballScene = 52,
    GetRepositoryScene = 53,

    // 赚钱任务
    MakeMoney = 100,

    TaskComplete = 10000,


}

export class TaskData{

    //任务id
    taskId: number = 0;
    
    // 奖励金币数量
    rewardNum: number = 0;
    
    // 任务类型：
    taskType: TaskType = TaskType.None;

    taskDes: string = '';
    
    // 总次数
    totalCount: number = 0;

    constructor(id:number, reward:number, taskType:TaskType, des:string, totalCount:number){
        this.taskId = id;
        this.rewardNum = reward;
        this.taskType = taskType;
        this.taskDes = des;
        this.totalCount = totalCount;
    }
}