import { _decorator, Button, Color, Component, instantiate, Label, Node, Prefab, ProgressBar, resources, ScrollView, Sprite, SpriteAtlas, UIComponent, Vec3, Widget } from 'cc';
import { TaskData, TaskType } from './TaskData';
import { TaskManager } from '../../managers/TaskManager';
import { StatsContext } from '../../contexts/StatsContext';
import { SaveContext } from '../../contexts/SaveContext';
import { FloatingText } from '../floating_text/FloatingText';
import { GameEvent } from '../../event/GameEvent';
import { COIN_TYPE } from '../../items/interactables/Coin';
import { PURCHASABLE_ITEM_ID } from '../../contexts/ConfigContext';
import { AudioManager } from '../../managers/AudioManager';
import { GuideManager } from '../../managers/GuideManager';
import { DropItemManager } from '../../managers/DropItemManager';
import { TweenHelper } from '../../utils/TweenHelper';
import { NumberHelper } from '../../utils/NumberHelper';
const { ccclass, property } = _decorator;

@ccclass('Task')
export class Task extends Component {

    // 奖励金币数量
    @property(Label)
    labelReward: Label = null!;

    // 任务描述
    @property(Label)
    labelDes: Label = null!;

    // 完成进度
    @property(Label)
    labelProgress: Label = null!;

    // 指引动画
    @property(Node)
    nodeGuide: Node = null!;

    private taskData:TaskData = null!;
    
    start() {
        //完成所有任务
        if(StatsContext.mainTaskID>=StatsContext.mainTaskData.size){
            //所有任务完成
            this.node.active = false;
            return;
        }
        this.setData();

        //点击硬币
        StatsContext.events.on(GameEvent.MAIN_TASK_CLICK_COIN, this.setClickCoinProgress, this);

        //增加硬币数量
        StatsContext.events.on(`SMALL_COIN-changed`, this.onSmallCoinsChanged, this);
        StatsContext.events.on(`CHANGE_SMALL_TO_MIDDLE-changed`, this.onSmall2MiddleChanged, this);
        StatsContext.events.on(`CHANGE_MIDDLE_TO_LARGE-changed`, this.onMiddle2LargeChanged, this);

        //给硬币充能
        StatsContext.events.on(`ADDITIONAL_COIN_VALUE_SMALL-changed`, this.onAdditionalSmallCoinValueChanged, this);
        StatsContext.events.on(`ADDITIONAL_COIN_VALUE_MEDIUM-changed`, this.onAdditionalMiddleCoinValueChanged, this);
        StatsContext.events.on(`ADDITIONAL_COIN_VALUE_LARGE-changed`, this.onAdditionalLargeCoinValueChanged, this);

        //赚钱任务
        StatsContext.events.on(GameEvent.MAKE_MONEY, this.onMakeMoney, this);


        //增加雇员
        StatsContext.events.on(GameEvent.EMPLOYEE_CHANGED, this.onEmployeeChange, this);

        //使用技能
        StatsContext.events.on(GameEvent.USE_SKILL, this.onUseSkill, this);

        //获得场景
        StatsContext.events.on(GameEvent.SCENE_CHANGED, this.onSceneChanged, this);

        StatsContext.events.on(GameEvent.RESET_GAME, this.onResetGame, this);

        //新手指引
        this.setNewerGuide();
    }

    onResetGame(){
        this.setData();
        this.node.active = true;
    }

    setData(){
        this.taskData = TaskManager.instance.getMainTaskData();
        if(this.taskData){
            this.labelDes.string = this.taskData.taskDes;
            this.labelProgress.string = StatsContext.mainTaskProgress.toLocaleString() + "/" + this.taskData.totalCount.toLocaleString();
            this.labelReward.string = NumberHelper.convert_number_to_text_with_thousand(this.taskData.rewardNum);

            if(StatsContext.mainTaskProgress>=this.taskData.totalCount){
                this.labelProgress.color = new Color(50, 205, 50, 255);//绿色
            }else{
                this.labelProgress.color = new Color(119, 23, 23, 255);//红色
            }
        }
    }

    update(deltaTime: number) {
        
    }

    
    setClickCoinProgress(type:COIN_TYPE){
        if(this.taskData){
            if(type==COIN_TYPE.SMALL_COIN){
                if(this.taskData.taskType == TaskType.ClickSmallCoin){
                    this.addProgressCount();
                }
            }else if(type==COIN_TYPE.MEDIUM_COIN){
                if(this.taskData.taskType == TaskType.ClickMiddleCoin){
                    this.addProgressCount();
                }
            }else if(type==COIN_TYPE.LARGE_COIN){
                if(this.taskData.taskType == TaskType.ClickLargeCoin){
                    this.addProgressCount();
                }
            }
        }
    }

    onSmallCoinsChanged(newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.BuySmallCoin){
                this.addProgressCount();
            }
        }
    }
    onSmall2MiddleChanged(newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.BuyMiddleCoin){
                this.addProgressCount();
            }
        }
    }
    onMiddle2LargeChanged(newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.BuyLargeCoin){
                this.addProgressCount();
            }
        }
    }
    onAdditionalSmallCoinValueChanged(newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.AdditionalSmallCoinValue){
                this.addProgressCount();
            }
        }
    }
    onAdditionalMiddleCoinValueChanged(newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.AdditionalMiddleCoinValue){
                this.addProgressCount();
            }
        }
    }
    onAdditionalLargeCoinValueChanged(newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.AdditionalLargeCoinValue){
                this.addProgressCount();
            }
        }
    }

    onEmployeeChange(key:string, newValue: number, oldValue: number){
        if(newValue>oldValue){
            if(this.taskData && this.taskData.taskType==TaskType.GetEmployee){
                this.addProgressCount();
            }
        }
    }

    onUseSkill(key:string){
        if(this.taskData && this.taskData.taskType==TaskType.SKILL_IMMEDIATE_PRODUCTION && key == PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION){
            this.addProgressCount();
        }else if(this.taskData && this.taskData.taskType==TaskType.SKILL_EMERGENCY_MOBILIZATION && key == PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION){
            this.addProgressCount();
        }else if(this.taskData && this.taskData.taskType==TaskType.SKILL_HIGH_EFFICIENT_WORK && key == PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK){
            this.addProgressCount();
        }else if(this.taskData && this.taskData.taskType==TaskType.SKILL_TEMP_VALUE_ADD && key == PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD){
            this.addProgressCount();
        }
    }

    onSceneChanged(key:string, value:number){
        if(value>0){
            if(this.taskData && this.taskData.taskType==TaskType.GetSeaScene && key==PURCHASABLE_ITEM_ID.SCENE_SEASIDE.toUpperCase()){
                this.addProgressCount();
            }
        }
    }

    addProgressCount(value:number=1){
        StatsContext.mainTaskProgress += value;
        if(StatsContext.mainTaskProgress >= this.taskData.totalCount){
            StatsContext.mainTaskProgress = this.taskData.totalCount;
            //任务条件达成，可以播放特效
            this.labelProgress.color =  new Color(50, 205, 50, 255);//绿色

            this.setNewerGuide();
        }
        this.labelProgress.string = StatsContext.mainTaskProgress.toLocaleString() + "/" + this.taskData.totalCount.toLocaleString();
        SaveContext.saveGame();
    }

    //点击领取奖励
    onClick(){
        if(!this.taskData){
            //任务数据异常
            return;
        }       

        //领取奖励
        if(StatsContext.mainTaskProgress==this.taskData.totalCount){

            AudioManager.playSoundEffect('task_get_reward');

            // TODO: 调用FloatingText.create显示收益
            // 使用FloatingText显示收益
            const textPos = new Vec3(
                this.node.position.x,
                this.node.position.y + 50,
                this.node.position.z
            );

            if(this.taskData.totalCount==0){
                FloatingText.create(this.node.parent, textPos, `所有任务已完成`);
                this.node.active = false;
                return;
            }
            
            FloatingText.create(this.node.parent, textPos, `+${this.taskData.rewardNum.toLocaleString()}`, 1, 20, '#274FEC');

            StatsContext.diamond += this.taskData.rewardNum;

            // 添加掉落物品逻辑 - 掉落3个物品示例
            // if (DropItemManager.instance) {
            //     const startPos = this.node.getWorldPosition();
            //     const targetPos = new Vec3(0, -200, 0); // 目标位置，可根据需要调整
            //     DropItemManager.instance.spawnDropItems(3, startPos, targetPos);
            // }
            
            //切换下一个任务
            StatsContext.mainTaskID++;
            StatsContext.mainTaskProgress=0;
            this.setData();
            
            //主线任务
            // StatsContext.newFingerGuide++;
            if(this.taskData && (this.taskData.taskId==1||this.taskData.taskId==2||this.taskData.taskId==3)){
                GuideManager.instance.nextGuide();
            }
            StatsContext.events.emit(GameEvent.MAIN_TASK_COMPLETE_REWARD);

            // 触发保存 (可以考虑节流，避免过于频繁的保存)
            SaveContext.saveGame();

            this.setNewerGuide();
        }else{
            //完成所有任务
            if(StatsContext.mainTaskID>=StatsContext.mainTaskData.size){
                //所有任务完成
                this.node.active = false;
            }else{
                // const textPos = new Vec3(
                //     this.node.position.x,
                //     this.node.position.y + 50,
                //     this.node.position.z
                // );
                // FloatingText.create(this.node.parent, textPos, `任务未完成`);
                // AudioManager.playSoundEffect('task_cant_reward');
                
                TweenHelper.shakeIcon(this.node);
            }
        }
        
    }

    //新手，显示动画
    setNewerGuide(){
        if(StatsContext.mainTaskProgress==this.taskData.totalCount && (StatsContext.mainTaskID<4)){
            this.nodeGuide.active = true;
        }else{
            this.nodeGuide.active = false;
        }
    }

    //赚钱任务
    onMakeMoney(value:number){
        if(this.taskData && this.taskData.taskType==TaskType.MakeMoney){
            this.addProgressCount(value);
        }
    }

}


