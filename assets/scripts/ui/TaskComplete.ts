import { _decorator, Button, Component, Label, Node } from 'cc';
import { StatsContext } from '../contexts/StatsContext';
import { GameEvent } from '../event/GameEvent';
import { SkillFragmentItem } from './shop/SkillFragmentItem';
import { PURCHASABLE_ITEM_ID } from '../contexts/ConfigContext';
import { SaveContext } from '../contexts/SaveContext';
const { ccclass, property } = _decorator;

@ccclass('TaskComplete')
export class TaskComplete extends Component {

    // 广告按钮
    @property(Button)
    buttonAD: Button = null;

    // 技能碎片
    @property(Node)
    skillFragment1: Node = null;
    @property(Node)
    skillFragment2: Node = null;
    @property(Node)
    skillFragment3: Node = null;

    @property(Label)
    labelCountDown: Label = null;


    private count_down:number = 30;
    private skillFragmentItem1 :SkillFragmentItem = null;
    private skillFragmentItem2 :SkillFragmentItem = null;
    private skillFragmentItem3 :SkillFragmentItem = null;

    // 技能列表
    private allSkills: string[] = [
        PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION, 
        PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK, 
        PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION, 
        PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD];
    
    protected onLoad(): void {
        this.skillFragmentItem1 = this.skillFragment1.getComponent(SkillFragmentItem);
        this.skillFragmentItem2 = this.skillFragment2.getComponent(SkillFragmentItem);
        this.skillFragmentItem3 = this.skillFragment3.getComponent(SkillFragmentItem);
    }
    start() {
        this.buttonAD?.node.on(Button.EventType.CLICK, this.onButtonADPressed, this);
        StatsContext.events.on(GameEvent.SELECT_SKILL_FRAGMENT, this.onClose, this);
    }
    protected onEnable(): void {
        //随机选择三个技能
        this.count_down = 30;
        const selectedSkills = this.randomlySelectSkills();
        this.assignSkillsToFragments(selectedSkills);
    }
    /**
     * 随机选择三个技能
     * @returns 包含三个随机技能的数组
     */
    private randomlySelectSkills(): string[] {
        // 新手引导
        if(StatsContext.skillFragmentTaskProgress==0){
            return [PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION, PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION, PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK];
        }
        if(StatsContext.skillFragmentTaskProgress==1){
            return [PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD, PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK, PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION];
        }

        // 使用 Fisher-Yates 洗牌算法打乱数组顺序
        const shuffledSkills = [];
        for (let i = 0; i < 3; i++) {
            const j = Math.floor(Math.random() * this.allSkills.length);
            shuffledSkills.push(this.allSkills[j]);
        }
        // 截取前三个技能
        return shuffledSkills;
    }

    /**
     * 将随机选择的技能分配给技能碎片节点
     * @param skills 包含三个随机技能的数组
     */
    private assignSkillsToFragments(skills: string[]) {
        if (this.skillFragmentItem1) {
            this.skillFragmentItem1.setData(skills[0]);
        }
        if (this.skillFragmentItem2) {
            this.skillFragmentItem2.setData(skills[1]);
        }
        if (this.skillFragmentItem3) {
            this.skillFragmentItem3.setData(skills[2]);
        }
    }

    update(deltaTime: number) {
        //倒计时
        this.count_down-=deltaTime;
        this.labelCountDown.string = Math.floor(this.count_down).toString() + "s";
        if(this.count_down <= 0){
            this.count_down = 30;
            //新手
            if(StatsContext.skillFragmentTaskProgress==0){
                this.skillFragmentItem2.onButtonSelectPressed();
            }else if(StatsContext.skillFragmentTaskProgress==1){
                this.skillFragmentItem3.onButtonSelectPressed();
            }else{
                //自动选择收益最高的选项
                if(this.skillFragmentItem1.fragmentCount>=this.skillFragmentItem2.fragmentCount && this.skillFragmentItem1.fragmentCount>=this.skillFragmentItem3.fragmentCount){
                    this.skillFragmentItem1.onButtonSelectPressed();
                }else if(this.skillFragmentItem2.fragmentCount>=this.skillFragmentItem1.fragmentCount && this.skillFragmentItem2.fragmentCount>=this.skillFragmentItem3.fragmentCount){
                    this.skillFragmentItem2.onButtonSelectPressed();
                }else{
                    this.skillFragmentItem3.onButtonSelectPressed();
                }
            }
        }
    }
    onButtonADPressed(){
        //观看广告
        if (this.skillFragmentItem1) {
            this.skillFragmentItem1.onButtonSelectPressed();
        }
        if (this.skillFragmentItem2) {
            this.skillFragmentItem2.onButtonSelectPressed();
        }
        if (this.skillFragmentItem3) {
            this.skillFragmentItem3.onButtonSelectPressed();
        }

        this.onClose();
    }
    onClose(){
        //选择碎片
        this.node.active = false;
        StatsContext.skillFragmentTaskProgress++;
        SaveContext.saveGame();

    }
}


