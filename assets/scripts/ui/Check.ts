import { _decorator, Button, Component, Node } from 'cc';
import { SaveContext } from '../contexts/SaveContext';
import { StatsContext } from '../contexts/StatsContext';
import { GameEvent } from '../event/GameEvent';
import { AudioManager } from '../managers/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('Check')
export class Check extends Component {

    onClose(){
        this.node.active = false;
        AudioManager.playSoundEffect('button_close');
    }

    onResetSaveData(){
        // 重置存档
        SaveContext.resetSaveData();
        StatsContext.resetToDefault();
        SaveContext.saveGame();

        this.node.active =  false;

        //重置游戏
        StatsContext.events.emit(GameEvent.RESET_GAME, true);
        
        AudioManager.playSoundEffect('button_close');
    }
}


