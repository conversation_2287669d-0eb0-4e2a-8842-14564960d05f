import { _decorator, Component, Node, Label, ProgressBar, Button, sys } from 'cc';
import { GameEvent } from '../event/GameEvent';
import { VersionManager, VersionInfo, UpdateProgress } from '../managers/VersionManager';

const { ccclass, property } = _decorator;

/**
 * 版本更新UI组件
 * 显示版本检查和热更新的进度界面
 */
@ccclass('VersionUpdateUI')
export class VersionUpdateUI extends Component {
    @property({ type: Node, displayName: '更新面板' })
    public updatePanel: Node = null;

    @property({ type: Label, displayName: '当前版本标签' })
    public currentVersionLabel: Label = null;

    @property({ type: Label, displayName: '最新版本标签' })
    public latestVersionLabel: Label = null;

    @property({ type: Label, displayName: '更新描述标签' })
    public updateDescLabel: Label = null;

    @property({ type: Label, displayName: '进度文本标签' })
    public progressLabel: Label = null;

    @property({ type: ProgressBar, displayName: '进度条' })
    public progressBar: ProgressBar = null;

    @property({ type: Button, displayName: '立即更新按钮' })
    public updateButton: Button = null;

    @property({ type: Button, displayName: '稍后更新按钮' })
    public laterButton: Node = null;

    @property({ type: Button, displayName: '重启游戏按钮' })
    public restartButton: Node = null;

    @property({ type: Node, displayName: '检查中提示' })
    public checkingTip: Node = null;

    @property({ type: Node, displayName: '无更新提示' })
    public noUpdateTip: Node = null;

    private _currentVersionInfo: VersionInfo = null;
    private _isUpdating: boolean = false;

    protected onLoad(): void {
        this.initUI();
        this.registerEvents();
    }

    protected onDestroy(): void {
        this.unregisterEvents();
    }

    /**
     * 初始化UI
     */
    private initUI(): void {
        // 隐藏所有面板
        this.hideAllPanels();

        // 设置按钮事件
        if (this.updateButton) {
            this.updateButton.node.on(Button.EventType.CLICK, this.onUpdateButtonClick, this);
        }

        if (this.laterButton) {
            this.laterButton.on(Button.EventType.CLICK, this.onLaterButtonClick, this);
        }

        if (this.restartButton) {
            this.restartButton.on(Button.EventType.CLICK, this.onRestartButtonClick, this);
        }

        // 显示当前版本
        const versionManager = VersionManager.getInstance();
        if (versionManager && this.currentVersionLabel) {
            this.currentVersionLabel.string = `当前版本: ${versionManager.getCurrentVersion()}`;
        }
    }

    /**
     * 注册事件
     */
    private registerEvents(): void {
        GameEvent.on('version-check-start', this.onVersionCheckStart, this);
        GameEvent.on('version-check-complete', this.onVersionCheckComplete, this);
        GameEvent.on('version-check-error', this.onVersionCheckError, this);
        GameEvent.on('hot-update-start', this.onHotUpdateStart, this);
        GameEvent.on('hot-update-progress', this.onHotUpdateProgress, this);
        GameEvent.on('hot-update-complete', this.onHotUpdateComplete, this);
        GameEvent.on('hot-update-error', this.onHotUpdateError, this);
    }

    /**
     * 取消注册事件
     */
    private unregisterEvents(): void {
        GameEvent.off('version-check-start', this.onVersionCheckStart, this);
        GameEvent.off('version-check-complete', this.onVersionCheckComplete, this);
        GameEvent.off('version-check-error', this.onVersionCheckError, this);
        GameEvent.off('hot-update-start', this.onHotUpdateStart, this);
        GameEvent.off('hot-update-progress', this.onHotUpdateProgress, this);
        GameEvent.off('hot-update-complete', this.onHotUpdateComplete, this);
        GameEvent.off('hot-update-error', this.onHotUpdateError, this);
    }

    /**
     * 版本检查开始
     */
    private onVersionCheckStart(data: any): void {
        console.log('[VersionUpdateUI] 版本检查开始');
        this.showCheckingTip();
    }

    /**
     * 版本检查完成
     */
    private onVersionCheckComplete(data: any): void {
        console.log('[VersionUpdateUI] 版本检查完成:', data);

        if (data.hasUpdate) {
            this._currentVersionInfo = data.versionInfo;
            this.showUpdatePanel(data.versionInfo, data.updateType);
        } else {
            this.showNoUpdateTip();
        }
    }

    /**
     * 版本检查错误
     */
    private onVersionCheckError(data: any): void {
        console.error('[VersionUpdateUI] 版本检查失败:', data.error);
        this.showError('版本检查失败，请检查网络连接');
    }

    /**
     * 热更新开始
     */
    private onHotUpdateStart(data: any): void {
        console.log('[VersionUpdateUI] 热更新开始:', data);
        this._isUpdating = true;
        this.showUpdateProgress('准备更新...', 0);
    }

    /**
     * 热更新进度
     */
    private onHotUpdateProgress(progress: UpdateProgress): void {
        console.log('[VersionUpdateUI] 热更新进度:', progress);
        
        let message = progress.message || '';
        if (progress.phase === 'downloading') {
            message = `下载中 (${progress.completedFiles}/${progress.totalFiles}): ${progress.currentFile || ''}`;
        } else if (progress.phase === 'installing') {
            message = '安装中...';
        }

        this.showUpdateProgress(message, progress.progress);
    }

    /**
     * 热更新完成
     */
    private onHotUpdateComplete(data: any): void {
        console.log('[VersionUpdateUI] 热更新完成:', data);
        this._isUpdating = false;

        if (data.needRestart) {
            this.showRestartButton('更新完成，需要重启游戏');
        } else {
            this.showSuccess('更新完成！');
            this.scheduleOnce(() => {
                this.hideAllPanels();
            }, 2);
        }
    }

    /**
     * 热更新错误
     */
    private onHotUpdateError(data: any): void {
        console.error('[VersionUpdateUI] 热更新失败:', data.error);
        this._isUpdating = false;
        this.showError(`更新失败: ${data.error}`);
    }

    /**
     * 显示检查中提示
     */
    private showCheckingTip(): void {
        this.hideAllPanels();
        if (this.checkingTip) {
            this.checkingTip.active = true;
        }
    }

    /**
     * 显示无更新提示
     */
    private showNoUpdateTip(): void {
        this.hideAllPanels();
        if (this.noUpdateTip) {
            this.noUpdateTip.active = true;
            this.scheduleOnce(() => {
                this.noUpdateTip.active = false;
            }, 2);
        }
    }

    /**
     * 显示更新面板
     */
    private showUpdatePanel(versionInfo: VersionInfo, updateType: string): void {
        this.hideAllPanels();
        
        if (!this.updatePanel) return;

        this.updatePanel.active = true;

        // 设置版本信息
        if (this.latestVersionLabel) {
            this.latestVersionLabel.string = `最新版本: ${versionInfo.version}`;
        }

        if (this.updateDescLabel) {
            this.updateDescLabel.string = versionInfo.description || '发现新版本';
        }

        // 设置按钮显示
        if (this.updateButton) {
            this.updateButton.node.active = true;
            const buttonLabel = this.updateButton.node.getComponentInChildren(Label);
            if (buttonLabel) {
                buttonLabel.string = updateType === 'mandatory' ? '立即更新' : '更新';
            }
        }

        if (this.laterButton) {
            this.laterButton.active = updateType !== 'mandatory';
        }

        // 隐藏进度相关UI
        if (this.progressBar) {
            this.progressBar.node.active = false;
        }
        if (this.progressLabel) {
            this.progressLabel.node.active = false;
        }
        if (this.restartButton) {
            this.restartButton.active = false;
        }
    }

    /**
     * 显示更新进度
     */
    private showUpdateProgress(message: string, progress: number): void {
        if (!this.updatePanel) return;

        this.updatePanel.active = true;

        // 隐藏按钮
        if (this.updateButton) {
            this.updateButton.node.active = false;
        }
        if (this.laterButton) {
            this.laterButton.active = false;
        }
        if (this.restartButton) {
            this.restartButton.active = false;
        }

        // 显示进度
        if (this.progressBar) {
            this.progressBar.node.active = true;
            this.progressBar.progress = progress / 100;
        }

        if (this.progressLabel) {
            this.progressLabel.node.active = true;
            this.progressLabel.string = `${message} (${progress}%)`;
        }
    }

    /**
     * 显示重启按钮
     */
    private showRestartButton(message: string): void {
        if (this.progressLabel) {
            this.progressLabel.string = message;
        }

        if (this.restartButton) {
            this.restartButton.active = true;
        }

        if (this.progressBar) {
            this.progressBar.node.active = false;
        }
    }

    /**
     * 显示成功信息
     */
    private showSuccess(message: string): void {
        if (this.progressLabel) {
            this.progressLabel.string = message;
        }

        if (this.progressBar) {
            this.progressBar.progress = 1;
        }
    }

    /**
     * 显示错误信息
     */
    private showError(message: string): void {
        if (this.progressLabel) {
            this.progressLabel.node.active = true;
            this.progressLabel.string = message;
        }

        if (this.progressBar) {
            this.progressBar.node.active = false;
        }

        // 显示重试按钮
        if (this.updateButton) {
            this.updateButton.node.active = true;
            const buttonLabel = this.updateButton.node.getComponentInChildren(Label);
            if (buttonLabel) {
                buttonLabel.string = '重试';
            }
        }
    }

    /**
     * 隐藏所有面板
     */
    private hideAllPanels(): void {
        if (this.updatePanel) {
            this.updatePanel.active = false;
        }
        if (this.checkingTip) {
            this.checkingTip.active = false;
        }
        if (this.noUpdateTip) {
            this.noUpdateTip.active = false;
        }
    }

    /**
     * 更新按钮点击
     */
    private onUpdateButtonClick(): void {
        if (this._isUpdating) {
            return;
        }

        if (!this._currentVersionInfo) {
            // 重新检查版本
            const versionManager = VersionManager.getInstance();
            if (versionManager) {
                versionManager.checkForUpdates();
            }
            return;
        }

        // 开始更新
        const versionManager = VersionManager.getInstance();
        if (versionManager) {
            versionManager.performHotUpdate(this._currentVersionInfo);
        }
    }

    /**
     * 稍后更新按钮点击
     */
    private onLaterButtonClick(): void {
        this.hideAllPanels();
        
        // 记录用户选择稍后更新
        sys.localStorage.setItem('update_later_timestamp', Date.now().toString());
    }

    /**
     * 重启游戏按钮点击
     */
    private onRestartButtonClick(): void {
        // 重启游戏
        cc.game.restart();
    }

    /**
     * 手动检查更新
     */
    public checkForUpdates(): void {
        const versionManager = VersionManager.getInstance();
        if (versionManager) {
            versionManager.checkForUpdates();
        }
    }

    /**
     * 显示更新UI
     */
    public show(): void {
        this.node.active = true;
        this.checkForUpdates();
    }

    /**
     * 隐藏更新UI
     */
    public hide(): void {
        this.node.active = false;
        this.hideAllPanels();
    }
}
