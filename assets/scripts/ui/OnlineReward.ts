import { _decorator, Component, Label, Node, Sprite, tween, v3, Vec3 } from 'cc';
import { TimeHelper } from '../utils/TimeHelper';
import { FloatingText } from './floating_text/FloatingText';
import { StatsContext } from '../contexts/StatsContext';
import { SaveContext } from '../contexts/SaveContext';
import { UIManager } from '../managers/UIManager';
import { TweenHelper } from '../utils/TweenHelper';
const { ccclass, property } = _decorator;

@ccclass('OnlineReward')
export class OnlineReward extends Component {

    @property(Node)
    nodeReddot: Node = null;

    @property(Label)
    labelTime: Label = null;

    private online_time: number = 0;
    private readonly ONLINE_TIME: number = 2 * 5;//在线5分钟，可领取一次奖励
    private readonly ONLINE_REWARD_LIMIT: number = 100; //在线奖励次数限制

    private canGetReward: boolean = false;

    onLoad() {
    }

    start() {
        this.checkShow();
        this.initStatus();

    }

    update(deltaTime: number) {
        this.online_time -= deltaTime;
        if (this.online_time <= 0) {
            this.nodeReddot.active = true;
            this.labelTime.string = "领取奖励";
            this.canGetReward = true;
        } else {
            this.labelTime.string = TimeHelper.secondsToMS(this.online_time);
            this.canGetReward = false;
        }
    }

    initStatus() {
        this.online_time = this.ONLINE_TIME;
        this.nodeReddot.active = false;
        this.canGetReward = false;
    }

    getRewawrd() {
        if (this.canGetReward) {
            //领取奖励
            const coinValue = 100 * (StatsContext.onlineRewardCount + 1);
            StatsContext.diamond += coinValue;
            StatsContext.onlineRewardCount++;
            SaveContext.saveGame();
            
            // TODO: 调用FloatingText.create显示收益
            const textPos = new Vec3(
            this.node.position.x - 50,
            this.node.position.y + 50,
            this.node.position.z
        );
            FloatingText.create(this.node.parent, textPos, `+${coinValue.toLocaleString()}`, 1, 20, '#274FEC');

            this.initStatus();
            this.checkShow();
        }else{
            //震动图标的动画
            TweenHelper.shakeIcon(this.node);
        }
    }

    checkShow() {
        //是否显示在线奖励
        if (StatsContext.onlineRewardCount >= this.ONLINE_REWARD_LIMIT) {
            this.nodeReddot.active = false;
        }
    }

    
}


