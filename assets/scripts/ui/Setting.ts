import { _decorator, Button, Component, Label, Node, ProgressBar, Slider, Sprite } from 'cc';
import { StatsContext } from '../contexts/StatsContext';
import { GameEvent } from '../event/GameEvent';
import { AudioManager } from '../managers/AudioManager';
import { SaveContext } from '../contexts/SaveContext';
import { UIManager } from '../managers/UIManager';
const { ccclass, property } = _decorator;

@ccclass('Setting')
export class Setting extends Component {

    // 音乐
    @property(Node)
    nodeMusic: Node = null;
    @property(Button)
    bottonMusicOn: Button = null;
    @property(Button)
    buttonMusicOff: Button = null;

    // 音效
    @property(Node)
    nodeSound: Node = null;
    @property(Button)
    buttonSoundOn: Button = null;
    @property(Button)
    buttonSoundOff: Button = null;

    private sliderMusic: Slider = null;
    private progressMusic:ProgressBar = null;
    private musicVolume :number = 0;

    private sliderSound: Slider = null;
    private progressSound:ProgressBar = null;
    private soundVolume:number = 0;

    onLoad(){
        this.sliderMusic = this.nodeMusic.getComponent(Slider);
        this.progressMusic = this.nodeMusic.getComponent(ProgressBar);

        this.sliderSound = this.nodeSound.getComponent(Slider);
        this.progressSound = this.nodeSound.getComponent(ProgressBar);
    }

    
    start() {
        StatsContext.events.on(GameEvent.RESET_GAME, this.onButtonClosePressed, this);

        //读取当前音量
        this.musicVolume = AudioManager.bgmVolume;
        this.sliderMusic.progress = AudioManager.bgmVolume;
        this.progressMusic.progress = AudioManager.bgmVolume;
        this.bottonMusicOn.node.active = this.sliderMusic.progress>0;
        this.buttonMusicOff.node.active = this.sliderMusic.progress<=0;

        this.soundVolume = AudioManager.sfxVolume;
        this.sliderSound.progress = AudioManager.sfxVolume;
        this.progressSound.progress = AudioManager.sfxVolume;
        this.buttonSoundOn.node.active = this.sliderSound.progress>0;
        this.buttonSoundOff.node.active = this.sliderSound.progress<=0;

    }

    onButtonClosePressed(){
        this.node.active = false;
        AudioManager.playSoundEffect('button_close');
    }

    //音乐
    onSliderMusicSlide(){
        this.musicVolume = this.sliderMusic.progress;
        AudioManager.bgmVolume = this.sliderMusic.progress;
        this.progressMusic.progress = this.sliderMusic.progress;
        this.bottonMusicOn.node.active = this.sliderMusic.progress>0;
        this.buttonMusicOff.node.active = this.sliderMusic.progress<=0;
        SaveContext.saveGame();
    }
    onMusicOnButtonClick(){
        AudioManager.playSoundEffect('button_click');
        AudioManager.bgmVolume = 0;
        this.sliderMusic.progress = 0;
        this.progressMusic.progress = 0;
        this.bottonMusicOn.node.active = false;
        this.buttonMusicOff.node.active = true;
        SaveContext.saveGame();
    }
    onMusicOffButtonClick(){
        AudioManager.playSoundEffect('button_click');
        AudioManager.bgmVolume = this.musicVolume;
        this.sliderMusic.progress = this.musicVolume;
        this.progressMusic.progress = this.musicVolume;
        this.bottonMusicOn.node.active = true;
        this.buttonMusicOff.node.active = false;
        SaveContext.saveGame();
    }

    //音效
    onSliderSoundSlide() {
        this.soundVolume = this.sliderSound.progress;
        console.log('音效音量:', this.soundVolume);
        // 在这里添加处理音效音量的逻辑
        AudioManager.sfxVolume = this.sliderSound.progress;
        this.progressSound.progress = this.sliderSound.progress;
        this.buttonSoundOn.node.active = this.sliderSound.progress>0;
        this.buttonSoundOff.node.active = this.sliderSound.progress<=0;
        SaveContext.saveGame();
    }

    onSoundOnButtonClick(){
        AudioManager.playSoundEffect('button_click');
        AudioManager.sfxVolume = 0;
        this.sliderSound.progress = 0;
        this.progressSound.progress = 0;
        this.buttonSoundOn.node.active = false;
        this.buttonSoundOff.node.active = true;
        SaveContext.saveGame();
    }
    onSoundOffButtonClick(){
        AudioManager.playSoundEffect('button_click');
        AudioManager.sfxVolume = this.soundVolume;
        this.sliderSound.progress = this.soundVolume;
        this.progressSound.progress = this.soundVolume;
        this.buttonSoundOn.node.active = true;
        this.buttonSoundOff.node.active = false;
        SaveContext.saveGame();
    }

    showCheck(){
        AudioManager.playSoundEffect('button_open');
        UIManager.instance.showCheck();
    }

}


