import { _decorator, Button, Component, Node } from 'cc';
import { SaveContext } from '../contexts/SaveContext';
import { StatsContext } from '../contexts/StatsContext';
import { GameEvent } from '../event/GameEvent';
import { AudioManager } from '../managers/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('Welcome')
export class Welcome extends Component {

    protected onLoad(): void {
        this.node.active = StatsContext.newFingerGuide==0;
    }

    protected onEnable(): void {
        this.node.active = StatsContext.newFingerGuide==0;
    }

    protected start(): void {
        this.node.active = StatsContext.newFingerGuide==0;
    }

    onClose(){
        this.node.active = false;
        AudioManager.playSoundEffect('button_close');
    }

}


