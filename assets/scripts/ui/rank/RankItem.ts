import { _decorator, Button, Color, Component, Enum, Label, Node, RichText, Sprite, SpriteFrame, Texture2D, animation, Animation, AnimationClip, assetManager, ImageAsset } from 'cc';
import { TimeHelper } from '../../utils/TimeHelper';

const { ccclass, property } = _decorator;

@ccclass('RankItemData')
export class RankItemData{
    rank:number;
    name:string;
    address:string;
    time:number;
    head:string;
}

@ccclass('RankItem')
export class RankItem extends Component {
    //背景
    @property(Sprite)
    spriteBgDefault: Sprite = null;
    @property(Sprite)
    spriteBg1: Sprite = null;
    @property(Sprite)
    spriteBg2: Sprite = null;
    @property(Sprite)
    spriteBg3: Sprite = null;
    @property(Sprite)
    spriteBgSelect: Sprite = null;

    //排名
    @property(Label)
    labelRank: Label = null;
    @property(Sprite)
    spriteRank1: Sprite = null;
    @property(Sprite)
    spriteRank2: Sprite = null;
    @property(Sprite)
    spriteRank3: Sprite = null;
    @property(Label)
    labelRankNo: Label = null;

    //头像
    @property(Sprite)
    spriteHead: Sprite = null;

    // 名称
    @property(Label)
    labelName: Label = null;
    
    // ip
    @property(Label)
    labelAddress: Label = null;

    // 时间
    @property(Label)
    labelTime: Label = null;

    // 是否自己
    @property
    isMe: boolean = false;

    // private cache: { [name: string]: SpriteFrame } = {};

    onLoad() {
        // if(!this.isMe){
        //     this.node.on(Node.EventType.TOUCH_START, this.onClick, this);
        // }
    }

    protected onDestroy(): void {
        // if(!this.isMe){
        //     this.node.off(Node.EventType.TOUCH_START, this.onClick, this);
        // }
    }

    start() {
        if(this.isMe){
            this.spriteBgDefault.node.active = false;
        }
    }
    setData(index:number, data:RankItemData=null){
        if(data==null){
            this.spriteHead.node.active = false;
            this.labelRankNo.node.active = true;
        }else{
            this.spriteHead.node.active = true;
            this.labelRankNo.node.active = false;

            this.labelName.string = data.name;
            this.labelAddress.string = data.address;
            this.labelTime.string = TimeHelper.secondsToDHMS(data.time);
            let self = this;
            assetManager.loadRemote<ImageAsset>(data.head, (err, imageAsset) => {
                if (!err && imageAsset) {
                    // let spFrame = this.cache[imageAsset.name];
                    // if (!spFrame) {            
                        const texture = new Texture2D();
                        texture.image = imageAsset;
                        let spFrame = new SpriteFrame();
                        spFrame.texture = texture;
                        imageAsset.addRef();            
                        // this.cache[imageAsset.name] = spFrame; // 添加映射表记录
                    // }
                    spFrame.addRef(); // 计数加1
                    self.spriteHead.spriteFrame = spFrame;
                }
            })
        }
        switch(index){
            case 1:
                this.spriteBg1.node.active = true;
                this.spriteRank1.node.active = true;
                break;
            case 2:
                this.spriteBg2.node.active = true;
                this.spriteRank2.node.active = true;
                break;
            case 3:
                this.spriteBg3.node.active = true;
                this.spriteRank3.node.active = true;
                break;
            default:
                this.labelRank.string = "#"+index.toString();
                break;
        }
        if(this.isMe && index>100){
            this.labelRank.string = "未上榜";
        }
    }
    
}  