import { _decorator, Button, Component, instantiate, Label, Node, Prefab, ProgressBar, resources, ScrollView, Sprite, SpriteAtlas, UIComponent, Widget } from 'cc';
import { RankItem } from './RankItem';
import { AudioManager } from '../../managers/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('Rank')
export class Rank extends Component {

    // 关闭按钮
    @property(Button)
    buttonClose: Button = null;

    // 预制体
    @property(Prefab)
    rankItemPrefab: Prefab = null!;

    // 滚动列表
    @property(Node)
    scrollViewContent: Node = null!;

    // me
    @property(RankItem)
    rankItemMe: RankItem = null!;


    start() {
        this.buttonClose.node.on(Button.EventType.CLICK, this.onClose, this);
        this.setData();
    }

    update(deltaTime: number) {
        
    }

    setData(){
        for(let i=1;i<=10;i++){
            const coinNode = instantiate(this.rankItemPrefab);
            const coinComponent = coinNode.getComponent(RankItem);
            coinComponent.setData(i);
            this.scrollViewContent.addChild(coinNode);
        }
        
        this.rankItemMe.setData(4);
    }
    onClose(){
        this.node.active = false;
        AudioManager.playSoundEffect('button_close');
    }
}


