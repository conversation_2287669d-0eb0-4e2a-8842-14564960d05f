import { _decorator, CCInteger, Component, Label, Node, Sprite } from 'cc';
import { ConfigContext, PURCHASABLE_ITEM_ID, PurchasableItemConfig } from '../../contexts/ConfigContext';
import { StatsContext } from '../../contexts/StatsContext';
import { GameEvent } from '../../event/GameEvent';
const { ccclass, property } = _decorator;

@ccclass('SkillIconCooldown')
export class SkillIconCooldown extends Component {
    // 技能冷却时间（秒）
    @property({ type: CCInteger, tooltip: '技能冷却时间（秒）' })
    cooldownTime: number = 10;

    // 技能图标 Sprite 组件
    @property(Sprite)
    skillIconSprite: Sprite = null;

    // 技能生效图标
    @property(Label)
    labelSkillValidity: Label = null;
    
    // 配置ID
    @property
    idKey: string = '';

    // 剩余冷却时间
    private _remainingTime: number = 0;

    //技能生效时间
    private _validityTime : number = 0;

    // 技能是否在冷却中
    private _isCooldown: boolean = false;

    // 配置
    private _config: PurchasableItemConfig = null;
    get config(): PurchasableItemConfig {
        return this._config;
    }

    onLoad() {
        this._config = ConfigContext.getPurchasableItemConfig(this.idKey.toLowerCase() as PURCHASABLE_ITEM_ID);
        if (!this._config) {
            console.warn(`[SkillIconCooldown] 配置不存在: ${this.idKey} (尝试使用 ${this.idKey.toLowerCase()})`);
        } else {
            console.log(`[SkillIconCooldown] 配置初始化成功: ${this.idKey}`);
        }
        if(this.labelSkillValidity)this.labelSkillValidity.node.active =  false;

        StatsContext.events.on(GameEvent.SKILL_LEVEL_CHANGED, this.onSkillLevelChanged, this);
    }
    
    start() {
        const level = StatsContext.getSkill(this.idKey);
        if(level>0){
            this.skillIconSprite.fillRange = 0;
            // this.resetCooldown();//技能释放结束再cd
            this.releaseSkill();
        }
    }
    onSkillLevelChanged(key:string, value:number, oleValue:number){
        // if(key == this.idKey && oleValue==0){
        //     //第一次升级立即触发技能
        //     // this.resetCooldown();//技能结束再cd
        //     this.releaseSkill();
        // }
    }

    update(deltaTime: number) {
        if (this._isCooldown) {
            // 更新剩余冷却时间
            this._remainingTime -= deltaTime;
            if (this._remainingTime <= 0) {
                // 冷却结束，释放技能
                // this.resetCooldown();//技能结束再cd
                this.releaseSkill();
            } else {
                // 更新技能图标遮罩显示冷却进度
                const fillRatio = this._remainingTime / this.cooldownTime;
                this.skillIconSprite.fillRange = fillRatio;
            }
        }
        if(this._validityTime>0){
            this._validityTime -= deltaTime;
            if(this.labelSkillValidity)this.labelSkillValidity.string = Math.floor(this._validityTime).toString();
            if(this._validityTime<=0){
                //技能结束
                if(this.labelSkillValidity)this.labelSkillValidity.node.active =  false;
                //发送通知
                StatsContext.events.emit(GameEvent.USE_SKILL_FUNISH, this.idKey);
                //开始cd
                this.resetCooldown();
            }
        }
    }

    /**
     * 使用技能并启动冷却倒计时
     */
    // useSkill() {
    //     if (!this._isCooldown) {
    //         // 标记技能进入冷却
    //         this._isCooldown = true;
    //         this._remainingTime = this.cooldownTime;
    //         this.skillIconSprite.fillRange = 1;
    //         console.log('技能已使用，开始冷却'+this.idKey);
    //         //释放技能
    //         switch(this.idKey){
    //             case PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION://立即生产
    //                 StatsContext.events.emit(GameEvent.USE_SKILL, this.idKey);
    //             break;
    //             case  PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD://临时增值
    //                 this._validityTime = this.config.price_expressions.base;
    //                 if(this.labelSkillValidity)this.labelSkillValidity.node.active =  true;
    //             break;
    //         }
    //     } else {
    //         console.log('技能正在冷却中');
    //     }
    // }

    /**
     * 重置冷却状态
     */
    resetCooldown() {
        this._isCooldown = true;
        this.cooldownTime = this.getCoodDown();
        this._remainingTime = this.cooldownTime;
        this.skillIconSprite.fillRange = 0;
        console.log(`[SkillIconCooldown] cd开始计时: ${this.idKey}, 倒计时: ${this.cooldownTime}`);
    }
    getCoodDown():number{
        if(!this.config)return 0;
        
        const baseTime = this.config.skill.cd;
        const level = StatsContext.getSkill(this.idKey);
        const reduceTime = level*this.config.skill.cd_addition;
        return baseTime-reduceTime;
    }

    /**
     * 释放技能的方法，可根据实际需求修改
     */
    releaseSkill() {
        if(!this.config)return;

        this._isCooldown = false;
        console.log('技能已自动释放'+this.idKey);
         if(this.idKey!=PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION){
            const level = StatsContext.getSkill(this.idKey);
            this._validityTime = this.config.skill.validity_time + this.config.skill.validity_time_multiplier*level;
            if(this.labelSkillValidity)this.labelSkillValidity.node.active =  true;
            if(this.labelSkillValidity)this.labelSkillValidity.string = Math.floor(this._validityTime).toString();
         }
        // 这里添加实际的技能释放逻辑
        StatsContext.events.emit(GameEvent.USE_SKILL, this.idKey);

        //立即生产后直接CD
        if(this.idKey==PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION){
            this.resetCooldown();
        }
    }
}


