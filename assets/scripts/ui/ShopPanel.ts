// import { _decorator, Component, Node, Button, Label, instantiate, Prefab, resources, director, Enum } from 'cc';
// import { StatsContext } from '../contexts/StatsContext';
// import { ConfigContext } from '../contexts/ConfigContext';
// import { AudioManager } from '../managers/AudioManager';

// const { ccclass, property } = _decorator;

// /**
//  * 商店物品类型枚举
//  */
// export enum SHOP_ITEM_TYPE {
//     SMALL_COIN = 0,
//     MEDIUM_COIN = 1,
//     LARGE_COIN = 2,
//     HELPER = 3,
//     DICE = 4
// }

// Enum(SHOP_ITEM_TYPE);

// /**
//  * 商店面板组件
//  * 负责显示和管理商店界面
//  */
// @ccclass('ShopPanel')
// export class ShopPanel extends Component {
//     // 商店物品按钮
//     @property([Node])
//     itemButtons: Node[] = [];

//     // 价格标签
//     @property([Label])
//     priceLabels: Label[] = [];

//     // 物品类型
//     @property({ type: [SHOP_ITEM_TYPE] })
//     itemTypes: SHOP_ITEM_TYPE[] = [];

//     // 商店物品预制体
//     @property(Prefab)
//     smallCoinPrefab: Prefab = null;

//     @property(Prefab)
//     mediumCoinPrefab: Prefab = null;

//     @property(Prefab)
//     largeCoinPrefab: Prefab = null;

//     @property(Prefab)
//     helperPrefab: Prefab = null;

//     @property(Prefab)
//     dicePrefab: Prefab = null;

//     // 关闭按钮
//     @property(Node)
//     closeButton: Node = null;

//     // 物品价格
//     private _prices: number[] = [];

//     // StatsContext 实例
//     private _statsContext: StatsContext = null;

//     onLoad() {
//         // 获取 StatsContext 实例
//         this._statsContext = StatsContext.instance;

//         // 初始化价格
//         this._initPrices();

//         // 设置价格标签
//         this._updatePriceLabels();

//         // 注册按钮事件
//         this._registerButtonEvents();
//     }

//     start() {
//         // 播放打开动画
//         this._playOpenAnimation();
//     }

//     /**
//      * 初始化物品价格
//      */
//     private _initPrices() {
//         this._prices = [];

//         for (let i = 0; i < this.itemTypes.length; i++) {
//             let price = 0;

//             switch (this.itemTypes[i]) {
//                 case SHOP_ITEM_TYPE.SMALL_COIN:
//                     price = ConfigContext.getValueByFullKey('small_coin_price') || 10;
//                     break;
//                 case SHOP_ITEM_TYPE.MEDIUM_COIN:
//                     price = ConfigContext.getValueByFullKey('medium_coin_price') || 50;
//                     break;
//                 case SHOP_ITEM_TYPE.LARGE_COIN:
//                     price = ConfigContext.getValueByFullKey('large_coin_price') || 200;
//                     break;
//                 case SHOP_ITEM_TYPE.HELPER:
//                     price = ConfigContext.getValueByFullKey('helper_price') || 500;
//                     break;
//                 case SHOP_ITEM_TYPE.DICE:
//                     price = ConfigContext.getValueByFullKey('dice_price') || 1000;
//                     break;
//             }

//             this._prices.push(price);
//         }
//     }

//     /**
//      * 更新价格标签
//      */
//     private _updatePriceLabels() {
//         for (let i = 0; i < this.priceLabels.length && i < this._prices.length; i++) {
//             if (this.priceLabels[i]) {
//                 this.priceLabels[i].string = this._prices[i].toFixed(0);
//             }
//         }
//     }

//     /**
//      * 注册按钮事件
//      */
//     private _registerButtonEvents() {
//         // 注册物品按钮点击事件
//         for (let i = 0; i < this.itemButtons.length; i++) {
//             const button = this.itemButtons[i].getComponent(Button);
//             const itemType = this.itemTypes[i];
//             const price = this._prices[i];

//             if (button) {
//                 button.node.on('click', () => {
//                     this._onItemButtonClicked(itemType, price);
//                 });
//             }
//         }

//         // 注册关闭按钮点击事件
//         if (this.closeButton) {
//             const closeButtonComp = this.closeButton.getComponent(Button);
//             if (closeButtonComp) {
//                 closeButtonComp.node.on('click', this._onCloseButtonClicked, this);
//             }
//         }
//     }

//     /**
//      * 物品按钮点击回调
//      */
//     private _onItemButtonClicked(itemType: SHOP_ITEM_TYPE, price: number) {
//         if (!this._statsContext) return;

//         // 检查玩家是否有足够的金币
//         const currentCoins = this._statsContext.getCoins();
//         if (currentCoins < price) {
//             console.log('金币不足');
//             AudioManager.playSoundEffect('error');
//             return;
//         }

//         // 扣除金币
//         this._statsContext.consumeCoins(price);

//         // 创建物品
//         this._createItem(itemType);

//         // 播放购买音效
//         AudioManager.playSoundEffect('purchase');
//     }

//     /**
//      * 创建物品
//      */
//     private _createItem(itemType: SHOP_ITEM_TYPE) {
//         let prefab: Prefab = null;

//         switch (itemType) {
//             case SHOP_ITEM_TYPE.SMALL_COIN:
//                 prefab = this.smallCoinPrefab;
//                 break;
//             case SHOP_ITEM_TYPE.MEDIUM_COIN:
//                 prefab = this.mediumCoinPrefab;
//                 break;
//             case SHOP_ITEM_TYPE.LARGE_COIN:
//                 prefab = this.largeCoinPrefab;
//                 break;
//             case SHOP_ITEM_TYPE.HELPER:
//                 prefab = this.helperPrefab;
//                 break;
//             case SHOP_ITEM_TYPE.DICE:
//                 prefab = this.dicePrefab;
//                 break;
//         }

//         if (prefab) {
//             // 实例化物品
//             const item = instantiate(prefab);

//             // 获取当前场景
//             const scene = director.getScene();
//             if (scene) {
//                 // 添加到场景
//                 scene.addChild(item);

//                 // 设置位置
//                 item.setPosition(0, 0, 0);
//             }
//         }
//     }

//     /**
//      * 关闭按钮点击回调
//      */
//     private _onCloseButtonClicked() {
//         // 播放关闭动画
//         this._playCloseAnimation();

//         // 延迟销毁
//         this.scheduleOnce(() => {
//             this.node.destroy();
//         }, 0.3);
//     }

//     /**
//      * 播放打开动画
//      */
//     private _playOpenAnimation() {
//         // 设置初始缩放
//         this.node.scale.set(0.5, 0.5, 1);

//         // 创建缩放动画
//         this.node.scale.set(1, 1, 1);
//     }

//     /**
//      * 播放关闭动画
//      */
//     private _playCloseAnimation() {
//         // 创建缩放动画
//         this.node.scale.set(0, 0, 1);
//     }
// }