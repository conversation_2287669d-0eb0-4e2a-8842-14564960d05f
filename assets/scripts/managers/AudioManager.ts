import { _decorator, Component, AudioClip, AudioSource, game, resources, director, Director } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 音频管理器，负责播放和控制游戏中的所有音频
 */
@ccclass('AudioManager')
export class AudioManager extends Component {
    // 单例实例
    private static _instance: AudioManager = null;

    // 音频设置
    private static _bgmVolume: number = 0.2;
    private static _sfxVolume: number = 1.0;
    private static _bgmEnabled: boolean = true;
    private static _sfxEnabled: boolean = true;

    // 当前背景音乐
    private _currentBGM: string = null;
    private _bgmSource: AudioSource = null;
    private _sfxSources: AudioSource[] = [];

    // 预加载的音频剪辑
    @property([AudioClip])
    bgmClips: AudioClip[] = [];

    @property([AudioClip])
    sfxClips: AudioClip[] = [];

    // 音频映射表
    private _bgmMap: Map<string, AudioClip> = new Map();
    private _sfxMap: Map<string, AudioClip> = new Map();

    // 单例访问器
    static get instance(): AudioManager {
        return this._instance;
    }

    // 音量访问器
    static get bgmVolume(): number {
        return this._bgmVolume;
    }

    static set bgmVolume(value: number) {
        this._bgmVolume = Math.max(0, Math.min(1, value));
        if (this._instance && this._instance._bgmSource) {
            this._instance._bgmSource.volume = this._bgmVolume;
        }
    }

    static get sfxVolume(): number {
        return this._sfxVolume;
    }

    static set sfxVolume(value: number) {
        this._sfxVolume = Math.max(0, Math.min(1, value));
        if (this._instance) {
            this._instance._sfxSources.forEach(source => {
                source.volume = this._sfxVolume;
            });
        }
    }

    // 启用/禁用音频
    static get bgmEnabled(): boolean {
        return this._bgmEnabled;
    }

    static set bgmEnabled(value: boolean) {
        this._bgmEnabled = value;
        if (this._instance && this._instance._bgmSource) {
            if (value) {
                if (this._instance._currentBGM) {
                    this._instance._bgmSource.play();
                }
            } else {
                this._instance._bgmSource.pause();
            }
        }
    }

    static get sfxEnabled(): boolean {
        return this._sfxEnabled;
    }

    static set sfxEnabled(value: boolean) {
        this._sfxEnabled = value;
    }

    // 生命周期方法
    onLoad() {
        // 单例模式
        if (AudioManager._instance !== null) {
            this.destroy();
            return;
        }

        AudioManager._instance = this;
        game.addPersistRootNode(this.node);

        // 初始化音频源
        this._bgmSource = this.getComponent(AudioSource) || this.addComponent(AudioSource);
        this._bgmSource.loop = true;
        this._bgmSource.volume = AudioManager._bgmVolume;

        // 初始化音频映射
        this.initAudioMaps();

        // 监听场景切换事件
        director.on(Director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneChanged, this);
    }

    onDestroy() {
        if (AudioManager._instance === this) {
            AudioManager._instance = null;
            director.off(Director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneChanged, this);
        }
    }

    /**
     * 初始化音频映射表
     */
    private initAudioMaps() {
        // 添加预设音频剪辑
        this.bgmClips.forEach(clip => {
            if (clip) {
                this._bgmMap.set(clip.name, clip);
            }
        });

        this.sfxClips.forEach(clip => {
            if (clip) {
                this._sfxMap.set(clip.name, clip);
            }
        });
    }

    /**
     * 场景切换回调
     * 在场景切换后确保音频状态的正确性
     */
    private onSceneChanged() {
        // 确保背景音乐在场景切换后继续播放
        if (this._currentBGM && AudioManager._bgmEnabled && this._bgmSource) {
            if (!this._bgmSource.playing) {
                this._bgmSource.play();
            }
        }

        // 清理不再使用的音效源
        this._sfxSources = this._sfxSources.filter(source => {
            if (!source.playing && this._sfxSources.length > 5) {
                this.node.removeComponent(source);
                return false;
            }
            return true;
        });
    }

    /**
     * 播放背景音乐
     * @param name 音乐名称
     * @param force 是否强制重新播放
     */
    static playBGM(name: string, force: boolean = false): void {
        if (!this._bgmEnabled || !this._instance) return;

        if (this._instance._currentBGM === name && !force) return;

        this._instance._currentBGM = name;

        // 获取音频剪辑
        let clip = this._instance._bgmMap.get(name);

        if (clip) {
            this._instance._bgmSource.stop();
            this._instance._bgmSource.clip = clip;
            this._instance._bgmSource.play();
        } else {
            // 动态加载音频
            resources.load(`audio/bgm/${name}`, AudioClip, (err, clip) => {
                if (err) {
                    console.error(`加载音频失败: ${name}`, err);
                    return;
                }

                this._instance._bgmMap.set(name, clip);

                if (this._instance._currentBGM === name) {
                    this._instance._bgmSource.stop();
                    this._instance._bgmSource.clip = clip;
                    this._instance._bgmSource.play();
                }
            });
        }
    }

    /**
     * 停止播放背景音乐
     */
    static stopBGM(): void {
        if (!this._instance || !this._instance._bgmSource) return;

        this._instance._bgmSource.stop();
        this._instance._currentBGM = null;
    }

    /**
     * 播放音效
     * @param name 音效名称
     * @param volume 音量，默认为1
     */
    static playSoundEffect(name: string, volume: number = 1.0): void {
        if (!this._sfxEnabled || !this._instance) return;

        // 获取音频剪辑
        let clip = this._instance._sfxMap.get(name);

        if (clip) {
            this._instance.playSFX(clip, volume);
        } else {
            // 动态加载音频
            resources.load(`audio/sfx/${name}`, AudioClip, (err, clip) => {
                if (err) {
                    console.error(`加载音频失败: ${name}`, err);
                    return;
                }

                this._instance._sfxMap.set(name, clip);
                this._instance.playSFX(clip, volume);
            });
        }
    }

    /**
     * 播放音效的内部方法
     */
    private playSFX(clip: AudioClip, volume: number): void {
        // 创建或重用音频源
        let source = this.getIdleSFXSource();
        source.clip = clip;
        source.volume = volume * AudioManager._sfxVolume;
        source.loop = false;
        source.play();
    }

    /**
     * 获取空闲的音效音频源
     */
    private getIdleSFXSource(): AudioSource {
        // 检查现有音频源
        for (let source of this._sfxSources) {
            if (!source.playing) {
                return source;
            }
        }

        // 创建新的音频源
        const source = this.node.addComponent(AudioSource);
        this._sfxSources.push(source);
        return source;
    }

    /**
     * 停止所有音效
     */
    static stopAllSFX(): void {
        if (!this._instance) return;

        this._instance._sfxSources.forEach(source => {
            source.stop();
        });
    }
}