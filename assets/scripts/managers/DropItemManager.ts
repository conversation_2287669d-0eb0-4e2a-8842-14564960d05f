import { _decorator, Component, instantiate, Node, Prefab, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('DropItemManager')
export class DropItemManager extends Component {

    private static _instance: DropItemManager = null;
    public static get instance(): DropItemManager {
        return this._instance;
    }

    @property(Prefab)
    public itemPrefab: Prefab = null; // 掉落物品预制体

    onLoad() {
        if (DropItemManager._instance) {
            this.destroy();
            return;
        }
        DropItemManager._instance = this;
    }

    /**
     * 生成掉落物品
     * @param count 掉落数量
     * @param startPos 起始位置
     * @param targetPos 目标位置
     * @param moveTime 移动时间(秒)
     */
    spawnDropItems(count: number, startPos: Vec3, targetPos: Vec3, moveTime: number = 1.5) {
        for (let i = 0; i < count; i++) {
            // 实例化物品
            const itemNode = instantiate(this.itemPrefab);
            itemNode.setParent(this.node);
            
            // 随机偏移起始位置
            const offset = new Vec3(
                (Math.random() - 0.5) * 100,
                (Math.random() - 0.5) * 100,
                0
            );
            itemNode.setPosition(startPos.clone().add(offset));

            // 移动到目标位置后消失
            tween(itemNode)
                .to(moveTime, { position: targetPos }, { easing: 'quadOut' })
                .call(() => {
                    itemNode.destroy();
                })
                .start();
        }
    }
}


