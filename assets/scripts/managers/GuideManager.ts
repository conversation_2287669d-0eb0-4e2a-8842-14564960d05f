import { _decorator, Component, Node, Label, Prefab, ScrollView } from 'cc';
import { StatsContext } from '../contexts/StatsContext';
import { TaskData, TaskType } from '../ui/task/TaskData';
import { GameEvent } from '../event/GameEvent';
const { ccclass, property } = _decorator;

/**
 * 新手指引管理器
 */
@ccclass('GuideManager')
export class GuideManager extends Component {
    // 单例实例
    private static _instance: GuideManager = null;

    // 单例访问器
    static get instance(): GuideManager {
        return this._instance;
    }
    // 新手步骤
    // 0 点击铜币
    // 1 领取主线任务1奖励
    // 2 购买铜币
    // 3 领取任务2奖励
    // 4 购买铜币充能
    // 5 领取任务3奖励
    // 6 点击雇员页签
    // 7 点击场景页签

    // 教学动画
    @property(Node)
    nodeGuide_2: Node = null;
    @property(Node)
    nodeGuide_3: Node = null;
    @property(Node)
    nodeGuide_13: Node = null;
    @property(Node)
    nodeGuide_14: Node = null;
    @property(Node)
    nodeGuide_18: Node = null;
    @property(ScrollView)
    scrollView: ScrollView = null;

    onLoad() {
        // 单例模式
        if (GuideManager._instance !== null) {
            // 已存在实例，销毁当前节点
            if (this.node) {
                this.node.parent = null;
            }
            return;
        }

        GuideManager._instance = this;
    }

    start() {
        StatsContext.events.on(GameEvent.MAIN_TASK_COMPLETE_REWARD, this.onMainTaskCompleteReward, this);
        StatsContext.events.on(GameEvent.RESET_GAME, this.onResetGame, this);

        this.hideGuide_13();
        this.hideGuide_14();
        this.hideGuide_18();
        this.onMainTaskCompleteReward();
    }

    onResetGame(){
        this.hideGuide_2();
        this.hideGuide_3();
        this.hideGuide_13();
        this.hideGuide_14();
        this.hideGuide_18();
    }
    
    onMainTaskCompleteReward(){
        if(StatsContext.mainTaskID==13 && StatsContext.newFingerGuide==5){
            this.showGuide_13();
        }
        else if(StatsContext.mainTaskID==14 && StatsContext.newFingerGuide==6){
            this.showGuide_14();
        }
        else if(StatsContext.mainTaskID==18 && StatsContext.newFingerGuide==7){
            this.showGuide_18();
        }
    }

    onDestroy() {
        if (GuideManager._instance === this) {
            GuideManager._instance = null;
        }
    }

    nextGuide(){
        StatsContext.newFingerGuide++;
        StatsContext.events.emit(GameEvent.GUIDE_CHANGED);
    }

    showGuide_2(){
        if(this.nodeGuide_2){
            this.scrollView.scrollToLeft();
            this.scrollView.enabled = false;
            this.nodeGuide_2.active = true;
        }
    }
    hideGuide_2(){
        if(this.nodeGuide_2){
            this.scrollView.enabled = true;
            this.nodeGuide_2.active = false;
        }
    }
    showGuide_3(){
        if(this.nodeGuide_3){
            this.scrollView.scrollToLeft();
            this.scrollView.enabled = false;
            this.nodeGuide_3.active = true;
        }
    }
    hideGuide_3(){
        if(this.nodeGuide_3){
            this.scrollView.enabled = true;
            this.nodeGuide_3.active = false;
        }
    }

    showGuide_13(){
        if(this.nodeGuide_13){
            this.scrollView.scrollToLeft();
            this.scrollView.enabled = false;
            this.nodeGuide_13.active = true;
        }
    }
    hideGuide_13(){
        if(this.nodeGuide_13){
            this.scrollView.enabled = true;
            this.nodeGuide_13.active = false;
        }
    }

    showGuide_14(){
        if(this.nodeGuide_14){
            this.scrollView.enabled = false;
            this.nodeGuide_14.active = true;
        }
    }
    hideGuide_14(){
        if(this.nodeGuide_14){
            this.scrollView.enabled = true;
            this.nodeGuide_14.active = false;
        }
    }

    showGuide_18(){
        if(this.nodeGuide_18){
            this.scrollView.enabled = false;
            this.nodeGuide_18.active = true;
        }
    }
    hideGuide_18(){
        if(this.nodeGuide_18){
            this.scrollView.enabled = true;
            this.nodeGuide_18.active = false;
        }
    }

}