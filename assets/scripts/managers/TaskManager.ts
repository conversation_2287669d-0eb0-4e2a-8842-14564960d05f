import { _decorator, Component, Node, Label, Prefab } from 'cc';
import { StatsContext } from '../contexts/StatsContext';
import { TaskData, TaskType } from '../ui/task/TaskData';
const { ccclass, property } = _decorator;

/**
 * 任务管理器，负责管理游戏的所有任务
 */
@ccclass('TaskManager')
export class TaskManager extends Component {
    // 单例实例
    private static _instance: TaskManager = null;

    // 单例访问器
    static get instance(): TaskManager {
        return this._instance;
    }

    onLoad() {
        // 单例模式
        if (TaskManager._instance !== null) {
            // 已存在实例，销毁当前节点
            if (this.node) {
                this.node.parent = null;
            }
            return;
        }

        TaskManager._instance = this;

        // 初始化任务数据
        this.initTaskData();
    }

    start() {
        
    }

    onDestroy() {
        if (TaskManager._instance === this) {
            TaskManager._instance = null;
        }

    }

    /**
     * 初始化任务数据
     */
    private initTaskData() {

        //新手任务
        StatsContext.mainTaskData.set(1,    new TaskData(1,     20, TaskType.ClickSmallCoin, "点击铜币", 10));
        StatsContext.mainTaskData.set(2,    new TaskData(2,     30, TaskType.BuySmallCoin, "购买铜币", 3));
        StatsContext.mainTaskData.set(3,    new TaskData(3,     50, TaskType.AdditionalSmallCoinValue, "铜币增值", 1));
        StatsContext.mainTaskData.set(4,    new TaskData(4,     100, TaskType.ClickSmallCoin, "点击铜币", 10));
        StatsContext.mainTaskData.set(5,    new TaskData(5,     200, TaskType.BuyMiddleCoin, "转铜为银", 2));
        StatsContext.mainTaskData.set(6,    new TaskData(6,     300, TaskType.ClickMiddleCoin, "点击银币", 10));
        StatsContext.mainTaskData.set(7,    new TaskData(7,     400, TaskType.AdditionalMiddleCoinValue, "银币增值", 1));
        StatsContext.mainTaskData.set(8,    new TaskData(8,     500, TaskType.ClickMiddleCoin, "点击银币", 10));
        StatsContext.mainTaskData.set(9,    new TaskData(9,     600, TaskType.BuyLargeCoin, "点银成金", 1));
        StatsContext.mainTaskData.set(10,   new TaskData(10,    700, TaskType.ClickLargeCoin, "点击金币", 10));
        StatsContext.mainTaskData.set(11,   new TaskData(11,    800, TaskType.AdditionalLargeCoinValue, "金币增值", 1));
        StatsContext.mainTaskData.set(12,   new TaskData(12,    900, TaskType.ClickLargeCoin, "点击金币", 10));
        StatsContext.mainTaskData.set(13,   new TaskData(13,    1000, TaskType.GetEmployee, "获得员工", 1));
        StatsContext.mainTaskData.set(14,   new TaskData(14,    2000, TaskType.SKILL_IMMEDIATE_PRODUCTION, "立即生产", 1));
        StatsContext.mainTaskData.set(15,   new TaskData(15,    3000, TaskType.SKILL_EMERGENCY_MOBILIZATION, "紧急动员", 1));
        StatsContext.mainTaskData.set(16,   new TaskData(16,    4000, TaskType.SKILL_HIGH_EFFICIENT_WORK, "高效工作", 1));
        StatsContext.mainTaskData.set(17,   new TaskData(17,    5000, TaskType.SKILL_TEMP_VALUE_ADD, "临时增值", 1));
        StatsContext.mainTaskData.set(18,   new TaskData(18,    6000, TaskType.GetSeaScene, "获得海边场景", 1));

        //赚钱任务
        StatsContext.mainTaskData.set(19,   new TaskData(19,    100,           TaskType.MakeMoney,     "赚一万",        10000));
        StatsContext.mainTaskData.set(20,   new TaskData(20,    1000,          TaskType.MakeMoney,     "赚十万",        100000));
        StatsContext.mainTaskData.set(21,   new TaskData(21,    2000,          TaskType.MakeMoney,     "赚二十万",      200000));
        StatsContext.mainTaskData.set(22,   new TaskData(22,    3000,          TaskType.MakeMoney,     "赚三十万",      300000));
        StatsContext.mainTaskData.set(23,   new TaskData(23,    4000,          TaskType.MakeMoney,     "赚四十万",      400000));
        StatsContext.mainTaskData.set(24,   new TaskData(24,    5000,          TaskType.MakeMoney,     "赚五十万",      500000));
        StatsContext.mainTaskData.set(25,   new TaskData(25,    6000,          TaskType.MakeMoney,     "赚六十万",      600000));
        StatsContext.mainTaskData.set(26,   new TaskData(26,    7000,          TaskType.MakeMoney,     "赚七十万",      700000));
        StatsContext.mainTaskData.set(27,   new TaskData(27,    8000,          TaskType.MakeMoney,     "赚八十万",      800000));
        StatsContext.mainTaskData.set(28,   new TaskData(28,    9000,          TaskType.MakeMoney,     "赚九十万",      900000));
        StatsContext.mainTaskData.set(29,   new TaskData(29,    10000,         TaskType.MakeMoney,     "赚一百万",      1000000));
        StatsContext.mainTaskData.set(30,   new TaskData(30,    20000,         TaskType.MakeMoney,     "赚二百万",      2000000));
        StatsContext.mainTaskData.set(31,   new TaskData(31,    30000,         TaskType.MakeMoney,     "赚三百万",      3000000));
        StatsContext.mainTaskData.set(32,   new TaskData(32,    40000,         TaskType.MakeMoney,     "赚四百万",      4000000));
        StatsContext.mainTaskData.set(33,   new TaskData(33,    50000,         TaskType.MakeMoney,     "赚五百万",      5000000));
        StatsContext.mainTaskData.set(34,   new TaskData(34,    60000,         TaskType.MakeMoney,     "赚六百万",      6000000));
        StatsContext.mainTaskData.set(35,   new TaskData(35,    70000,         TaskType.MakeMoney,     "赚七百万",      7000000));
        StatsContext.mainTaskData.set(36,   new TaskData(36,    80000,         TaskType.MakeMoney,     "赚八百万",      8000000));
        StatsContext.mainTaskData.set(37,   new TaskData(37,    90000,         TaskType.MakeMoney,     "赚九百万",      9000000));
        StatsContext.mainTaskData.set(38,   new TaskData(38,    100000,        TaskType.MakeMoney,     "赚一千万",      10000000));
        StatsContext.mainTaskData.set(39,   new TaskData(39,    200000,        TaskType.MakeMoney,     "赚二千万",      20000000));
        StatsContext.mainTaskData.set(40,   new TaskData(40,    300000,        TaskType.MakeMoney,     "赚三千万",      30000000));
        StatsContext.mainTaskData.set(41,   new TaskData(41,    400000,        TaskType.MakeMoney,     "赚四千万",      40000000));
        StatsContext.mainTaskData.set(42,   new TaskData(42,    500000,        TaskType.MakeMoney,     "赚五千万",      50000000));
        StatsContext.mainTaskData.set(43,   new TaskData(43,    600000,        TaskType.MakeMoney,     "赚六千万",      60000000));
        StatsContext.mainTaskData.set(44,   new TaskData(44,    700000,        TaskType.MakeMoney,     "赚七千万",      70000000));
        StatsContext.mainTaskData.set(45,   new TaskData(45,    800000,        TaskType.MakeMoney,     "赚八千万",      80000000));
        StatsContext.mainTaskData.set(46,   new TaskData(46,    900000,        TaskType.MakeMoney,     "赚九千万",      90000000));
        StatsContext.mainTaskData.set(47,   new TaskData(47,    1000000,       TaskType.MakeMoney,     "赚一亿",        100000000));
        StatsContext.mainTaskData.set(48,   new TaskData(48,    2000000,       TaskType.MakeMoney,     "赚二亿",       200000000));
        StatsContext.mainTaskData.set(49,   new TaskData(49,    3000000,       TaskType.MakeMoney,     "赚三亿",       300000000));
        StatsContext.mainTaskData.set(50,   new TaskData(50,    4000000,       TaskType.MakeMoney,     "赚四亿",       400000000));
        StatsContext.mainTaskData.set(51,   new TaskData(51,    5000000,       TaskType.MakeMoney,     "赚五亿",       500000000));
        StatsContext.mainTaskData.set(52,   new TaskData(52,    6000000,       TaskType.MakeMoney,     "赚六亿",       600000000));
        StatsContext.mainTaskData.set(53,   new TaskData(53,    7000000,       TaskType.MakeMoney,     "赚七亿",       700000000));
        StatsContext.mainTaskData.set(54,   new TaskData(54,    8000000,       TaskType.MakeMoney,     "赚八亿",       800000000));
        StatsContext.mainTaskData.set(55,   new TaskData(55,    9000000,       TaskType.MakeMoney,     "赚九亿",       900000000));
        StatsContext.mainTaskData.set(56,   new TaskData(56,    10000000,      TaskType.MakeMoney,     "赚十亿",       1000000000));
        StatsContext.mainTaskData.set(57,   new TaskData(57,    20000000,      TaskType.MakeMoney,     "赚二十亿",     2000000000));
        StatsContext.mainTaskData.set(58,   new TaskData(58,    30000000,      TaskType.MakeMoney,     "赚三十亿",     3000000000));
        StatsContext.mainTaskData.set(59,   new TaskData(59,    40000000,      TaskType.MakeMoney,     "赚四十亿",     4000000000));
        StatsContext.mainTaskData.set(60,   new TaskData(60,    50000000,      TaskType.MakeMoney,     "赚五十亿",     5000000000));
        StatsContext.mainTaskData.set(61,   new TaskData(61,    60000000,      TaskType.MakeMoney,     "赚六十亿",     6000000000));
        StatsContext.mainTaskData.set(62,   new TaskData(62,    70000000,      TaskType.MakeMoney,     "赚七十亿",     7000000000));
        StatsContext.mainTaskData.set(63,   new TaskData(63,    80000000,      TaskType.MakeMoney,     "赚八十亿",     8000000000));
        StatsContext.mainTaskData.set(64,   new TaskData(64,    90000000,      TaskType.MakeMoney,     "赚九十亿",     9000000000));
        StatsContext.mainTaskData.set(65,   new TaskData(65,    100000000,     TaskType.MakeMoney,     "赚一百亿",     10000000000));
        StatsContext.mainTaskData.set(66,   new TaskData(66,    200000000,     TaskType.MakeMoney,     "赚二百亿",     20000000000));
        StatsContext.mainTaskData.set(67,   new TaskData(67,    300000000,     TaskType.MakeMoney,     "赚三百亿",     30000000000));
        StatsContext.mainTaskData.set(68,   new TaskData(68,    400000000,     TaskType.MakeMoney,     "赚四百亿",     40000000000));
        StatsContext.mainTaskData.set(69,   new TaskData(69,    500000000,     TaskType.MakeMoney,     "赚五百亿",     50000000000));
        StatsContext.mainTaskData.set(70,   new TaskData(70,    600000000,     TaskType.MakeMoney,     "赚六百亿",     60000000000));
        StatsContext.mainTaskData.set(71,   new TaskData(71,    700000000,     TaskType.MakeMoney,     "赚七百亿",     70000000000));
        StatsContext.mainTaskData.set(72,   new TaskData(72,    800000000,     TaskType.MakeMoney,     "赚八百亿",     80000000000));
        StatsContext.mainTaskData.set(73,   new TaskData(73,    900000000,     TaskType.MakeMoney,     "赚九百亿",     90000000000));
        StatsContext.mainTaskData.set(74,   new TaskData(74,    1000000000,    TaskType.MakeMoney,     "赚一兆",       100000000000));
        StatsContext.mainTaskData.set(75,   new TaskData(75,    2000000000,    TaskType.MakeMoney,     "赚二兆",       200000000000));
        StatsContext.mainTaskData.set(76,   new TaskData(76,    3000000000,    TaskType.MakeMoney,     "赚三兆",       300000000000));
        StatsContext.mainTaskData.set(77,   new TaskData(77,    4000000000,    TaskType.MakeMoney,     "赚四兆",       400000000000));
        StatsContext.mainTaskData.set(78,   new TaskData(78,    5000000000,    TaskType.MakeMoney,     "赚五兆",       500000000000));
        StatsContext.mainTaskData.set(79,   new TaskData(79,    6000000000,    TaskType.MakeMoney,     "赚六兆",       600000000000)); 
        StatsContext.mainTaskData.set(80,   new TaskData(80,    7000000000,    TaskType.MakeMoney,     "赚七兆",       700000000000));
        StatsContext.mainTaskData.set(81,   new TaskData(81,    8000000000,    TaskType.MakeMoney,     "赚八兆",       800000000000));
        StatsContext.mainTaskData.set(82,   new TaskData(82,    9000000000,    TaskType.MakeMoney,     "赚九兆",       900000000000));
        StatsContext.mainTaskData.set(83,   new TaskData(83,    10000000000,   TaskType.MakeMoney,     "赚十兆",       1000000000000));
        StatsContext.mainTaskData.set(84,   new TaskData(84,    20000000000,   TaskType.MakeMoney,     "赚二十兆",     2000000000000));
        StatsContext.mainTaskData.set(85,   new TaskData(85,    30000000000,   TaskType.MakeMoney,     "赚三十兆",     3000000000000));
        StatsContext.mainTaskData.set(86,   new TaskData(86,    40000000000,   TaskType.MakeMoney,     "赚四十兆",     4000000000000));
        StatsContext.mainTaskData.set(87,   new TaskData(87,    50000000000,   TaskType.MakeMoney,     "赚五十兆",     5000000000000));
        StatsContext.mainTaskData.set(88,   new TaskData(88,    60000000000,   TaskType.MakeMoney,     "赚六十兆",     6000000000000));    
        StatsContext.mainTaskData.set(89,   new TaskData(89,    70000000000,   TaskType.MakeMoney,     "赚七十兆",     7000000000000));
        StatsContext.mainTaskData.set(90,   new TaskData(90,    80000000000,   TaskType.MakeMoney,     "赚八十兆",     8000000000000));
        StatsContext.mainTaskData.set(91,   new TaskData(91,    90000000000,   TaskType.MakeMoney,     "赚九十兆",     9000000000000));
        StatsContext.mainTaskData.set(92,   new TaskData(92,    100000000000,  TaskType.MakeMoney,     "赚一百兆",     10000000000000));
        StatsContext.mainTaskData.set(93,   new TaskData(93,    200000000000,  TaskType.MakeMoney,     "赚二百兆",     20000000000000));
        StatsContext.mainTaskData.set(94,   new TaskData(94,    300000000000,  TaskType.MakeMoney,     "赚三百兆",     30000000000000));
        StatsContext.mainTaskData.set(95,   new TaskData(95,    400000000000,  TaskType.MakeMoney,     "赚四百兆",     40000000000000));
        StatsContext.mainTaskData.set(96,   new TaskData(96,    500000000000,  TaskType.MakeMoney,     "赚五百兆",     50000000000000));
        StatsContext.mainTaskData.set(97,   new TaskData(97,    600000000000,  TaskType.MakeMoney,     "赚六百兆",     60000000000000));
        StatsContext.mainTaskData.set(98,   new TaskData(98,    700000000000,  TaskType.MakeMoney,     "赚七百兆",     70000000000000));
        StatsContext.mainTaskData.set(99,   new TaskData(99,    800000000000,  TaskType.MakeMoney,     "赚八百兆",     80000000000000));
        StatsContext.mainTaskData.set(100,  new TaskData(100,   900000000000,  TaskType.MakeMoney,     "赚九百兆",     90000000000000));
        StatsContext.mainTaskData.set(101,  new TaskData(101,   1000000000000, TaskType.MakeMoney,     "赚一千兆",     100000000000000));

        StatsContext.mainTaskData.set(102,  new TaskData(102,   0,              TaskType.TaskComplete,  "所有任务已完成",   0));
    }

    public getMainTaskData():TaskData{
        if(StatsContext.mainTaskID < 1){
            StatsContext.mainTaskID = 1;
        }else if(StatsContext.mainTaskID >= StatsContext.mainTaskData.size){
            StatsContext.mainTaskID = StatsContext.mainTaskData.size;
        }
        return StatsContext.mainTaskData.get(StatsContext.mainTaskID);
    }
}