import { _decorator, Component, sys, director, game } from 'cc';
import { GameEvent } from '../event/GameEvent';

const { ccclass, property } = _decorator;

/**
 * 版本信息接口
 */
export interface VersionInfo {
    version: string;
    buildNumber: number;
    releaseDate: string;
    description: string;
    mandatory: boolean;
    downloadUrl?: string;
    fileSize?: number;
    checksum?: string;
    resources: ResourceInfo[];
    hotfixUrl?: string;
}

/**
 * 资源信息接口
 */
export interface ResourceInfo {
    path: string;
    url: string;
    size: number;
    checksum: string;
    version: string;
    priority: number;
    type: 'script' | 'asset' | 'config' | 'texture';
}

/**
 * 更新进度信息
 */
export interface UpdateProgress {
    phase: 'checking' | 'downloading' | 'installing' | 'complete' | 'error';
    progress: number; // 0-100
    currentFile?: string;
    totalFiles?: number;
    completedFiles?: number;
    message?: string;
}

/**
 * Cocos Creator 3.x 版本管理器
 * 负责版本检测、热更新和资源加载
 */
@ccclass('VersionManager')
export class VersionManager extends Component {
    @property({ displayName: '更新服务器URL' })
    public updateServerUrl: string = 'https://api.luckycoin.com/version';

    @property({ displayName: '启用版本检查' })
    public enableVersionCheck: boolean = true;

    @property({ displayName: '启用热更新' })
    public enableHotUpdate: boolean = true;

    @property({ displayName: '检查间隔(秒)' })
    public checkInterval: number = 300; // 5分钟

    private static _instance: VersionManager = null;
    private _currentVersion: string = '1.0.0';
    private _isChecking: boolean = false;
    private _isUpdating: boolean = false;
    private _checkTimer: number = 0;

    public static getInstance(): VersionManager {
        return VersionManager._instance;
    }

    protected onLoad(): void {
        if (VersionManager._instance === null) {
            VersionManager._instance = this;
            director.addPersistRootNode(this.node);
            this.initVersionManager();
        } else {
            this.node.destroy();
        }
    }

    protected onDestroy(): void {
        if (VersionManager._instance === this) {
            VersionManager._instance = null;
        }
    }

    /**
     * 初始化版本管理器
     */
    private async initVersionManager(): Promise<void> {
        try {
            console.log('[VersionManager] 初始化版本管理器...');
            
            // 加载本地版本信息
            await this.loadLocalVersion();
            
            // 如果启用版本检查，开始定期检查
            if (this.enableVersionCheck) {
                this.startVersionCheck();
            }

            console.log('[VersionManager] 版本管理器初始化完成', {
                currentVersion: this._currentVersion,
                enableVersionCheck: this.enableVersionCheck,
                enableHotUpdate: this.enableHotUpdate
            });

            // 发送初始化完成事件
            GameEvent.emit('version-manager-ready', {
                version: this._currentVersion
            });

        } catch (error) {
            console.error('[VersionManager] 初始化失败:', error);
            GameEvent.emit('version-manager-error', { error });
        }
    }

    /**
     * 加载本地版本信息
     */
    private async loadLocalVersion(): Promise<void> {
        try {
            const versionData = sys.localStorage.getItem('game_version_info');
            if (versionData) {
                const versionInfo = JSON.parse(versionData);
                this._currentVersion = versionInfo.version || '1.0.0';
            } else {
                // 创建默认版本信息
                const defaultVersion: VersionInfo = {
                    version: '1.0.0',
                    buildNumber: 1,
                    releaseDate: new Date().toISOString(),
                    description: '初始版本',
                    mandatory: false,
                    resources: []
                };
                this.saveLocalVersion(defaultVersion);
            }
        } catch (error) {
            console.error('[VersionManager] 加载本地版本信息失败:', error);
            this._currentVersion = '1.0.0';
        }
    }

    /**
     * 保存本地版本信息
     */
    private saveLocalVersion(versionInfo: VersionInfo): void {
        try {
            sys.localStorage.setItem('game_version_info', JSON.stringify(versionInfo));
            this._currentVersion = versionInfo.version;
            console.log('[VersionManager] 本地版本信息已保存:', versionInfo.version);
        } catch (error) {
            console.error('[VersionManager] 保存本地版本信息失败:', error);
        }
    }

    /**
     * 开始版本检查
     */
    private startVersionCheck(): void {
        // 立即检查一次
        this.checkForUpdates();
        
        // 设置定期检查
        this.schedule(this.checkForUpdates.bind(this), this.checkInterval);
    }

    /**
     * 检查版本更新
     */
    public async checkForUpdates(): Promise<{
        hasUpdate: boolean;
        versionInfo?: VersionInfo;
        updateType: 'none' | 'optional' | 'mandatory';
    }> {
        if (this._isChecking) {
            console.log('[VersionManager] 正在检查更新中...');
            return { hasUpdate: false, updateType: 'none' };
        }

        this._isChecking = true;

        try {
            console.log('[VersionManager] 检查版本更新...', {
                currentVersion: this._currentVersion,
                platform: sys.platform
            });

            // 发送检查开始事件
            GameEvent.emit('version-check-start', {
                currentVersion: this._currentVersion
            });

            // 构建请求参数
            const params = {
                platform: sys.platform,
                currentVersion: this._currentVersion,
                deviceId: this.getDeviceId(),
                timestamp: Date.now()
            };

            // 发送请求到服务器
            const response = await this.httpRequest(`${this.updateServerUrl}/check`, 'GET', params);
            
            if (!response || !response.success) {
                throw new Error('服务器响应异常');
            }

            const latestVersion: VersionInfo = response.data;

            // 比较版本号
            const hasUpdate = this.compareVersions(latestVersion.version, this._currentVersion) > 0;

            if (!hasUpdate) {
                console.log('[VersionManager] 当前已是最新版本');
                GameEvent.emit('version-check-complete', {
                    hasUpdate: false,
                    currentVersion: this._currentVersion
                });
                return { hasUpdate: false, updateType: 'none' };
            }

            const updateType = latestVersion.mandatory ? 'mandatory' : 'optional';

            console.log('[VersionManager] 发现新版本:', {
                currentVersion: this._currentVersion,
                latestVersion: latestVersion.version,
                updateType,
                description: latestVersion.description
            });

            // 发送检查完成事件
            GameEvent.emit('version-check-complete', {
                hasUpdate: true,
                versionInfo: latestVersion,
                updateType
            });

            return {
                hasUpdate: true,
                versionInfo: latestVersion,
                updateType
            };

        } catch (error) {
            console.error('[VersionManager] 检查版本更新失败:', error);
            GameEvent.emit('version-check-error', { error: error.message });
            return { hasUpdate: false, updateType: 'none' };
        } finally {
            this._isChecking = false;
        }
    }

    /**
     * 执行热更新
     */
    public async performHotUpdate(versionInfo: VersionInfo): Promise<boolean> {
        if (this._isUpdating) {
            console.log('[VersionManager] 正在更新中...');
            return false;
        }

        if (!this.enableHotUpdate) {
            console.log('[VersionManager] 热更新已禁用');
            return false;
        }

        this._isUpdating = true;

        try {
            console.log('[VersionManager] 开始热更新:', {
                fromVersion: this._currentVersion,
                toVersion: versionInfo.version
            });

            // 发送更新开始事件
            GameEvent.emit('hot-update-start', {
                fromVersion: this._currentVersion,
                toVersion: versionInfo.version
            });

            // 1. 下载资源文件
            await this.downloadResources(versionInfo.resources);

            // 2. 验证文件完整性
            await this.verifyResources(versionInfo.resources);

            // 3. 应用更新
            await this.applyUpdate(versionInfo);

            // 4. 更新本地版本信息
            this.saveLocalVersion(versionInfo);

            console.log('[VersionManager] 热更新完成:', versionInfo.version);

            // 发送更新完成事件
            GameEvent.emit('hot-update-complete', {
                version: versionInfo.version,
                needRestart: versionInfo.mandatory
            });

            // 如果是强制更新，重启游戏
            if (versionInfo.mandatory) {
                this.scheduleOnce(() => {
                    game.restart();
                }, 2);
            }

            return true;

        } catch (error) {
            console.error('[VersionManager] 热更新失败:', error);
            GameEvent.emit('hot-update-error', { error: error.message });
            return false;
        } finally {
            this._isUpdating = false;
        }
    }

    /**
     * 下载资源文件
     */
    private async downloadResources(resources: ResourceInfo[]): Promise<void> {
        const totalFiles = resources.length;
        let completedFiles = 0;

        // 按优先级排序
        const sortedResources = resources.sort((a, b) => a.priority - b.priority);

        // 发送下载开始事件
        this.emitUpdateProgress({
            phase: 'downloading',
            progress: 0,
            totalFiles,
            completedFiles: 0,
            message: '开始下载资源...'
        });

        for (const resource of sortedResources) {
            try {
                console.log('[VersionManager] 下载资源:', resource.path);
                
                await this.downloadSingleResource(resource);
                completedFiles++;

                const progress = Math.round((completedFiles / totalFiles) * 100);
                this.emitUpdateProgress({
                    phase: 'downloading',
                    progress,
                    currentFile: resource.path,
                    totalFiles,
                    completedFiles,
                    message: `下载中: ${resource.path}`
                });

            } catch (error) {
                console.error('[VersionManager] 资源下载失败:', resource.path, error);
                throw new Error(`资源下载失败: ${resource.path}`);
            }
        }
    }

    /**
     * 下载单个资源文件
     */
    private async downloadSingleResource(resource: ResourceInfo): Promise<void> {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', resource.url, true);
            xhr.responseType = 'arraybuffer';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    // 保存到本地存储
                    const key = `resource_${resource.path}_${resource.version}`;
                    const base64Data = this.arrayBufferToBase64(xhr.response);
                    sys.localStorage.setItem(key, base64Data);
                    resolve();
                } else {
                    reject(new Error(`HTTP ${xhr.status}`));
                }
            };

            xhr.onerror = () => reject(new Error('网络错误'));
            xhr.ontimeout = () => reject(new Error('请求超时'));
            xhr.timeout = 30000; // 30秒超时

            xhr.send();
        });
    }

    /**
     * 验证资源文件完整性
     */
    private async verifyResources(resources: ResourceInfo[]): Promise<void> {
        this.emitUpdateProgress({
            phase: 'installing',
            progress: 0,
            message: '验证文件完整性...'
        });

        for (let i = 0; i < resources.length; i++) {
            const resource = resources[i];
            const key = `resource_${resource.path}_${resource.version}`;
            const data = sys.localStorage.getItem(key);

            if (!data) {
                throw new Error(`资源文件不存在: ${resource.path}`);
            }

            // 这里可以添加校验和验证逻辑
            // const checksum = this.calculateChecksum(data);
            // if (checksum !== resource.checksum) {
            //     throw new Error(`资源文件校验失败: ${resource.path}`);
            // }

            const progress = Math.round(((i + 1) / resources.length) * 100);
            this.emitUpdateProgress({
                phase: 'installing',
                progress,
                message: `验证中: ${resource.path}`
            });
        }
    }

    /**
     * 应用更新
     */
    private async applyUpdate(versionInfo: VersionInfo): Promise<void> {
        console.log('[VersionManager] 应用更新:', versionInfo.version);
        
        this.emitUpdateProgress({
            phase: 'complete',
            progress: 100,
            message: '更新完成'
        });

        // 触发资源更新事件
        GameEvent.emit('resources-updated', {
            version: versionInfo.version,
            resources: versionInfo.resources
        });
    }

    /**
     * 发送更新进度事件
     */
    private emitUpdateProgress(progress: UpdateProgress): void {
        GameEvent.emit('hot-update-progress', progress);
    }

    /**
     * 获取当前版本
     */
    public getCurrentVersion(): string {
        return this._currentVersion;
    }

    /**
     * 获取设备ID
     */
    private getDeviceId(): string {
        let deviceId = sys.localStorage.getItem('device_id');
        if (!deviceId) {
            deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sys.localStorage.setItem('device_id', deviceId);
        }
        return deviceId;
    }

    /**
     * HTTP请求封装
     */
    private httpRequest(url: string, method: string = 'GET', params?: any): Promise<any> {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            if (method === 'GET' && params) {
                const queryString = Object.keys(params)
                    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
                    .join('&');
                url += '?' + queryString;
            }

            xhr.open(method, url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.timeout = 10000; // 10秒超时

            xhr.onload = () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('响应解析失败'));
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            };

            xhr.onerror = () => reject(new Error('网络错误'));
            xhr.ontimeout = () => reject(new Error('请求超时'));

            if (method === 'POST' && params) {
                xhr.send(JSON.stringify(params));
            } else {
                xhr.send();
            }
        });
    }

    /**
     * 比较版本号
     */
    private compareVersions(version1: string, version2: string): number {
        const v1Parts = version1.split('.').map(Number);
        const v2Parts = version2.split('.').map(Number);
        
        const maxLength = Math.max(v1Parts.length, v2Parts.length);
        
        for (let i = 0; i < maxLength; i++) {
            const v1Part = v1Parts[i] || 0;
            const v2Part = v2Parts[i] || 0;
            
            if (v1Part > v2Part) return 1;
            if (v1Part < v2Part) return -1;
        }
        
        return 0;
    }

    /**
     * ArrayBuffer转Base64
     */
    private arrayBufferToBase64(buffer: ArrayBuffer): string {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }
}
