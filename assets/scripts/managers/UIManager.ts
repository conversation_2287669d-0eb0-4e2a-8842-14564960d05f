import { _decorator, Component, Node, Label, Prefab } from 'cc';
import { StatsContext } from '../contexts/StatsContext';
import { PurchasableItemConfig } from '../contexts/ConfigContext';
import { ShopItemIntroduce } from '../ui/shop/ShopItemIntroduce';
import { OfflineIncomeUI } from '../ui/OfflineIncomeUI';

const { ccclass, property } = _decorator;

/**
 * UI管理器，负责管理游戏的所有UI元素
 */
@ccclass('UIManager')
export class UIManager extends Component {
    // 单例实例
    private static _instance: UIManager = null;

    // UI元素引用
    @property(Node)
    mainPanel: Node = null;

    @property(Node)
    optionsPanel: Node = null;

    @property(Node)
    shopPanel: Node = null;

    @property(Label)
    moneyLabel: Label = null;

    @property(Node)
    shopItemInstroduce: Node = null;

    @property(Node)
    shopItemSkillInstroduce: Node = null;
    
    @property(Node)
    taskComplete: Node = null;

    @property(Node)
    shopItemSkillFragmentInstroduce: Node = null;

    @property(Prefab)
    floatingTextPrefab: Prefab = null;

    @property(Node)
    nodeSetting: Node = null;

    @property(Node)
    nodeCheck: Node = null;

    @property(Node)
    rank: Node = null;

    @property(Node)
    offlineIncomeUI: Node = null;

    @property(Node)
    nodeTargetTips: Node = null;
    
    // 格式常量
    private readonly MONEY_STRING_FORMAT: string = "<color=#ffffff>{amount}</color>";

    // 单例访问器
    static get instance(): UIManager {
        return this._instance;
    }

    onLoad() {
        // 单例模式
        if (UIManager._instance !== null) {
            // 已存在实例，销毁当前节点
            if (this.node) {
                this.node.parent = null;
            }
            return;
        }

        UIManager._instance = this;

        // 初始化UI
        this.initUI();
    }

    start() {
        // 设置定时更新金钱显示
        this.schedule(this.updateMoneyLabel, 0.2);
    }

    onDestroy() {
        if (UIManager._instance === this) {
            UIManager._instance = null;
        }

        // 停止计时器
        this.unschedule(this.updateMoneyLabel);
    }

    /**
     * 初始化UI
     */
    private initUI() {
        // 显示主面板，隐藏其他面板
        if (this.mainPanel) this.mainPanel.active = true;
        if (this.optionsPanel) this.optionsPanel.active = false;
        if (this.shopPanel) this.shopPanel.active = false;

        // 更新金钱显示
        this.updateMoneyLabel();
    }

    /**
     * 更新金钱标签
     */
    private updateMoneyLabel() {
        // 更新金钱显示
        if (this.moneyLabel) {
            this.moneyLabel.string = this.MONEY_STRING_FORMAT.replace(
                "{amount}",
                StatsContext.money.toLocaleString()
            );
        }
    }

    /**
     * 选项按钮点击事件
     */
    toggleOptionsPanel() {
        if (this.optionsPanel) {
            this.optionsPanel.active = !this.optionsPanel.active;
        }
    }

    /**
     * 商店按钮点击事件
     */
    toggleShopPanel() {
        if (this.shopPanel) {
            this.shopPanel.active = !this.shopPanel.active;
        }
    }

    /**
     * 显示主面板
     */
    showMainPanel() {
        if (this.mainPanel) this.mainPanel.active = true;
        if (this.optionsPanel) this.optionsPanel.active = false;
        if (this.shopPanel) this.shopPanel.active = false;
    }

    /**
     * 显示选项面板
     */
    showOptionsPanel() {
        if (this.optionsPanel) this.optionsPanel.active = true;
    }

    /**
     * 显示商店面板
     */
    showShopPanel() {
        if (this.shopPanel) this.shopPanel.active = true;
    }

    showShopItemIntroduce(idKey: string, config: PurchasableItemConfig, canBuy: boolean){
        if (this.shopItemInstroduce) {
            this.shopItemInstroduce.active = true;
            this.shopItemInstroduce.getComponent(ShopItemIntroduce).setData(idKey, config, canBuy);
        }
    }

    showShopItemSkillIntroduce(idKey: string, config: PurchasableItemConfig, canBuy: boolean){
        if (this.shopItemSkillInstroduce) {
            this.shopItemSkillInstroduce.active = true;
            this.shopItemSkillInstroduce.getComponent(ShopItemIntroduce).setData(idKey, config, canBuy);
        }
    }
    
    showTaskComplete(){
        if (this.taskComplete) {
            this.taskComplete.active = true;
        }
    }

    showShopItemSkillFragmentIntroduce(idKey: string, config: PurchasableItemConfig, canBuy: boolean){
        if (this.shopItemSkillFragmentInstroduce) {
            this.shopItemSkillFragmentInstroduce.active = true;
            this.shopItemSkillFragmentInstroduce.getComponent(ShopItemIntroduce).setData(idKey, config, canBuy);
        }
    }

    showSetting(){
        if (this.nodeSetting) {
            this.nodeSetting.active = true;
        }
    }

    showCheck(){
        if (this.nodeCheck) {
            this.nodeCheck.active = true;
        }
    }

    showRank(){
        if (this.rank) {
            this.rank.active = true;
        }
    }

    showOfflineIncomeUI(offlineMoney: number){
        if (this.offlineIncomeUI) {
            this.offlineIncomeUI.active = true;
            this.offlineIncomeUI.getComponent(OfflineIncomeUI).setData(offlineMoney);
        }
    }

    showTargetTips(){
        if (this.nodeTargetTips) {
            this.nodeTargetTips.active = true;
        }
    }

    /**
     * 保存游戏
     */
    saveGame() {
        //TODO
    }

    /**
     * 重置游戏
     */
    resetGame() {
        //TODO
    }
}