import { _decorator, Component, Button, Label, Texture2D, SpriteFrame } from 'cc';
import { VersionManager } from '../managers/VersionManager';
import { ResourceLoader } from '../utils/ResourceLoader';
import { VersionConfig } from '../config/VersionConfig';
import { GameEvent } from '../event/GameEvent';

const { ccclass, property } = _decorator;

/**
 * 版本管理器使用示例
 * 展示如何在游戏中使用版本检测、热更新和资源加载功能
 */
@ccclass('VersionManagerExample')
export class VersionManagerExample extends Component {

    @property({ type: Button, displayName: '检查更新按钮' })
    public checkUpdateBtn: Button = null;

    @property({ type: Button, displayName: '加载资源按钮' })
    public loadResourceBtn: Button = null;

    @property({ type: Button, displayName: '清理缓存按钮' })
    public clearCacheBtn: Button = null;

    @property({ type: Label, displayName: '状态标签' })
    public statusLabel: Label = null;

    @property({ type: Label, displayName: '版本标签' })
    public versionLabel: Label = null;

    protected onLoad(): void {
        this.initUI();
        this.registerEvents();
    }

    protected onDestroy(): void {
        this.unregisterEvents();
    }

    /**
     * 初始化UI
     */
    private initUI(): void {
        // 显示当前版本信息
        if (this.versionLabel) {
            const versionInfo = VersionConfig.getVersionInfo();
            this.versionLabel.string = `版本: ${versionInfo.version} (${versionInfo.buildNumber})`;
        }

        // 设置按钮事件
        if (this.checkUpdateBtn) {
            this.checkUpdateBtn.node.on(Button.EventType.CLICK, this.onCheckUpdateClick, this);
        }

        if (this.loadResourceBtn) {
            this.loadResourceBtn.node.on(Button.EventType.CLICK, this.onLoadResourceClick, this);
        }

        if (this.clearCacheBtn) {
            this.clearCacheBtn.node.on(Button.EventType.CLICK, this.onClearCacheClick, this);
        }

        this.updateStatus('就绪');
    }

    /**
     * 注册版本管理事件
     */
    private registerEvents(): void {
        GameEvent.on('version-check-start', this.onVersionCheckStart, this);
        GameEvent.on('version-check-complete', this.onVersionCheckComplete, this);
        GameEvent.on('version-check-error', this.onVersionCheckError, this);
        GameEvent.on('hot-update-start', this.onHotUpdateStart, this);
        GameEvent.on('hot-update-progress', this.onHotUpdateProgress, this);
        GameEvent.on('hot-update-complete', this.onHotUpdateComplete, this);
        GameEvent.on('hot-update-error', this.onHotUpdateError, this);
    }

    /**
     * 取消注册事件
     */
    private unregisterEvents(): void {
        GameEvent.off('version-check-start', this.onVersionCheckStart, this);
        GameEvent.off('version-check-complete', this.onVersionCheckComplete, this);
        GameEvent.off('version-check-error', this.onVersionCheckError, this);
        GameEvent.off('hot-update-start', this.onHotUpdateStart, this);
        GameEvent.off('hot-update-progress', this.onHotUpdateProgress, this);
        GameEvent.off('hot-update-complete', this.onHotUpdateComplete, this);
        GameEvent.off('hot-update-error', this.onHotUpdateError, this);
    }

    /**
     * 检查更新按钮点击
     */
    private async onCheckUpdateClick(): Promise<void> {
        const versionManager = VersionManager.getInstance();
        if (!versionManager) {
            this.updateStatus('版本管理器未初始化');
            return;
        }

        this.updateStatus('检查更新中...');
        await versionManager.checkForUpdates();
    }

    /**
     * 加载资源按钮点击
     */
    private async onLoadResourceClick(): Promise<void> {
        this.updateStatus('加载资源中...');

        try {
            // 示例1: 加载纹理资源
            const texture = await ResourceLoader.getInstance().loadVersionedResource<Texture2D>(
                'textures/ui/button_normal',
                Texture2D,
                { useCache: true, fallbackToLocal: true }
            );

            if (texture) {
                console.log('[Example] 纹理加载成功:', texture);
                this.updateStatus('纹理资源加载成功');
            } else {
                this.updateStatus('纹理资源加载失败');
            }

            // 示例2: 加载JSON配置
            const config = await ResourceLoader.getInstance().loadVersionedResource<any>(
                'configs/game_config',
                cc.JsonAsset,
                { version: '1.0.0' }
            );

            if (config) {
                console.log('[Example] 配置加载成功:', config);
                this.updateStatus('配置文件加载成功');
            }

            // 示例3: 预加载资源列表
            const resourcePaths = [
                'textures/ui/icon_coin',
                'textures/ui/icon_gem',
                'audio/sfx/click'
            ];

            await ResourceLoader.getInstance().preloadResources(
                resourcePaths,
                cc.Asset,
                { useCache: true },
                (progress, path) => {
                    this.updateStatus(`预加载中: ${progress}% - ${path}`);
                }
            );

            this.updateStatus('所有资源加载完成');

        } catch (error) {
            console.error('[Example] 资源加载失败:', error);
            this.updateStatus(`资源加载失败: ${error.message}`);
        }
    }

    /**
     * 清理缓存按钮点击
     */
    private onClearCacheClick(): void {
        try {
            const resourceLoader = ResourceLoader.getInstance();
            const cacheInfo = resourceLoader.getCacheSize();
            
            console.log('[Example] 清理前缓存信息:', cacheInfo);
            
            resourceLoader.clearCache();
            
            const newCacheInfo = resourceLoader.getCacheSize();
            console.log('[Example] 清理后缓存信息:', newCacheInfo);
            
            this.updateStatus(`缓存已清理 (释放 ${cacheInfo.sizeKB}KB)`);
            
        } catch (error) {
            console.error('[Example] 清理缓存失败:', error);
            this.updateStatus(`清理缓存失败: ${error.message}`);
        }
    }

    /**
     * 版本检查开始
     */
    private onVersionCheckStart(data: any): void {
        this.updateStatus('正在检查版本...');
    }

    /**
     * 版本检查完成
     */
    private onVersionCheckComplete(data: any): void {
        if (data.hasUpdate) {
            const updateType = data.updateType === 'mandatory' ? '强制' : '可选';
            this.updateStatus(`发现${updateType}更新: ${data.versionInfo.version}`);
        } else {
            this.updateStatus('当前已是最新版本');
        }
    }

    /**
     * 版本检查错误
     */
    private onVersionCheckError(data: any): void {
        this.updateStatus(`版本检查失败: ${data.error}`);
    }

    /**
     * 热更新开始
     */
    private onHotUpdateStart(data: any): void {
        this.updateStatus(`开始热更新: ${data.fromVersion} -> ${data.toVersion}`);
    }

    /**
     * 热更新进度
     */
    private onHotUpdateProgress(progress: any): void {
        const phase = progress.phase === 'downloading' ? '下载' : '安装';
        this.updateStatus(`${phase}中: ${progress.progress}%`);
    }

    /**
     * 热更新完成
     */
    private onHotUpdateComplete(data: any): void {
        this.updateStatus(`热更新完成: ${data.version}`);
        
        // 更新版本显示
        if (this.versionLabel) {
            this.versionLabel.string = `版本: ${data.version}`;
        }
    }

    /**
     * 热更新错误
     */
    private onHotUpdateError(data: any): void {
        this.updateStatus(`热更新失败: ${data.error}`);
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string): void {
        if (this.statusLabel) {
            this.statusLabel.string = `状态: ${message}`;
        }
        console.log(`[Example] ${message}`);
    }

    /**
     * 获取版本管理器状态信息
     */
    public getVersionManagerStatus(): {
        isReady: boolean;
        currentVersion: string;
        cacheInfo: any;
        platformInfo: any;
    } {
        const versionManager = VersionManager.getInstance();
        const resourceLoader = ResourceLoader.getInstance();
        
        return {
            isReady: !!versionManager,
            currentVersion: versionManager ? versionManager.getCurrentVersion() : 'Unknown',
            cacheInfo: resourceLoader.getCacheSize(),
            platformInfo: VersionConfig.getPlatformConfig()
        };
    }

    /**
     * 设置开发模式
     */
    public setDevelopmentMode(enabled: boolean): void {
        if (enabled) {
            VersionConfig.setEnvironment('development');
            this.updateStatus('已切换到开发模式');
        } else {
            VersionConfig.setEnvironment('production');
            this.updateStatus('已切换到生产模式');
        }
    }

    /**
     * 打印调试信息
     */
    public printDebugInfo(): void {
        console.log('[Example] === 版本管理调试信息 ===');
        
        // 打印配置信息
        VersionConfig.printConfig();
        
        // 打印状态信息
        const status = this.getVersionManagerStatus();
        console.log('[Example] 状态信息:', status);
        
        // 打印平台信息
        console.log('[Example] 平台:', cc.sys.platformToString(cc.sys.platform));
        console.log('[Example] 浏览器:', cc.sys.browserType);
        console.log('[Example] 操作系统:', cc.sys.os);
    }
}
