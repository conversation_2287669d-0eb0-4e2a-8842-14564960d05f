import { _decorator, Component, director, Label, Node, profiler, ProgressBar, instantiate, Prefab } from 'cc';
import { VersionManager } from './managers/VersionManager';
import { ResourceLoader } from './utils/ResourceLoader';
import { GameEvent } from './event/GameEvent';
const { ccclass, property } = _decorator;

@ccclass('Loading')
export class Loading extends Component {

    // 进度条组件
    @property(ProgressBar)
    progressBar: ProgressBar = null!;

    // 显示进度的文本标签
    @property(Label)
    progressLabel: Label = null!;

    // 版本管理器预制体
    @property(Prefab)
    versionManagerPrefab: Prefab = null!;

    // 版本更新UI预制体
    @property(Prefab)
    versionUpdateUIPrefab: Prefab = null!;

    // 主场景名称
    private mainSceneName = 'Game';
    private versionManager: VersionManager = null;
    private isVersionCheckComplete = false;
    private isSceneLoadComplete = false;

    start() {
        console.log('[Loading] 开始初始化...');

        // 初始化版本管理器
        this.initVersionManager();

        // 开始版本检查和场景加载
        this.startLoadingProcess();

        profiler.showStats();
    }

    /**
     * 初始化版本管理器
     */
    private initVersionManager(): void {
        if (this.versionManagerPrefab) {
            const versionManagerNode = instantiate(this.versionManagerPrefab);
            this.versionManager = versionManagerNode.getComponent(VersionManager);
            director.getScene().addChild(versionManagerNode);
            console.log('[Loading] 版本管理器已初始化');
        } else {
            console.warn('[Loading] 版本管理器预制体未设置');
        }
    }

    /**
     * 开始加载流程
     */
    private startLoadingProcess(): void {
        // 注册版本管理事件
        this.registerVersionEvents();

        // 并行执行版本检查和场景预加载
        this.checkVersion();
        this.preloadMainScene();
    }

    /**
     * 注册版本管理事件
     */
    private registerVersionEvents(): void {
        GameEvent.on('version-manager-ready', this.onVersionManagerReady, this);
        GameEvent.on('version-check-complete', this.onVersionCheckComplete, this);
        GameEvent.on('version-check-error', this.onVersionCheckError, this);
        GameEvent.on('hot-update-complete', this.onHotUpdateComplete, this);
    }

    /**
     * 版本管理器就绪
     */
    private onVersionManagerReady(data: any): void {
        console.log('[Loading] 版本管理器就绪:', data.version);
        this.updateProgress(0.1, '版本管理器已就绪...');
    }

    /**
     * 版本检查完成
     */
    private onVersionCheckComplete(data: any): void {
        console.log('[Loading] 版本检查完成:', data);
        this.isVersionCheckComplete = true;

        if (data.hasUpdate && data.updateType === 'mandatory') {
            // 强制更新，显示更新UI
            this.showVersionUpdateUI();
        } else {
            // 无更新或可选更新，继续加载
            this.updateProgress(0.3, '版本检查完成...');
            this.checkLoadingComplete();
        }
    }

    /**
     * 版本检查错误
     */
    private onVersionCheckError(data: any): void {
        console.warn('[Loading] 版本检查失败，继续加载:', data.error);
        this.isVersionCheckComplete = true;
        this.updateProgress(0.3, '版本检查失败，继续加载...');
        this.checkLoadingComplete();
    }

    /**
     * 热更新完成
     */
    private onHotUpdateComplete(data: any): void {
        console.log('[Loading] 热更新完成:', data.version);
        this.isVersionCheckComplete = true;
        this.updateProgress(0.5, '热更新完成...');
        this.checkLoadingComplete();
    }

    /**
     * 检查版本
     */
    private checkVersion(): void {
        this.updateProgress(0.05, '检查版本更新...');

        if (this.versionManager) {
            this.versionManager.checkForUpdates();
        } else {
            // 如果没有版本管理器，直接标记完成
            this.isVersionCheckComplete = true;
            this.updateProgress(0.3, '跳过版本检查...');
        }
    }

    /**
     * 预加载主场景
     */
    private preloadMainScene(): void {
        this.updateProgress(0.4, '加载游戏资源...');

        director.preloadScene(this.mainSceneName, (completedCount, totalCount) => {
            // 计算场景加载进度 (40% - 90%)
            const sceneProgress = completedCount / totalCount;
            const totalProgress = 0.4 + (sceneProgress * 0.5);
            this.updateProgress(totalProgress, `加载游戏资源...${Math.floor(sceneProgress * 100)}%`);
        }, (err) => {
            if (err) {
                console.error('[Loading] 加载主场景失败:', err);
                this.updateProgress(1, '加载失败');
                return;
            }

            console.log('[Loading] 主场景预加载完成');
            this.isSceneLoadComplete = true;
            this.updateProgress(0.9, '资源加载完成...');
            this.checkLoadingComplete();
        });
    }

    /**
     * 检查加载是否完成
     */
    private checkLoadingComplete(): void {
        if (this.isVersionCheckComplete && this.isSceneLoadComplete) {
            this.updateProgress(1, '加载完成');

            // 延迟跳转，让用户看到100%
            this.scheduleOnce(() => {
                this.loadMainScene();
            }, 0.5);
        }
    }

    /**
     * 跳转到主场景
     */
    private loadMainScene(): void {
        console.log('[Loading] 跳转到主场景');
        director.loadScene(this.mainSceneName);
    }

    /**
     * 显示版本更新UI
     */
    private showVersionUpdateUI(): void {
        if (this.versionUpdateUIPrefab) {
            const updateUINode = instantiate(this.versionUpdateUIPrefab);
            this.node.addChild(updateUINode);
            console.log('[Loading] 显示版本更新UI');
        } else {
            console.warn('[Loading] 版本更新UI预制体未设置');
            // 如果没有更新UI，继续加载
            this.isVersionCheckComplete = true;
            this.checkLoadingComplete();
        }
    }

    /**
     * 更新加载进度
     * @param progress 加载进度，范围 0 - 1
     * @param message 可选的进度消息
     */
    updateProgress(progress: number, message?: string) {
        // 更新进度条的值
        this.progressBar.progress = progress;

        // 更新进度文本
        if (message) {
            this.progressLabel.string = `${message} ${Math.floor(progress * 100)}%`;
        } else {
            this.progressLabel.string = `加载中...${Math.floor(progress * 100)}%`;
        }

        console.log(`[Loading] 进度: ${Math.floor(progress * 100)}% - ${message || '加载中'}`);
    }

    /**
     * 组件销毁时清理事件
     */
    protected onDestroy(): void {
        GameEvent.off('version-manager-ready', this.onVersionManagerReady, this);
        GameEvent.off('version-check-complete', this.onVersionCheckComplete, this);
        GameEvent.off('version-check-error', this.onVersionCheckError, this);
        GameEvent.off('hot-update-complete', this.onHotUpdateComplete, this);
    }
}


