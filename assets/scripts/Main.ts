import { _decorator, Component, director, Label, Node, profiler, ProgressBar } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Loading')
export class Loading extends Component {

    // 进度条组件
    @property(ProgressBar)
    progressBar: ProgressBar = null!;

    // 显示进度的文本标签
    @property(Label)
    progressLabel: Label = null!;

    // 主场景名称
    private mainSceneName = 'Game';
    
    start() {
        // 开始加载主场景
        this.loadMainScene();
        profiler.showStats();
    }

    /**
     * 加载主场景
     */
    loadMainScene() {
        director.preloadScene(this.mainSceneName, (completedCount, totalCount) => {
            // 计算加载进度
            const progress = completedCount / totalCount;
            // 更新进度条
            this.updateProgress(progress);
        }, (err) => {
            if (err) {
                console.error('加载主场景失败:', err);
                return;
            }
            // 加载完成后跳转至主场景
            director.loadScene(this.mainSceneName);
        });
    }

    /**
     * 更新加载进度
     * @param progress 加载进度，范围 0 - 1
     */
    updateProgress(progress: number) {
        // 更新进度条的值
        this.progressBar.progress = progress;
        // 更新进度文本，显示为百分比
        this.progressLabel.string = `加载中...${Math.floor(progress * 100)}%`;
    }
}


