// /**
//  * Big.js - 用于处理大数值的类
//  * 这是一个简化版的大数处理类，用于游戏中的数值计算
//  */
// export class Big {
//     // 数值部分
//     private _value: number;
//     // 指数部分
//     private _exponent: number;

//     /**
//      * 构造函数
//      * @param value 初始值，可以是数字或字符串
//      */
//     constructor(value: number | string | Big = 0) {
//         if (value instanceof Big) {
//             this._value = value._value;
//             this._exponent = value._exponent;
//         } else if (typeof value === 'string') {
//             // 处理科学计数法
//             if (value.includes('e') || value.includes('E')) {
//                 const parts = value.split(/e|E/);
//                 const base = parseFloat(parts[0]);
//                 const exp = parseInt(parts[1]);
//                 this._value = base;
//                 this._exponent = exp;
//             } else {
//                 // 处理普通数字
//                 const num = parseFloat(value);
//                 if (isNaN(num)) {
//                     this._value = 0;
//                     this._exponent = 0;
//                 } else {
//                     this._value = num;
//                     this._exponent = 0;
//                     this.normalize();
//                 }
//             }
//         } else {
//             // 处理数字
//             this._value = value;
//             this._exponent = 0;
//             this.normalize();
//         }
//     }

//     /**
//      * 标准化数值，确保值在合理范围内
//      */
//     private normalize(): void {
//         if (this._value === 0) {
//             this._exponent = 0;
//             return;
//         }

//         // 将值调整到 1-10 之间
//         while (Math.abs(this._value) >= 10) {
//             this._value /= 10;
//             this._exponent++;
//         }
//         while (Math.abs(this._value) < 1 && this._value !== 0) {
//             this._value *= 10;
//             this._exponent--;
//         }
//     }

//     /**
//      * 加法
//      * @param other 要加的数
//      * @returns 新的Big对象
//      */
//     plus(other: number | string | Big): Big {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         // 如果一个数为0，直接返回另一个数
//         if (this.isZero()) return new Big(otherBig);
//         if (otherBig.isZero()) return new Big(this);

//         // 指数差异
//         const expDiff = this._exponent - otherBig._exponent;
        
//         if (expDiff > 15) {
//             // 如果指数差异太大，较小的数可以忽略
//             return new Big(this);
//         } else if (expDiff < -15) {
//             return new Big(otherBig);
//         }
        
//         // 将两个数调整到相同指数
//         let value1 = this._value;
//         let value2 = otherBig._value;
//         let exponent = Math.max(this._exponent, otherBig._exponent);
        
//         if (expDiff > 0) {
//             value2 = value2 / Math.pow(10, expDiff);
//         } else if (expDiff < 0) {
//             value1 = value1 / Math.pow(10, -expDiff);
//         }
        
//         // 执行加法
//         const result = new Big();
//         result._value = value1 + value2;
//         result._exponent = exponent;
//         result.normalize();
        
//         return result;
//     }

//     /**
//      * 减法
//      * @param other 要减的数
//      * @returns 新的Big对象
//      */
//     minus(other: number | string | Big): Big {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         // 将减法转换为加上负数
//         const negated = new Big(otherBig);
//         negated._value = -negated._value;
        
//         return this.plus(negated);
//     }

//     /**
//      * 乘法
//      * @param other 要乘的数
//      * @returns 新的Big对象
//      */
//     times(other: number | string | Big): Big {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         // 如果一个数为0，结果为0
//         if (this.isZero() || otherBig.isZero()) return new Big(0);
        
//         // 执行乘法
//         const result = new Big();
//         result._value = this._value * otherBig._value;
//         result._exponent = this._exponent + otherBig._exponent;
//         result.normalize();
        
//         return result;
//     }

//     /**
//      * 除法
//      * @param other 要除的数
//      * @returns 新的Big对象
//      */
//     div(other: number | string | Big): Big {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         // 检查除数是否为0
//         if (otherBig.isZero()) {
//             throw new Error("Division by zero");
//         }
        
//         // 如果被除数为0，结果为0
//         if (this.isZero()) return new Big(0);
        
//         // 执行除法
//         const result = new Big();
//         result._value = this._value / otherBig._value;
//         result._exponent = this._exponent - otherBig._exponent;
//         result.normalize();
        
//         return result;
//     }

//     /**
//      * 幂运算
//      * @param n 指数
//      * @returns 新的Big对象
//      */
//     pow(n: number): Big {
//         if (n === 0) return new Big(1);
//         if (this.isZero()) return new Big(0);
        
//         const result = new Big();
//         result._value = Math.pow(this._value, n);
//         result._exponent = this._exponent * n;
//         result.normalize();
        
//         return result;
//     }

//     /**
//      * 比较是否大于
//      * @param other 要比较的数
//      * @returns 是否大于
//      */
//     gt(other: number | string | Big): boolean {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         if (this._exponent > otherBig._exponent) return this._value > 0;
//         if (this._exponent < otherBig._exponent) return otherBig._value < 0;
        
//         return this._value > otherBig._value;
//     }

//     /**
//      * 比较是否小于
//      * @param other 要比较的数
//      * @returns 是否小于
//      */
//     lt(other: number | string | Big): boolean {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         if (this._exponent > otherBig._exponent) return this._value < 0;
//         if (this._exponent < otherBig._exponent) return otherBig._value > 0;
        
//         return this._value < otherBig._value;
//     }

//     /**
//      * 比较是否等于
//      * @param other 要比较的数
//      * @returns 是否等于
//      */
//     eq(other: number | string | Big): boolean {
//         const otherBig = other instanceof Big ? other : new Big(other);
        
//         return this._exponent === otherBig._exponent && this._value === otherBig._value;
//     }

//     /**
//      * 检查是否为0
//      * @returns 是否为0
//      */
//     isZero(): boolean {
//         return this._value === 0;
//     }

//     /**
//      * 转换为字符串
//      * @returns 字符串表示
//      */
//     toString(): string {
//         if (this.isZero()) return "0";
        
//         if (this._exponent === 0) {
//             return this._value.toString();
//         } else {
//             return this._value + "e" + this._exponent;
//         }
//     }

//     /**
//      * 转换为固定小数位的字符串
//      * @param dp 小数位数
//      * @returns 固定小数位的字符串
//      */
//     toFixed(dp: number = 0): string {
//         if (this.isZero()) return "0" + (dp > 0 ? "." + "0".repeat(dp) : "");
        
//         if (this._exponent >= 0) {
//             // 数值较大，使用科学计数法
//             const fullNum = this._value * Math.pow(10, this._exponent);
//             return fullNum.toFixed(dp);
//         } else if (this._exponent + this._value.toString().length - 2 >= 0) {
//             // 数值在正常范围内
//             const fullNum = this._value * Math.pow(10, this._exponent);
//             return fullNum.toFixed(dp);
//         } else {
//             // 数值较小，使用科学计数法
//             return this.toExponential(dp);
//         }
//     }

//     /**
//      * 转换为科学计数法字符串
//      * @param dp 小数位数
//      * @returns 科学计数法字符串
//      */
//     toExponential(dp: number = 0): string {
//         if (this.isZero()) return "0" + (dp > 0 ? "." + "0".repeat(dp) : "") + "e+0";
        
//         const mantissa = this._value.toFixed(dp);
//         return mantissa + "e" + (this._exponent >= 0 ? "+" : "") + this._exponent;
//     }

//     /**
//      * 获取指数部分
//      */
//     get e(): number {
//         return this._exponent;
//     }

//     /**
//      * 转换为浮点数
//      */
//     toFloat(): number {
//         return this._value * Math.pow(10, this._exponent);
//     }

//     /**
//      * 比较是否大于（兼容BigNumber）
//      */
//     isGreaterThan(other: number | string | Big): boolean {
//         return this.gt(other);
//     }

//     /**
//      * 比较是否小于（兼容BigNumber）
//      */
//     isLessThan(other: number | string | Big): boolean {
//         return this.lt(other);
//     }

//     /**
//      * 比较是否大于等于（兼容BigNumber）
//      */
//     isGreaterThanOrEqualTo(other: number | string | Big): boolean {
//         return this.gt(other) || this.eq(other);
//     }

//     /**
//      * 除法（兼容BigNumber）
//      */
//     dividedBy(other: number | string | Big): Big {
//         return this.div(other);
//     }

//     /**
//      * 静态方法：向下取整
//      */
//     static roundDown(value: Big): Big {
//         const result = new Big(value);
//         if (result._exponent < 0) {
//             // 如果指数为负，则小数部分被舍去
//             result._value = Math.floor(result._value * Math.pow(10, result._exponent)) / Math.pow(10, result._exponent);
//         }
//         return result;
//     }

//     /**
//      * 静态方法：两个Big值相加
//      */
//     static add(a: Big, b: Big): Big {
//         return a.plus(b);
//     }

//     /**
//      * 静态方法：两个Big值相减
//      */
//     static subtract(a: Big, b: Big): Big {
//         return a.minus(b);
//     }
// } 