import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TimeHelper')
export class TimeHelper {
    /**
     * 获取当前时间戳（毫秒）
     * @returns 当前时间戳（毫秒）
     */
    static getCurrentTimestamp(): number {
        return new Date().getTime();
    }

    /**
     * 获取当前时间戳（秒）
     * @returns 当前时间戳（秒）
     */
    static getCurrentTimestampInSeconds(): number {
        return Math.floor(Date.now() / 1000);
    }

    /**
     * 将秒数转换为天-小时-分-秒格式的字符串
     * @param seconds 总秒数
     * @returns 格式化后的字符串
     */
    static secondsToDHMS(seconds: number): string {
        // 计算天、小时、分钟和秒
        const days = Math.floor(seconds / (3600 * 24));
        const hours = Math.floor((seconds % (3600 * 24)) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        let result = '';
        //天
        if (days > 0) {
            result += `${days}天`;
        }
        //小时
        if (hours >= 10 ) {
            result += `${hours}:`;
        }else if(hours > 0){
            result += `0${hours}:`;
        }else{
            result += `00:`;
        }
        //分钟
        if (minutes >= 10) {
            result += `${minutes}:`;
        }else if(minutes>0){
            result += `0${minutes}:`;
        }else{
            result += `00:`;
        }
        //秒
        if (secs >= 10) {
            result += `${secs}`;
        }else if(secs>0){
            result += `0${secs}`;
        }else{
            result += `00`;
        }

        return result;
    }

    /**
     * 将秒数转换为天-小时-分-秒格式的字符串
     * @param seconds 总秒数
     * @returns 格式化后的字符串
     */
    static secondsToMS(seconds: number): string {
        // 分钟和秒
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        let result = '';
        //分钟
        if (minutes >= 10) {
            result += `${minutes}:`;
        }else if(minutes>0){
            result += `0${minutes}:`;
        }else{
            result += `00:`;
        }
        //秒
        if (secs >= 10) {
            result += `${secs}`;
        }else if(secs>0){
            result += `0${secs}`;
        }else{
            result += `00`;
        }

        return result;
    }
}


