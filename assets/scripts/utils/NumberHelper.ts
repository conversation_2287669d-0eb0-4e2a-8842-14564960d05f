import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('NumberHelper')
export class NumberHelper {
    // 数字单位
    private static units: string[] = ["", "K", "M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc"];

    /**
     * 将 number 类型的数值转换为带单位的文本表示
     * @param value number 类型的数值
     * @param precision 精度（小数点后位数）
     * @returns 带单位的文本表示
     */
    static convert_number_to_text(value: number, precision: number = 1): string {
        if (value === 0) return "0";
        // 计算数值的指数
        const exponent = Math.floor(Math.log10(Math.abs(value)));
        const unitIndex = Math.floor(exponent / 3);

        if (unitIndex >= this.units.length) {
            // 如果数值太大，超出了预定义单位，则使用科学计数法
            return value.toExponential(precision);
        } else if (unitIndex <= 0) {
            // 小于1000的数值，直接显示
            return value.toFixed(precision).replace(/\.?0+$/, "");
        } else {
            // 使用单位缩写
            const divisor = Math.pow(10, unitIndex * 3);
            const scaledValue = value / divisor;
            return scaledValue.toFixed(precision).replace(/\.?0+$/, "") + this.units[unitIndex];
        }
    }

    /**
     * 将 number 类型的数值转换为带单位k的文本表示
     * @param value number 类型的数值
     * @returns 带单位的文本表示
     */
    static convert_number_to_text_with_thousand(value: number): string {
        if (value < 1000) return value.toString();

        value = value/1000;

        return Math.round(value) + "K";

    }


}


