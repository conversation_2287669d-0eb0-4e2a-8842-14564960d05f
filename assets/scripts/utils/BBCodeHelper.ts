export class BBCodeHelper {
    private text: string;
    private tags: string[] = [];
    
    /**
     * 创建一个新的BBCode构建器
     * @param text 初始文本
     * @returns BBCodeHelper实例
     */
    static build(text: string): BBCodeHelper {
        return new BBCodeHelper(text);
    }
    
    constructor(text: string) {
        this.text = text;
    }
    
    /**
     * 添加颜色标签
     * @param colorCode 颜色代码，如 "#FF0000" 或 "red"
     * @returns BBCodeHelper实例
     */
    color(colorCode: string): BBCodeHelper {
        this.tags.push(`<color=${colorCode}>`);
        return this;
    }
    
    /**
     * 添加粗体标签
     * @returns BBCodeHelper实例
     */
    bold(): BBCodeHelper {
        this.tags.push("<b>");
        return this;
    }
    
    /**
     * 添加斜体标签
     * @returns BBCodeHelper实例
     */
    italic(): BBCodeHelper {
        this.tags.push("<i>");
        return this;
    }
    
    /**
     * 添加下划线标签
     * @returns BBCodeHelper实例
     */
    underline(): BBCodeHelper {
        this.tags.push("<u>");
        return this;
    }
    
    /**
     * 添加字体大小标签
     * @param size 字体大小
     * @returns BBCodeHelper实例
     */
    size(size: number): BBCodeHelper {
        this.tags.push(`<size=${size}>`);
        return this;
    }
    
    /**
     * 添加自定义标签
     * @param tag 标签名称
     * @param value 标签值（可选）
     * @returns BBCodeHelper实例
     */
    tag(tag: string, value?: string): BBCodeHelper {
        if (value) {
            this.tags.push(`<${tag}=${value}>`);
        } else {
            this.tags.push(`<${tag}>`);
        }
        return this;
    }
    
    /**
     * 生成最终的BBCode文本
     * @returns 带有BBCode标签的文本
     */
    result(): string {
        let result = this.text;
        
        // 添加开始标签
        for (let i = 0; i < this.tags.length; i++) {
            result = this.tags[i] + result;
        }
        
        // 添加结束标签（反向）
        for (let i = this.tags.length - 1; i >= 0; i--) {
            const tag = this.tags[i];
            const closingTag = this.getClosingTag(tag);
            result = result + closingTag;
        }
        
        return result;
    }
    
    /**
     * 获取标签的关闭标签
     * @param openingTag 开始标签
     * @returns 关闭标签
     */
    private getClosingTag(openingTag: string): string {
        // 提取标签名称
        const match = openingTag.match(/<([^=>]+)(?:=.*)?>/);
        if (match && match[1]) {
            return `</${match[1]}>`;
        }
        return "";
    }
} 