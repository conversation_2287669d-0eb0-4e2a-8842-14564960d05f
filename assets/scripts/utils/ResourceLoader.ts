import { _decorator, sys, resources, AssetManager, Texture2D, SpriteFrame, AudioClip, JsonAsset, Prefab } from 'cc';
import { VersionManager } from '../managers/VersionManager';

const { ccclass } = _decorator;

/**
 * 资源加载选项
 */
export interface LoadOptions {
    version?: string;
    useCache?: boolean;
    timeout?: number;
    fallbackToLocal?: boolean;
}

/**
 * 版本化资源加载器
 * 支持热更新资源的加载和缓存管理
 */
@ccclass('ResourceLoader')
export class ResourceLoader {
    private static _instance: ResourceLoader = null;
    private _loadingCache: Map<string, Promise<any>> = new Map();
    private _resourceCache: Map<string, any> = new Map();

    public static getInstance(): ResourceLoader {
        if (!ResourceLoader._instance) {
            ResourceLoader._instance = new ResourceLoader();
        }
        return ResourceLoader._instance;
    }

    /**
     * 加载版本化资源
     */
    public async loadVersionedResource<T>(
        path: string, 
        type: typeof Texture2D | typeof SpriteFrame | typeof AudioClip | typeof JsonAsset | typeof Prefab,
        options: LoadOptions = {}
    ): Promise<T | null> {
        const {
            version = VersionManager.getInstance()?.getCurrentVersion() || '1.0.0',
            useCache = true,
            timeout = 10000,
            fallbackToLocal = true
        } = options;

        const cacheKey = `${path}_${version}`;

        // 检查缓存
        if (useCache && this._resourceCache.has(cacheKey)) {
            console.log('[ResourceLoader] 从缓存加载资源:', path);
            return this._resourceCache.get(cacheKey);
        }

        // 检查是否正在加载
        if (this._loadingCache.has(cacheKey)) {
            console.log('[ResourceLoader] 等待资源加载完成:', path);
            return await this._loadingCache.get(cacheKey);
        }

        // 开始加载
        const loadPromise = this.doLoadResource<T>(path, type, version, timeout, fallbackToLocal);
        this._loadingCache.set(cacheKey, loadPromise);

        try {
            const resource = await loadPromise;
            
            // 缓存资源
            if (useCache && resource) {
                this._resourceCache.set(cacheKey, resource);
            }

            return resource;
        } catch (error) {
            console.error('[ResourceLoader] 资源加载失败:', path, error);
            return null;
        } finally {
            this._loadingCache.delete(cacheKey);
        }
    }

    /**
     * 执行资源加载
     */
    private async doLoadResource<T>(
        path: string,
        type: any,
        version: string,
        timeout: number,
        fallbackToLocal: boolean
    ): Promise<T | null> {
        // 1. 尝试从热更新缓存加载
        const hotUpdateResource = await this.loadFromHotUpdateCache<T>(path, version);
        if (hotUpdateResource) {
            console.log('[ResourceLoader] 从热更新缓存加载:', path);
            return hotUpdateResource;
        }

        // 2. 尝试从远程服务器加载
        const remoteResource = await this.loadFromRemote<T>(path, type, version, timeout);
        if (remoteResource) {
            console.log('[ResourceLoader] 从远程服务器加载:', path);
            return remoteResource;
        }

        // 3. 回退到本地资源
        if (fallbackToLocal) {
            const localResource = await this.loadFromLocal<T>(path, type);
            if (localResource) {
                console.log('[ResourceLoader] 从本地资源加载:', path);
                return localResource;
            }
        }

        throw new Error(`无法加载资源: ${path}`);
    }

    /**
     * 从热更新缓存加载资源
     */
    private async loadFromHotUpdateCache<T>(path: string, version: string): Promise<T | null> {
        try {
            const key = `resource_${path}_${version}`;
            const cachedData = sys.localStorage.getItem(key);
            
            if (!cachedData) {
                return null;
            }

            // 将Base64数据转换为资源
            const arrayBuffer = this.base64ToArrayBuffer(cachedData);
            
            // 根据文件类型处理
            if (path.endsWith('.json')) {
                const text = new TextDecoder().decode(arrayBuffer);
                return JSON.parse(text) as T;
            } else if (path.endsWith('.png') || path.endsWith('.jpg')) {
                // 创建Texture2D
                const texture = new Texture2D();
                const imageAsset = new ImageAsset();
                imageAsset._nativeAsset = arrayBuffer;
                texture.image = imageAsset;
                return texture as T;
            }

            return null;
        } catch (error) {
            console.error('[ResourceLoader] 热更新缓存加载失败:', path, error);
            return null;
        }
    }

    /**
     * 从远程服务器加载资源
     */
    private async loadFromRemote<T>(
        path: string,
        type: any,
        version: string,
        timeout: number
    ): Promise<T | null> {
        try {
            const versionManager = VersionManager.getInstance();
            if (!versionManager) {
                return null;
            }

            const resourceUrl = this.getResourceUrl(path, version);
            console.log('[ResourceLoader] 从远程加载:', resourceUrl);

            return new Promise<T | null>((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', resourceUrl, true);
                xhr.timeout = timeout;

                // 根据资源类型设置响应类型
                if (path.endsWith('.json')) {
                    xhr.responseType = 'text';
                } else {
                    xhr.responseType = 'arraybuffer';
                }

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        try {
                            let resource: T;

                            if (path.endsWith('.json')) {
                                resource = JSON.parse(xhr.responseText) as T;
                            } else if (path.endsWith('.png') || path.endsWith('.jpg')) {
                                const texture = new Texture2D();
                                const imageAsset = new ImageAsset();
                                imageAsset._nativeAsset = xhr.response;
                                texture.image = imageAsset;
                                resource = texture as T;
                            } else {
                                resolve(null);
                                return;
                            }

                            // 缓存到本地
                            this.cacheResourceToLocal(path, version, xhr.response);
                            resolve(resource);
                        } catch (error) {
                            reject(error);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}`));
                    }
                };

                xhr.onerror = () => reject(new Error('网络错误'));
                xhr.ontimeout = () => reject(new Error('请求超时'));
                xhr.send();
            });

        } catch (error) {
            console.error('[ResourceLoader] 远程加载失败:', path, error);
            return null;
        }
    }

    /**
     * 从本地资源加载
     */
    private async loadFromLocal<T>(path: string, type: any): Promise<T | null> {
        return new Promise<T | null>((resolve) => {
            resources.load(path, type, (error, asset) => {
                if (error) {
                    console.error('[ResourceLoader] 本地资源加载失败:', path, error);
                    resolve(null);
                } else {
                    resolve(asset as T);
                }
            });
        });
    }

    /**
     * 获取资源URL
     */
    private getResourceUrl(path: string, version: string): string {
        const versionManager = VersionManager.getInstance();
        if (!versionManager) {
            return '';
        }

        const baseUrl = versionManager.updateServerUrl.replace('/version', '/resources');
        return `${baseUrl}/${path}?v=${version}&t=${Date.now()}`;
    }

    /**
     * 缓存资源到本地
     */
    private cacheResourceToLocal(path: string, version: string, data: any): void {
        try {
            const key = `resource_${path}_${version}`;
            let cacheData: string;

            if (typeof data === 'string') {
                cacheData = btoa(data);
            } else if (data instanceof ArrayBuffer) {
                cacheData = this.arrayBufferToBase64(data);
            } else {
                cacheData = btoa(JSON.stringify(data));
            }

            sys.localStorage.setItem(key, cacheData);
            console.log('[ResourceLoader] 资源已缓存:', path);
        } catch (error) {
            console.error('[ResourceLoader] 缓存资源失败:', path, error);
        }
    }

    /**
     * 预加载资源列表
     */
    public async preloadResources(
        resourcePaths: string[],
        type: any,
        options: LoadOptions = {},
        onProgress?: (progress: number, path: string) => void
    ): Promise<void> {
        console.log('[ResourceLoader] 开始预加载资源:', resourcePaths.length);

        const totalCount = resourcePaths.length;
        let loadedCount = 0;

        const loadPromises = resourcePaths.map(async (path) => {
            try {
                await this.loadVersionedResource(path, type, options);
                loadedCount++;
                
                if (onProgress) {
                    const progress = Math.round((loadedCount / totalCount) * 100);
                    onProgress(progress, path);
                }
            } catch (error) {
                console.error('[ResourceLoader] 预加载失败:', path, error);
                loadedCount++;
                
                if (onProgress) {
                    const progress = Math.round((loadedCount / totalCount) * 100);
                    onProgress(progress, path);
                }
            }
        });

        await Promise.all(loadPromises);
        console.log('[ResourceLoader] 预加载完成');
    }

    /**
     * 清理缓存
     */
    public clearCache(version?: string): void {
        if (version) {
            // 清理指定版本的缓存
            const keysToRemove: string[] = [];
            for (let i = 0; i < sys.localStorage.length; i++) {
                const key = sys.localStorage.key(i);
                if (key && key.startsWith('resource_') && key.endsWith(`_${version}`)) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => sys.localStorage.removeItem(key));
            
            // 清理内存缓存
            const cacheKeysToRemove: string[] = [];
            this._resourceCache.forEach((_, key) => {
                if (key.endsWith(`_${version}`)) {
                    cacheKeysToRemove.push(key);
                }
            });
            cacheKeysToRemove.forEach(key => this._resourceCache.delete(key));
            
            console.log('[ResourceLoader] 已清理版本缓存:', version);
        } else {
            // 清理所有缓存
            this._resourceCache.clear();
            
            const keysToRemove: string[] = [];
            for (let i = 0; i < sys.localStorage.length; i++) {
                const key = sys.localStorage.key(i);
                if (key && key.startsWith('resource_')) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => sys.localStorage.removeItem(key));
            
            console.log('[ResourceLoader] 已清理所有缓存');
        }
    }

    /**
     * 获取缓存大小
     */
    public getCacheSize(): { count: number; sizeKB: number } {
        let count = 0;
        let totalSize = 0;

        for (let i = 0; i < sys.localStorage.length; i++) {
            const key = sys.localStorage.key(i);
            if (key && key.startsWith('resource_')) {
                count++;
                const value = sys.localStorage.getItem(key);
                if (value) {
                    totalSize += value.length;
                }
            }
        }

        return {
            count,
            sizeKB: Math.round(totalSize / 1024)
        };
    }

    /**
     * Base64转ArrayBuffer
     */
    private base64ToArrayBuffer(base64: string): ArrayBuffer {
        const binaryString = atob(base64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }

    /**
     * ArrayBuffer转Base64
     */
    private arrayBufferToBase64(buffer: ArrayBuffer): string {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }
}
