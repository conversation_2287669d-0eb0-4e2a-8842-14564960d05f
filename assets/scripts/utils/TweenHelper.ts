import { _decorator, Component, Node, tween, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TweenHelper')
export class TweenHelper {

    // 添加震动图标方法
    static shakeIcon(node) {
        const originalPos = node.position.clone();
        const shakeAmplitude = 3; // 震动幅度
        const shakeDuration = 0.05; // 单次震动时长
        const shakeTimes = 3; // 震动次数

        tween(node)
            .sequence(
                ...Array(shakeTimes).fill(0).map(() => 
                    tween().by(shakeDuration, { position: v3(shakeAmplitude, 0, 0) })
                        .by(shakeDuration, { position: v3(-shakeAmplitude * 2, 0, 0) })
                        .by(shakeDuration, { position: v3(shakeAmplitude, 0, 0) })
                )
            )
            .call(() => {
                node.position = originalPos; // 恢复原始位置
            })
            .start();
    }
}


