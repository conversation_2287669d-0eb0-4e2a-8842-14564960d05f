// import { Big } from './Big';

// export class BigHelper {
//     // 数字单位
//     private static units: string[] = ["", "K", "M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc"];
    
//     /**
//      * 将Big对象转换为带单位的文本表示
//      * @param value Big对象
//      * @param precision 精度（小数点后位数）
//      * @returns 带单位的文本表示
//      */
//     static convert_big_to_text(value: Big, precision: number = 2): string {
//         if (value.isZero()) return "0";
        
//         const exponent = value.e;
//         const unitIndex = Math.floor(exponent / 3);
        
//         if (unitIndex >= this.units.length) {
//             // 如果数值太大，超出了预定义单位，则使用科学计数法
//             return value.toExponential(precision);
//         } else if (unitIndex <= 0) {
//             // 小于1000的数值，直接显示
//             return value.toFixed(precision).replace(/\.?0+$/, "");
//         } else {
//             // 使用单位缩写
//             const divisor = new Big(10).pow(unitIndex * 3);
//             const scaledValue = value.div(divisor);
//             return scaledValue.toFixed(precision).replace(/\.?0+$/, "") + this.units[unitIndex];
//         }
//     }
    
//     /**
//      * 格式化Big对象为带千位分隔符的字符串
//      * @param value Big对象
//      * @returns 带千位分隔符的字符串
//      */
//     static format_with_commas(value: Big): string {
//         const parts = value.toString().split(".");
//         parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
//         return parts.join(".");
//     }
    
//     /**
//      * 将字符串转换为Big对象
//      * @param text 字符串表示的数值
//      * @returns Big对象
//      */
//     static parse_from_string(text: string): Big {
//         try {
//             return new Big(text);
//         } catch (e) {
//             console.error("Failed to parse Big from string:", text);
//             return new Big(0);
//         }
//     }
    
//     /**
//      * 将带单位的字符串转换为Big对象
//      * @param text 带单位的字符串，如 "1.5K"
//      * @returns Big对象
//      */
//     static parse_from_unit_string(text: string): Big {
//         // 匹配数字部分和单位部分
//         const match = text.match(/^([\d.]+)([A-Za-z]*)$/);
//         if (!match) return new Big(0);
        
//         const [, numPart, unitPart] = match;
//         const baseValue = new Big(numPart);
        
//         // 如果没有单位，直接返回数值
//         if (!unitPart) return baseValue;
        
//         // 查找单位对应的指数
//         const unitIndex = this.units.findIndex(unit => unit === unitPart);
//         if (unitIndex <= 0) return baseValue;
        
//         // 计算实际值
//         const multiplier = new Big(10).pow(unitIndex * 3);
//         return baseValue.times(multiplier);
//     }
// } 