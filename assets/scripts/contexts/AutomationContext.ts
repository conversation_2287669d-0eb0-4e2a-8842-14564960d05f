import { _decorator } from 'cc';

export class AutomationContext {
    // 是否激活自动化模式
    static IS_ACTIVE: boolean = false;
    
    // 自动化设置
    static AUTO_CLICK_INTERVAL: number = 0.5; // 自动点击间隔（秒）
    static AUTO_CLICK_ENABLED: boolean = false; // 是否启用自动点击
    
    // 启用自动化模式
    static enableAutomation(): void {
        this.IS_ACTIVE = true;
        console.log('Automation mode enabled');
    }
    
    // 禁用自动化模式
    static disableAutomation(): void {
        this.IS_ACTIVE = false;
        console.log('Automation mode disabled');
    }
    
    // 设置自动点击间隔
    static setAutoClickInterval(interval: number): void {
        this.AUTO_CLICK_INTERVAL = Math.max(0.1, interval);
    }
    
    // 启用自动点击
    static enableAutoClick(): void {
        this.AUTO_CLICK_ENABLED = true;
    }
    
    // 禁用自动点击
    static disableAutoClick(): void {
        this.AUTO_CLICK_ENABLED = false;
    }
} 