import { _decorator, Component, Node, sys } from 'cc';
import { StatsContext } from './StatsContext';
import { AudioManager } from '../managers/AudioManager';
import { TimeHelper } from '../utils/TimeHelper';
const { ccclass, property } = _decorator;

/**
 * 存档数据结构
 */
export interface SaveData {
    // 基本货币
    money: number;
    prestige_points: number;
    // 统计数据
    highestMoney: number;
    highestValueGained: number;
    totalMoney: number;
    taskCompleteMoney: number;
    // 玩家金币
    playerCoins: number;
    // 属性值 - 将包含所有来自 StatsContext 的分类属性
    attributes: Record<string, any>;
    // 游戏内购买记录
    purchases: Record<string, number>;
    // 一次性购买记录
    oneTimePurchases: string[];
    // 员工等级
    helperLevels: Record<string, number>;
    // 员工加成
    helperBonus: number;
    // 当前场景
    currentScene: string;
    //音乐音量
    musicVolume: number;
    //音效音量
    soundVolume: number;
    //新手指引步骤
    newFingerGuide: number;
    //主线任务ID
    mainTaskID: number;
    //主线任务进度
    mainTaskProgress: number;
    //技能碎片目标达成次数
    skillFragmentTaskProgress: number;
    //钻石
    diamond: number;
    // 记录时间戳
    recordTime: number;
    //在线奖励次数
    onlineRewardCount: number;
    
    //开始游戏时间
    startTime: number;
}

/**
 * 存档上下文
 * 负责游戏存档的读取和保存
 */
@ccclass('SaveContext')
export class SaveContext extends Component {
    /** 存档键名 */
    private static SAVE_KEY = 'Earn_one_hundred_million_save';

    /** 存档数据 */
    public static saveData: SaveData = {
        money: 0, 
        prestige_points: 0,
        highestMoney: 0,
        highestValueGained: 0,
        totalMoney: 0,
        taskCompleteMoney: 0,
        playerCoins: 0,
        attributes: {},
        purchases: {},
        oneTimePurchases: [],
        helperLevels: {},
        helperBonus: 1,
        currentScene: 'SCENE_GRASSLAND',
        musicVolume: 1,
        soundVolume: 1,
        newFingerGuide: 0,
        mainTaskID: 1,
        mainTaskProgress: 0,
        skillFragmentTaskProgress: 0,
        diamond: 0,
        // 记录时间戳
        recordTime: 0,
        //在线奖励次数
        onlineRewardCount: 0,
        //开始游戏时间
        startTime: 0,
    }

    /**
     * 组件加载
     */
    onLoad() {
        // SaveContext.resetSaveData();
        this.loadGame();
    }

    /**
     * 保存游戏数据
     */
    public static saveGame(): void {
        try {
            // 同步状态数据
            this.syncFromStatsContext();

            // 序列化并存储
            const saveJson = JSON.stringify(this.saveData);
            sys.localStorage.setItem(this.SAVE_KEY, saveJson);
            console.log('游戏已保存');
        } catch (error) {
            console.error('保存游戏时出错:', error);
        }
    }

    /**
     * 从StatsContext同步数据
     */
    private static syncFromStatsContext(): void {
        try {
            // 同步货币数据
            this.saveData.money = StatsContext.money;
            this.saveData.prestige_points = StatsContext.prestigePoints;
            
            // 同步统计数据
            this.saveData.highestMoney = StatsContext.highestMoney;
            this.saveData.highestValueGained = StatsContext.highestValueGained;
            this.saveData.totalMoney = StatsContext.totalMoney;
            this.saveData.taskCompleteMoney = StatsContext.taskCompleteMoney;
            
            // 同步购买进度
            this.saveData.purchases = { ...StatsContext.purchases };
            this.saveData.oneTimePurchases = [...StatsContext.oneTimePurchases];
            
            // 同步游戏值
            this.saveData.playerCoins = StatsContext.instance.getCoins();
            
            // 清空旧的attributes，准备填充新的分类数据
            this.saveData.attributes = {};

            // @ts-ignore 我们知道 StatsContext 有这些静态 Set
            const quantityKeys = StatsContext.QUANTITY_KEYS as Set<string>; 
            // @ts-ignore
            const modifierKeys = StatsContext.MODIFIER_KEYS as Set<string>; 
            // @ts-ignore
            const unlockKeys = StatsContext.UNLOCK_KEYS as Set<string>; 
            // @ts-ignore
            const upgradeKeys = StatsContext.UPGRADE_KEYS as Set<string>; 
            // @ts-ignore
            const employeeKeys = StatsContext.EMPLOYEE_KEYS as Set<string>; 
            // @ts-ignore
            const skillFragmentKeys = StatsContext.SKILL_FRAGMENT_KEYS as Set<string>; 
            // @ts-ignore
            const skillKeys = StatsContext.SKILL_KEYS as Set<string>; 
            // @ts-ignore
            const sceneKeys = StatsContext.SCENE_KEYS as Set<string>; 
            // @ts-ignore
            const taskKeys = StatsContext.SKILL_FRAGMENT_TASK_KEYS as Set<string>; 

            quantityKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getItemQuantity(key);
            });

            modifierKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getUpgradeModifier(key);
            });

            unlockKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.isFeatureUnlocked(key);
            });

            upgradeKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getUpgrade(key);
            });

            employeeKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getEmployee(key);
            });

            skillFragmentKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getSkillFragment(key);
            });

            skillKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getSkill(key);
            });

            sceneKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getScene(key);
            });
            
            taskKeys.forEach(key => {
                this.saveData.attributes[key] = StatsContext.getSkillFragmentTask(key);
            });
            
            // 获取员工等级
            const helperLevels: Record<string, number> = {};
            StatsContext.instance.getAllHelperLevels().forEach((level, helperId) => {
                helperLevels[helperId] = level;
            });
            this.saveData.helperLevels = helperLevels;
            
            // 获取员工加成
            this.saveData.helperBonus = StatsContext.instance.getHelperBonus();
            
            // 获取当前场景
            this.saveData.currentScene = StatsContext.currentScene;

            // 音量
            this.saveData.musicVolume = AudioManager.bgmVolume;
            this.saveData.soundVolume = AudioManager.sfxVolume;

            //新手引导
            this.saveData.newFingerGuide = StatsContext.newFingerGuide;
            
            //主线任务ID
            this.saveData.mainTaskID = StatsContext.mainTaskID;

            //主线任务进度
            this.saveData.mainTaskProgress = StatsContext.mainTaskProgress;

            //技能碎片目标达成次数
            this.saveData.skillFragmentTaskProgress = StatsContext.skillFragmentTaskProgress;

            // 钻石
            this.saveData.diamond = StatsContext.diamond;

            // 记录时间戳
            this.saveData.recordTime = TimeHelper.getCurrentTimestamp();

            //在线奖励次数
            this.saveData.onlineRewardCount = StatsContext.onlineRewardCount;

            //开始游戏时间
            this.saveData.startTime = StatsContext.startTime;

            // console.log("数据已同步到存档:", this.saveData);
        } catch (error) {
            console.error("同步数据时出错:", error);
        }
    }

    /**
     * 加载游戏存档
     */
    public loadGame(): void {
        try {
            const saveJson = sys.localStorage.getItem(SaveContext.SAVE_KEY);
            
            if (saveJson) {
                // 解析存档数据
                const data = JSON.parse(saveJson);
                this.deserializeData(data);
                
                // 应用到游戏状态
                this.applyToStatsContext();
                console.log('游戏已加载',data);
            } else {
                console.log('未找到存档，开始新游戏');
                StatsContext.resetToDefault();
            }
        } catch (error) {
            console.error('加载游戏时出错:', error);
            StatsContext.resetToDefault();
        }
    }

    /**
     * 解析存档数据
     * @param data 从存储读取的原始数据
     */
    private deserializeData(data: any): void {
        try {
            // 解析货币数据
            SaveContext.saveData.money = Number(data.money || 0);
            SaveContext.saveData.prestige_points = Number(data.prestige_points || 0);
            
            // 解析统计数据
            SaveContext.saveData.highestMoney = Number(data.highestMoney || 0);
            SaveContext.saveData.highestValueGained = Number(data.highestValueGained || 0);
            SaveContext.saveData.totalMoney = Number(data.totalMoney || 0);
            SaveContext.saveData.taskCompleteMoney = Number(data.taskCompleteMoney || 0);
            
            // 解析购买记录
            SaveContext.saveData.purchases = data.purchases || {};
            SaveContext.saveData.oneTimePurchases = data.oneTimePurchases || [];
            
            // 解析游戏值
            SaveContext.saveData.playerCoins = Number(data.playerCoins || 0);
            SaveContext.saveData.attributes = data.attributes || {};
            SaveContext.saveData.helperLevels = data.helperLevels || {};
            SaveContext.saveData.helperBonus = Number(data.helperBonus || 1);
            
            // 解析游戏设置
            SaveContext.saveData.currentScene = data.currentScene || 'SCENE_GRASSLAND';
            SaveContext.saveData.musicVolume = Number(data.musicVolume || 1);
            SaveContext.saveData.soundVolume = Number(data.soundVolume || 1);

            // 解析新手指引
            SaveContext.saveData.newFingerGuide = Number(data.newFingerGuide || 0);
            
            // 解析主线任务ID
            SaveContext.saveData.mainTaskID = Number(data.mainTaskID || 0);

            // 解析主线任务进度
            SaveContext.saveData.mainTaskProgress = Number(data.mainTaskProgress || 0);
            
            // 解析技能碎片目标达成次数
            SaveContext.saveData.skillFragmentTaskProgress = Number(data.skillFragmentTaskProgress || 0);

            // 解析钻石
            SaveContext.saveData.diamond = Number(data.diamond || 0);

            // 解析记录时间戳
            SaveContext.saveData.recordTime = Number(data.recordTime || 0);

            // 解析在线奖励次数
            SaveContext.saveData.onlineRewardCount = Number(data.onlineRewardCount || 0);

            // 解析开始游戏时间
            SaveContext.saveData.startTime = Number(data.startTime || 0);

        } catch (error) {
            console.error("解析存档数据时出错:", error);
            // 出错时重置到默认状态
            StatsContext.resetToDefault();
        }
    }

    /**
     * 将存档数据应用到游戏状态
     */
    private applyToStatsContext(): void {
        // 设置基本数据
        StatsContext.money = SaveContext.saveData.money;
        StatsContext.prestigePoints = SaveContext.saveData.prestige_points;
        StatsContext.highestMoney = SaveContext.saveData.highestMoney;
        StatsContext.highestValueGained = SaveContext.saveData.highestValueGained;
        StatsContext.totalMoney = SaveContext.saveData.totalMoney;  
        StatsContext.taskCompleteMoney = SaveContext.saveData.taskCompleteMoney;
        
        // 设置购买记录
        StatsContext.purchases = SaveContext.saveData.purchases;
        StatsContext.oneTimePurchases = SaveContext.saveData.oneTimePurchases;
        
        // 设置游戏属性值
        // 使用新的初始化方法，它会负责将attributes分配到StatsContext的分类Map中
        StatsContext.initializeCategorizedValues(SaveContext.saveData.attributes);
        
        // 设置特殊数据
        StatsContext.instance.setCoins(SaveContext.saveData.playerCoins);
        StatsContext.instance.setHelperLevelsFromSave(SaveContext.saveData.helperLevels);
        StatsContext.instance.setHelperBonus(SaveContext.saveData.helperBonus);

        // 设置当前场景
        StatsContext.currentScene = SaveContext.saveData.currentScene;

        // 设置音量
        AudioManager.bgmVolume = SaveContext.saveData.musicVolume;
        AudioManager.sfxVolume = SaveContext.saveData.soundVolume;

        // 设置新手指引
        StatsContext.newFingerGuide = SaveContext.saveData.newFingerGuide;
        
        // 设置主线任务ID
        StatsContext.mainTaskID = SaveContext.saveData.mainTaskID;

        //设置主线任务进度
        StatsContext.mainTaskProgress = SaveContext.saveData.mainTaskProgress;

        //设置技能碎片目标达成次数
        StatsContext.skillFragmentTaskProgress = SaveContext.saveData.skillFragmentTaskProgress;

        // 设置钻石
        StatsContext.diamond = SaveContext.saveData.diamond;

        // 设置记录时间戳
        StatsContext.recordTime = SaveContext.saveData.recordTime;

        // 设置在线奖励次数
        StatsContext.onlineRewardCount = SaveContext.saveData.onlineRewardCount;

        // 设置开始游戏时间
        StatsContext.startTime = SaveContext.saveData.startTime;
    }

    /**
     * 重置存档
     */
    public static resetSaveData(): void {
        // 重置存档数据到默认值
        this.saveData = {
            money: 0,
            prestige_points: 0,
            highestMoney: 0,
            highestValueGained: 0,
            totalMoney: 0,
            taskCompleteMoney: 0,
            playerCoins: 0,
            attributes: {},
            purchases: {},
            oneTimePurchases: [],
            helperLevels: {},
            helperBonus: 1,
            currentScene: 'SCENE_GRASSLAND',
            musicVolume: 1,
            soundVolume: 1,
            newFingerGuide: 0,
            mainTaskID: 1,
            mainTaskProgress: 0,
            skillFragmentTaskProgress: 0,
            diamond: 0,
            // 记录时间戳
            recordTime: 0,
            //在线奖励次数
            onlineRewardCount: 0,
            //开始游戏时间
            startTime: 0,
        };
        
        // 清除本地存储
        sys.localStorage.removeItem(this.SAVE_KEY);
        console.log('存档数据已重置');
    }
}