import { _decorator, Component, Node, EventTarget } from 'cc';
import { PURCHASABLE_ITEM_ID } from './ConfigContext';
import { GameEvent } from '../event/GameEvent';
import { SaveContext } from './SaveContext';
import { TaskData } from '../ui/task/TaskData';
const { ccclass, property } = _decorator;


/**
 * 统计上下文
 * 负责管理游戏运行时的状态数据和业务逻辑
 */
@ccclass('StatsContext')
export class StatsContext extends Component {
    /** 单例实例 */
    private static _instance: StatsContext | null = null;

    /** 事件系统，用于状态变更通知 */
    public static events = new EventTarget();

    /** 数量键值 */
    private static readonly QUANTITY_KEYS = new Set<string>([
        'SMALL_COIN', 'MEDIUM_COIN', 'LARGE_COIN', 'DICE', 'HELPER'
    ]);

    /** 修饰键值 */
    private static readonly MODIFIER_KEYS = new Set<string>([
        'REFLIP_CHANCE', 'COIN_FLIP_SPEED_SMALL', 'COIN_FLIP_SPEED_MEDIUM', 'COIN_FLIP_SPEED_LARGE',
        'ADDITIONAL_COIN_VALUE_SMALL', 'ADDITIONAL_COIN_VALUE_MEDIUM', 'ADDITIONAL_COIN_VALUE_LARGE',
        'ADDITIONAL_COIN_VALUE_MULTIPLIER_SMALL', 'ADDITIONAL_COIN_VALUE_MULTIPLIER_MEDIUM', 'ADDITIONAL_COIN_VALUE_MULTIPLIER_LARGE',
        'HELPER_EFFICIENCY'
    ]);

    /** 解锁键值 */
    private static readonly UNLOCK_KEYS = new Set<string>([
        'FLIP_SMALL_COIN_ON_HOVER', 'LARGE_COINS_TRIGGER_COINS', 'HELPER_TRIGGER_MEDIUM_COINS',
        'HELPER_TRIGGER_LARGE_COINS', 'HELPER_TRIGGER_DIE', 'COINS_RESPECT_MOUSE_DIRECTION'
    ]);

    /** 升级硬币 */
    private static readonly UPGRADE_KEYS = new Set<string>([
        'SMALL_COIN','CHANGE_SMALL_TO_MIDDLE', 'CHANGE_MIDDLE_TO_LARGE', 'ADDITIONAL_COIN_VALUE_SMALL', 'ADDITIONAL_COIN_VALUE_MEDIUM', 'ADDITIONAL_COIN_VALUE_LARGE',
    ]);
    /** 雇员 */
    private static readonly EMPLOYEE_KEYS = new Set<string>([
        'EMPLOYEE_COW', 'EMPLOYEE_HORSE', 'EMPLOYEE_BEAR','EMPLOYEE_CAT','EMPLOYEE_CHOOK', 'EMPLOYEE_DINOSAUR', 'EMPLOYEE_DOG','EMPLOYEE_FROG','EMPLOYEE_PANDA','EMPLOYEE_PENGUIN','EMPLOYEE_RABBIT'
    ]);
    /** 技能碎片 */
    private static readonly SKILL_FRAGMENT_KEYS = new Set<string>([
        'SKILL_TEMP_VALUE_ADD_FRAGMENT', 'SKILL_EMERGENCY_MOBILIZATION_FRAGMENT', 'SKILL_IMMEDIATE_PRODUCTION_FRAGMENT', 'SKILL_HIGH_EFFICIENT_WORK_FRAGMENT'
    ]);
    /** 技能 */
    private static readonly SKILL_KEYS = new Set<string>([
        'SKILL_TEMP_VALUE_ADD', 'SKILL_EMERGENCY_MOBILIZATION', 'SKILL_IMMEDIATE_PRODUCTION', 'SKILL_HIGH_EFFICIENT_WORK',"SKILL_SPEED_UP"
    ]);
    /** 场地 */
    private static readonly SCENE_KEYS = new Set<string>([
        'SCENE_BASKETBALL_COURT', 'SCENE_GRASSLAND', 'SCENE_SEASIDE', 'SCENE_WAREHOUSE'
    ]);
    /** 技能碎片任务 */
    private static readonly SKILL_FRAGMENT_TASK_KEYS = new Set<string>([
        'TASK_COIN'
    ]);

    // 存储玩家拥有的各类物品的数量
    private static _itemQuantities: Map<string, number> = new Map<string, number>();
    // 存储各项升级的数值修饰，如几率、速度、额外价值、倍率等
    private static _upgradeModifiers: Map<string, number> = new Map<string, number>();
    // 存储各项功能是否解锁的布尔状态。
    private static _featureUnlocks: Map<string, boolean> = new Map<string, boolean>();
    // 存储玩家当前升级的硬币数量和增值
    private static _upgrade: Map<string, number> = new Map<string, number>();
    // 存储玩家当前雇佣的雇员
    private static _employee: Map<string, number> = new Map<string, number>();
    // 存储玩家技能碎片
    private static _skillFragments: Map<string, number> = new Map<string, number>();
    // 存储玩家当前技能等级
    private static _skill: Map<string, number> = new Map<string, number>();
    // 存储玩家当前场地
    private static _scene: Map<string, number> = new Map<string, number>();
    // 存储玩家当前任务
    private static _skillFragmentTask: Map<string, number> = new Map<string, number>();
    // 技能碎片任务数据
    public static skillFragmentTaskData: number[] = [//10,25,50,75,100,250,500,750,1000,2500,5000,7500,10000,
        10000,20000,30000,40000,50000,60000,70000,80000,90000,//万
        100000,200000,300000,400000,500000,600000,700000,800000,900000,//十万
        1000000,2000000,3000000,4000000,5000000,6000000,7000000,8000000,9000000,//百万
        10000000,25000000,50000000,75000000,//千万
        100000000,250000000,500000000,750000000,//亿
        1000000000,2500000000,5000000000,7500000000,//十亿
        10000000000,25000000000,50000000000,75000000000,//百亿
        100000000000,250000000000,500000000000,750000000000,//千亿
        1000000000000,2500000000000,5000000000000,7500000000000,//兆
        10000000000000,25000000000000,50000000000000,75000000000000,//十兆
        100000000000000,250000000000000,500000000000000,750000000000000,//百兆
        1000000000000000,2500000000000000,5000000000000000,7500000000000000,//千兆
    ];
    
    public static currentScene:string = 'SCENE_GRASSLAND';

    //红点
    public static employeeMaxCount:number = 11;

    //新手指引
    public static newFingerGuide:number = 0;
    
    //主线任务数据
    public static mainTaskData:Map<number,TaskData> = new Map<number,TaskData>();
    public static mainTaskID:number = 1;
    public static mainTaskProgress:number = 0;

    //技能碎片目标达成次数
    public static skillFragmentTaskProgress:number = 0;

    //在线奖励次数
    public static onlineRewardCount:number = 0;

    //开始游戏时间
    public static startTime:number = 0;
    
    // 初始化默认值
    static {
        this.resetCategorizedValuesToDefault();
    }

    private static resetCategorizedValuesToDefault(): void {
        // 物品数量
        this._itemQuantities.clear();
        // this._itemQuantities.set('SMALL_COIN', 1);
        // this._itemQuantities.set('MEDIUM_COIN', 0);
        // this._itemQuantities.set('LARGE_COIN', 0);
        // this._itemQuantities.set('DICE', 0);
        // this._itemQuantities.set('HELPER', 0);

        // 升级修饰
        this._upgradeModifiers.clear();
        this._upgradeModifiers.set('REFLIP_CHANCE', 0.0);
        this._upgradeModifiers.set('COIN_FLIP_SPEED_SMALL', 1.0);
        this._upgradeModifiers.set('COIN_FLIP_SPEED_MEDIUM', 1.0);
        this._upgradeModifiers.set('COIN_FLIP_SPEED_LARGE', 1.0);
        this._upgradeModifiers.set('ADDITIONAL_COIN_VALUE_SMALL', 0);
        this._upgradeModifiers.set('ADDITIONAL_COIN_VALUE_MEDIUM', 0);
        this._upgradeModifiers.set('ADDITIONAL_COIN_VALUE_LARGE', 0);
        this._upgradeModifiers.set('ADDITIONAL_COIN_VALUE_MULTIPLIER_SMALL', 1.0);
        this._upgradeModifiers.set('ADDITIONAL_COIN_VALUE_MULTIPLIER_MEDIUM', 1.0);
        this._upgradeModifiers.set('ADDITIONAL_COIN_VALUE_MULTIPLIER_LARGE', 1.0);
        this._upgradeModifiers.set('HELPER_EFFICIENCY', 1.0);

        // 功能解锁
        this._featureUnlocks.clear();
        this._featureUnlocks.set('FLIP_SMALL_COIN_ON_HOVER', false);
        this._featureUnlocks.set('LARGE_COINS_TRIGGER_COINS', false);
        this._featureUnlocks.set('HELPER_TRIGGER_MEDIUM_COINS', true);
        this._featureUnlocks.set('HELPER_TRIGGER_LARGE_COINS', true);
        this._featureUnlocks.set('HELPER_TRIGGER_DIE', false);
        this._featureUnlocks.set('COINS_RESPECT_MOUSE_DIRECTION', false);

        // 升级
        this._upgrade.clear();
        this._upgrade.set('SMALL_COIN', 0);
        this._upgrade.set('CHANGE_SMALL_TO_MIDDLE', 0);
        this._upgrade.set('CHANGE_MIDDLE_TO_LARGE', 0);
        this._upgrade.set('ADDITIONAL_COIN_VALUE_SMALL', 0);
        this._upgrade.set('ADDITIONAL_COIN_VALUE_MEDIUM', 0);
        this._upgrade.set('ADDITIONAL_COIN_VALUE_LARGE', 0);

        // 雇员
        this._employee.clear();
        this._employee.set('EMPLOYEE_COW', 0);
        this._employee.set('EMPLOYEE_HORSE', 0);
        this._employee.set('EMPLOYEE_BEAR', 0);
        this._employee.set('EMPLOYEE_CAT', 0);
        this._employee.set('EMPLOYEE_CHOOK', 0);
        this._employee.set('EMPLOYEE_DINOSAUR', 0);
        this._employee.set('EMPLOYEE_DOG', 0);
        this._employee.set('EMPLOYEE_FROG', 0);
        this._employee.set('EMPLOYEE_PANDA', 0);
        this._employee.set('EMPLOYEE_PENGUIN', 0);
        this._employee.set('EMPLOYEE_RABBIT', 0);

        // 技能碎片
        this._skillFragments.clear();
        this._skillFragments.set('SKILL_TEMP_VALUE_ADD', 0);
        this._skillFragments.set('SKILL_EMERGENCY_MOBILIZATION', 0);
        this._skillFragments.set('SKILL_IMMEDIATE_PRODUCTION', 0);
        this._skillFragments.set('SKILL_HIGH_EFFICIENT_WORK', 0);

        //技能等级
        this._skill.clear();
        this._skill.set('SKILL_TEMP_VALUE_ADD', 0);
        this._skill.set('SKILL_EMERGENCY_MOBILIZATION', 0);
        this._skill.set('SKILL_IMMEDIATE_PRODUCTION', 0);
        this._skill.set('SKILL_HIGH_EFFICIENT_WORK', 0);

        // 场地
        this._scene.clear();
        this._scene.set('SCENE_BASKETBALL_COURT', 0);
        this._scene.set('SCENE_GRASSLAND', 1);
        this._scene.set('SCENE_SEASIDE', 0);
        this._scene.set('SCENE_WAREHOUSE', 0);

        // 技能碎片任务
        this._skillFragmentTask.clear();
        this._skillFragmentTask.set('TASK_COIN', 0);
    }

    //====================================================================================
    // 游戏主要货币与统计数据
    //====================================================================================
    
    /** 当前金钱 */
    private static _money: number = 0;
    /** 历史最高金钱 */
    private static _highestMoney: number = 0;
    /** 最高单次收益 */
    private static _highestValueGained: number = 0;
    /** 总计获得金钱 */
    private static _totalMoney: number = 0;
    /** 达成目标的金钱 */
    private static _taskCompleteMoney: number = 0;
    /** 声望点数 */
    private static _prestigePoints: number = 0;

    /** 互动物品收益统计 */
    private static _moneyEarnedByInteractable: Map<string, number> = new Map<string, number>();

    /** 购买记录 */
    public static purchases: Record<string, number> = {};
    /** 一次性购买记录 */
    public static oneTimePurchases: string[] = [];

    /** 当前钻石 */
    private static _diamond: number = 0;

    /** 记录时间戳，用于计算离线收益 */
    private static _recordTime: number = 0;

    //====================================================================================
    // 实例属性
    //====================================================================================
    
    /** 玩家拥有的硬币 */
    private _coins: number = 0;

    /** 员工等级 */
    private _helperLevels: Map<string, number> = new Map();

    /** 员工加成倍率 */
    private _helperBonus: number = 1;   

    /**
     * 组件加载
     */
    onLoad() {
        // 设置单例
        StatsContext._instance = this;
    }

    /**
     * 获取单例实例
     */
    public static get instance(): StatsContext {
        return this._instance!;
    }

    //====================================================================================
    // 辅助方法
    //====================================================================================
    private static _getKeyString(key: string | PURCHASABLE_ITEM_ID): string {
        if (typeof key === 'number') {
            return PURCHASABLE_ITEM_ID[key];
        }
        return key.toUpperCase();
    }

    /**
     * 通过键值确定属性类型
     * @param key 键值
     * @returns 属性类型 （数量、修饰、解锁）
     */
    public static getKeyCategory(key: string | PURCHASABLE_ITEM_ID): string {
        const upperKey = key.toUpperCase();
        // 升级
        if(this.UPGRADE_KEYS.has(upperKey)){
            return 'upgrade';
        }
        // 数量
        if (this.QUANTITY_KEYS.has(upperKey)) {  
            return 'quantity';
        }
        // 修饰
        if (this.MODIFIER_KEYS.has(upperKey)) {
            return 'modifier';
        }
        // 解锁
        if (this.UNLOCK_KEYS.has(upperKey)) {
            return 'unlock';
        }
        // 雇员
        if(this.EMPLOYEE_KEYS.has(upperKey)){
            return 'employee';
        }
        // 技能
        if(this.SKILL_KEYS.has(upperKey)){
            return 'skill';
        }
        // 场景
        if(this.SCENE_KEYS.has(upperKey)){
            return 'scene';
        }
        // 任务
        if(this.SKILL_FRAGMENT_TASK_KEYS.has(upperKey)){
            return 'task';
        }

        return 'unknown';
    }   

    //====================================================================================
    // 属性值映射管理 - 分类访问器
    //====================================================================================

    /**
     * 批量初始化属性值 (用于存档加载等)
     * @param values 属性值对象, key为PURCHASABLE_ITEM_ID的字符串形式
     */
    public static initializeCategorizedValues(values: Record<string, any>): void {
        if (values) {
            for (const [key, value] of Object.entries(values)) {
                if(this.UPGRADE_KEYS.has(key)){
                    if (typeof value === 'number') this._upgrade.set(key, value);
                    else console.warn(`StatsContext: 期望升级键值 ${key}, 实际类型为 ${typeof value}`);
                }
                else if (this.QUANTITY_KEYS.has(key)) {
                    if (typeof value === 'number') this._itemQuantities.set(key, value);
                    else console.warn(`StatsContext: 期望数量键值 ${key}, 实际类型为 ${typeof value}`);
                } else if (this.MODIFIER_KEYS.has(key)) {
                    if (typeof value === 'number') this._upgradeModifiers.set(key, value);
                    else console.warn(`StatsContext: 期望修饰键值 ${key}, 实际类型为 ${typeof value}`);
                } else if (this.UNLOCK_KEYS.has(key)) {
                    if (typeof value === 'boolean') this._featureUnlocks.set(key, value);
                    else console.warn(`StatsContext: 期望功能解锁键值 ${key}, 实际类型为 ${typeof value}`);
                } 
                else if(this.EMPLOYEE_KEYS.has(key)){
                    if (typeof value === 'number') this._employee.set(key, value);
                    else console.warn(`StatsContext: 期望雇员键值 ${key}, 实际类型为 ${typeof value}`);
                }
                else if(this.SKILL_FRAGMENT_KEYS.has(key)){
                    if (typeof value === 'number') this._skillFragments.set(key, value);
                    else console.warn(`StatsContext: 期望技能键值 ${key}, 实际类型为 ${typeof value}`);
                }
                else if(this.SKILL_KEYS.has(key)){
                    if (typeof value === 'number') this._skill.set(key, value);
                    else console.warn(`StatsContext: 期望技能键值 ${key}, 实际类型为 ${typeof value}`);
                }   
                else if(this.SCENE_KEYS.has(key)){
                    if (typeof value === 'number') this._scene.set(key, value);
                    else console.warn(`StatsContext: 期望场地键值 ${key}, 实际类型为 ${typeof value}`);
                }else if(this.SKILL_FRAGMENT_TASK_KEYS.has(key)){
                    if (typeof value === 'number') this._skillFragmentTask.set(key, value);
                    else console.warn(`StatsContext: 期望任务键值 ${key}, 实际类型为 ${typeof value}`);
                }
                // 其他非分类键值可以忽略或特殊处理
            }
        }
    }

    // --- 物品数量 ---
    static getItemQuantity(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.QUANTITY_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getItemQuantity: Key '${keyString}' 没有在 QUANTITY_KEYS 中定义.`);
            return 0; // 或者抛出错误
        }
        return this._itemQuantities.get(keyString) || 0;
    }

    static setItemQuantity(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.QUANTITY_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setItemQuantity: Key '${keyString}' 没有在 QUANTITY_KEYS 中定义.`);
            return;
        }
        const oldValue = this._itemQuantities.get(keyString) || 0; // 确保 oldValue 有一个默认值
        
        // 仅当值发生变化时才真正更新和派发事件，以避免不必要的处理（可选优化，但有助于调试）
        // if (value === oldValue) {
        //     console.log(`[StatsContext] setItemQuantity: Key '${keyString}' 的值 (${value}) 未发生变化，不派发事件。`);
        //     return;
        // }

        this._itemQuantities.set(keyString, value);
        
        const eventName = `${keyString}-changed`;
        console.log(`[StatsContext] 即将派发事件: 事件名='${eventName}', newValue=${value}, oldValue=${oldValue}`);
        this.events.emit(eventName, value, oldValue);
    }

    static addItemQuantity(key: string | PURCHASABLE_ITEM_ID, amount: number): void {
        const keyString = this._getKeyString(key);
        if (!this.QUANTITY_KEYS.has(keyString)) {
            console.warn(`[StatsContext] addItemQuantity: Key '${keyString}' 没有在 QUANTITY_KEYS 中定义.`);
            return;
        }
        const currentValue = this._itemQuantities.get(keyString) || 0;
        this.setItemQuantity(keyString, currentValue + amount); // setItemQuantity会触发事件
    }

    // --- 升级修饰  ---
    static getUpgradeModifier(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.MODIFIER_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getUpgradeModifier: Key '${keyString}' 没有在 MODIFIER_KEYS 中定义.`);
            return 0; // 或者根据情况返回1.0等
        }
        // Modifiers 默认值可能不是0，比如倍率是1.0
        const defaultValue = (keyString.includes('MULTIPLIER') || keyString.includes('SPEED') || keyString.includes('EFFICIENCY')) ? 1.0 : 0.0;
        return this._upgradeModifiers.get(keyString) ?? defaultValue;
    }

    static setUpgradeModifier(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.MODIFIER_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setUpgradeModifier: Key '${keyString}' 没有在 MODIFIER_KEYS 中定义.`);
            return;
        }
        const oldValue = this._upgradeModifiers.get(keyString);
        this._upgradeModifiers.set(keyString, value);
        this.events.emit(`${keyString}-changed`, value, oldValue);
    }

    static addUpgradeModifier(key: string | PURCHASABLE_ITEM_ID, amount: number): void {
        const keyString = this._getKeyString(key);
        if (!this.MODIFIER_KEYS.has(keyString)) {
            console.warn(`[StatsContext] addUpgradeModifier: Key '${keyString}' 没有在 MODIFIER_KEYS 中定义.`);
            return;
        }
        const defaultValue = (keyString.includes('MULTIPLIER') || keyString.includes('SPEED') || keyString.includes('EFFICIENCY')) ? 1.0 : 0.0;
        const currentValue = this._upgradeModifiers.get(keyString) ?? defaultValue;
        this.setUpgradeModifier(keyString, currentValue + amount);
    }

    // --- 功能解锁 ---
    static isFeatureUnlocked(key: string | PURCHASABLE_ITEM_ID): boolean {
        const keyString = this._getKeyString(key);
        if (!this.UNLOCK_KEYS.has(keyString)) {
            console.warn(`[StatsContext] isFeatureUnlocked: Key '${keyString}' 没有在 UNLOCK_KEYS 中定义.`);
            return false;
        }
        return this._featureUnlocks.get(keyString) || false;
    }

    static setFeatureUnlock(key: string | PURCHASABLE_ITEM_ID, unlocked: boolean): void {
        const keyString = this._getKeyString(key);
        if (!this.UNLOCK_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setFeatureUnlock: Key '${keyString}' 没有在 UNLOCK_KEYS 中定义.`);
            return;
        }
        const oldValue = this._featureUnlocks.get(keyString);
        this._featureUnlocks.set(keyString, unlocked);
        this.events.emit(`${keyString}-changed`, unlocked, oldValue);
    }

    // --- 升级 ---
    static getUpgrade(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.UPGRADE_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getUpgradeCoins: Key '${keyString}' 没有在 UPGRADE_KEYS 中定义.`);
            return 0;
        }
        return this._upgrade.get(keyString) || 0;
    }

    static setUpgrade(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.UPGRADE_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setUpgradeCoins: Key '${keyString}' 没有在 UNLOCK_KEYS 中定义.`);
            return;
        }
        //判断是否有硬币
        const oldValue = this._upgrade.get(keyString);
        this._upgrade.set(keyString, oldValue+value);
        //铜币转银币,铜币数量要减一
        if(keyString.toLowerCase()==PURCHASABLE_ITEM_ID.CHANGE_SMALL_TO_MIDDLE){
            let smallCount = this._upgrade.get(PURCHASABLE_ITEM_ID.SMALL_COIN.toUpperCase());
            smallCount -= value;
            if(smallCount<0){
                console.warn(`[StatsContext] setUpgradeCoins: Key '${keyString}' 铜币数量异常.`);
                smallCount = 0;
            }
            this._upgrade.set(PURCHASABLE_ITEM_ID.SMALL_COIN.toUpperCase(), smallCount);
        }
        //银币转金币，银币数量要减一
        if(keyString.toLowerCase()==PURCHASABLE_ITEM_ID.CHANGE_MIDDLE_TO_LARGE){
            let middleCount = this._upgrade.get(PURCHASABLE_ITEM_ID.CHANGE_SMALL_TO_MIDDLE.toUpperCase());
            middleCount -= value;
            if(middleCount<0){
                console.warn(`[StatsContext] setUpgradeCoins: Key '${keyString}' 银币数量异常.`);
                middleCount = 0;
            }
            this._upgrade.set(PURCHASABLE_ITEM_ID.CHANGE_SMALL_TO_MIDDLE.toUpperCase(), middleCount);
        }
        this.events.emit(`${keyString}-changed`, oldValue+value, oldValue);
        SaveContext.saveGame();
    }

    // --- 雇员 ---
    static getEmployee(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.EMPLOYEE_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getEmployee: Key '${keyString}' 没有在 EMPLOYEE_KEYS 中定义.`);
            return 0; // 或者抛出错误
        }
        return this._employee.get(keyString) || 0;
    }
    static setEmployee(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.EMPLOYEE_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setEmployee: Key '${keyString}' 没有在 EMPLOYEE_KEYS 中定义.`);
            return;
        }
        const oldValue = this._employee.get(keyString);
        this._employee.set(keyString, value);
        this.events.emit(GameEvent.EMPLOYEE_CHANGED, keyString, value, oldValue);
        SaveContext.saveGame();
    }
    static getEmployeeCount(): number {
        let count = 0;
        for(let key of this.EMPLOYEE_KEYS){
            if(this._employee.get(key) > 0){
                count++;
            }
        }
        return count;
    }

    // --- 技能碎片 ---
    static getSkillFragment(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.SKILL_FRAGMENT_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getSkillFragment: Key '${keyString}' 没有在 SKILL_FRAGMENT_KEYS 中定义.`);
            return 0; // 或者抛出错误
        }
        return this._skillFragments.get(keyString) || 0;
    }
    static setSkillFragment(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.SKILL_FRAGMENT_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setSkillFragment: Key '${keyString}' 没有在 SKILL_FRAGMENT_KEYS 中定义.`);
            return;
        }
        const oldValue = this._skillFragments.get(keyString);
        this._skillFragments.set(keyString, value);
        this.events.emit(GameEvent.SKILL_FRAGMENT_CHANGED, keyString, value, oldValue);
        SaveContext.saveGame();
    }
    // --- 技能 ---
    static getSkill(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.SKILL_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getSkill: Key '${keyString}' 没有在 SKILL_KEYS 中定义.`);
            return 0; // 或者抛出错误
        }
        return this._skill.get(keyString) || 0;
    }
    static setSkill(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.SKILL_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setSkill: Key '${keyString}' 没有在 SKILL_KEYS 中定义.`);
            return;
        }
        const oldValue = this._skill.get(keyString);
        this._skill.set(keyString, oldValue+value);
        this.events.emit(GameEvent.SKILL_LEVEL_CHANGED, key, value, oldValue);
        SaveContext.saveGame();
    }

    // --- 场地 ---
    static getScene(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.SCENE_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getScene: Key '${keyString}' 没有在 SCENE_KEYS 中定义.`);
            return 0; // 或者抛出错误
        }
        return this._scene.get(keyString) || 0;
    }
    static setScene(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.SCENE_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setScene: Key '${keyString}' 没有在 SCENE_KEYS 中定义.`);
            return;
        }
        // const oldScene = this.currentScene;
        this.currentScene = keyString;
        const oldValue = this._scene.get(keyString);
        this._scene.set(keyString, value);
        this.events.emit(GameEvent.SCENE_CHANGED, keyString, value, oldValue);
        SaveContext.saveGame();
    }
    
    // --- 技能碎片任务 ---
    static getSkillFragmentTask(key: string | PURCHASABLE_ITEM_ID): number {
        const keyString = this._getKeyString(key);
        if (!this.SKILL_FRAGMENT_TASK_KEYS.has(keyString)) {
            console.warn(`[StatsContext] getTask: Key '${keyString}' 没有在 TASK_KEYS 中定义.`);
            return 0; // 或者抛出错误
        }
        return this._skillFragmentTask.get(keyString) || 0;
    }
    static setSkillFragmentTask(key: string | PURCHASABLE_ITEM_ID, value: number): void {
        const keyString = this._getKeyString(key);
        if (!this.SKILL_FRAGMENT_TASK_KEYS.has(keyString)) {
            console.warn(`[StatsContext] setTask: Key '${keyString}' 没有在 TASK_KEYS 中定义.`);
            return;
        }
        const oldValue = this._skillFragmentTask.get(keyString);
        this._skillFragmentTask.set(keyString, value);
        this.events.emit(GameEvent.SKILL_FRAGMENT_TASK_COMPLETE, keyString, value, oldValue);
        SaveContext.saveGame();
    }
    
    //====================================================================================
    // 游戏货币与数值访问器
    //====================================================================================
    
    /** 当前金钱 */
    static get money(): number { return this._money; }
    static set money(value: number) {
        if (value > this._money) {
            this._totalMoney += (value - this._money);
        }
        this._money = value;
        this.events.emit(GameEvent.MONEY_CHANGE, this._money);

        if (value > this._highestMoney) {
            this._highestMoney = value;
        }
    }

    /** 历史最高金钱 */
    static get highestMoney(): number { return this._highestMoney; }
    static set highestMoney(value: number) { this._highestMoney = value; }

    /** 最高单次收益 */
    static get highestValueGained(): number { return this._highestValueGained; }
    static set highestValueGained(value: number) { this._highestValueGained = value; }

    /** 总计获得金钱 */
    static get totalMoney(): number { return this._totalMoney; }
    static set totalMoney(value: number) { this._totalMoney = value; }

    /** 达成目标的金钱 */
    static get taskCompleteMoney(): number { return this._taskCompleteMoney; }
    static set taskCompleteMoney(value: number) { this._taskCompleteMoney = value; }

    /** 声望点数 */
    static get prestigePoints(): number { return this._prestigePoints; }
    static set prestigePoints(value: number) {
        this._prestigePoints = value;
        this.events.emit('prestige-points-changed', this._prestigePoints);
    }

    /** 互动物品收益统计 */
    static get moneyEarnedByInteractable(): Map<string, number> { return this._moneyEarnedByInteractable; }
    
    /** 当前钻石 */
    static get diamond(): number { return this._diamond; }
    static set diamond(value: number) { 
        this._diamond = value; 
        this.events.emit(GameEvent.DIAMOND_CHANGE, this._diamond);
    }

    /** 记录时间戳，用于计算离线收益 */
    static get recordTime(): number { return this._recordTime; }
    static set recordTime(value: number) { this._recordTime = value; }

    //====================================================================================
    // 玩家金币管理
    //====================================================================================
    
    /**
     * 获取玩家金币数量
     */
    public getCoins(): number {
        return this._coins;
    }

    /**
     * 设置玩家金币数量
     * @param coins 金币数量
     */
    public setCoins(coins: number): void {
        this._coins = coins;
    }

    /**
     * 增加玩家金币
     * @param amount 增加数量
     */
    public addCoins(amount: number): void {
        this._coins += amount;
    }

    /**
     * 消费玩家金币
     * @param amount 消费数量
     * @returns 是否消费成功
     */
    public consumeCoins(amount: number): boolean {
        if (this._coins >= amount) {
            this._coins -= amount;
            return true;
        }
        return false;
    }

    //====================================================================================
    // 员工系统管理
    //====================================================================================
    
    /**
     * 获取所有员工等级
     */
    public getAllHelperLevels(): Map<string, number> {
        return this._helperLevels;
    }

    /**
     * 从存档设置员工等级
     * @param helperLevels 员工等级数据
     */
    public setHelperLevelsFromSave(helperLevels: Record<string, number>): void {
        this._helperLevels.clear();
        for (const [helperId, level] of Object.entries(helperLevels)) {
            this._helperLevels.set(helperId, level);
        }
    }

    /**
     * 获取指定员工等级
     * @param helperId 员工ID
     */
    public getHelperLevel(helperId: string): number {
        return this._helperLevels.get(helperId) || 0;
    }

    /**
     * 设置员工等级
     * @param helperId 员工ID
     * @param level 等级
     */
    public setHelperLevel(helperId: string, level: number): void {
        this._helperLevels.set(helperId, level);
    }

    /**
     * 获取员工加成倍率
     */
    public getHelperBonus(): number {
        return this._helperBonus;
    }

    /**
     * 设置员工加成倍率
     * @param bonus 加成倍率
     */
    public setHelperBonus(bonus: number): void {
        this._helperBonus = bonus;
    }


    //====================================================================================
    // 系统状态管理
    //====================================================================================
    
    /**
     * 重置游戏状态到默认值
     * 用于清除存档或重新开始游戏
     */
    public static resetToDefault(): void {
        // 重置分类属性
        this.resetCategorizedValuesToDefault();

        // 重置数值
        this._money = 0;
        this._highestMoney = 0;
        this._highestValueGained = 0;
        this._totalMoney = 0;
        this._taskCompleteMoney = 0;
        this._prestigePoints = 0;

        // 重置交互物品统计
        this._moneyEarnedByInteractable.clear();

        // 重置购买记录
        this.purchases = {};
        this.oneTimePurchases = [];

        // 重置实例属性
        if (this._instance) {
            this._instance._coins = 0;
            this._instance._helperLevels.clear();
            this._instance._helperBonus = 1;
        }

        //重置默认场景
        this.currentScene = 'SCENE_GRASSLAND';

        //重置新手指引
        this.newFingerGuide = 0;

        //重置主线任务ID
        this.mainTaskID = 1;
        //重置主线任务进度
        this.mainTaskProgress = 0;

        //技能碎片目标达成次数
        this.skillFragmentTaskProgress = 0;

        // 发出重置事件
        this.events.emit('stats-reset');

        // 重置钻石
        this.diamond = 0;
    }
}