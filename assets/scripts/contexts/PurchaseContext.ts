import { TimeHelper } from '../utils/TimeHelper';
import { ConfigContext, PURCHASABLE_ITEM_ID, PurchasableItemConfig } from './ConfigContext';
import { StatsContext } from './StatsContext';

/**
 * 购买上下文，用于处理所有与物品购买相关的逻辑
 */
export class PurchaseContext {
    /**
     * 获取物品已购买数量
     * @param itemId 物品ID (在内部处理为小写)
     * @returns 已购买数量
     */
    public static getPurchaseAmount(itemId: string): number {
        if (!StatsContext.purchases) {
            return 0;
        }
        // 确保使用小写键进行查找
        return StatsContext.purchases[itemId.toLowerCase()] || 0;
    }

    /**
     * 检查是否可以显示某物品的购买选项
     * @param itemId 物品ID (在内部处理为小写)
     * @returns 是否可以显示
     */
    public static canShowItem(itemId: string): boolean {
        const lowerCaseItemId = itemId.toLowerCase();
        // ConfigContext.getPurchasableItem 可能需要 PURCHASABLE_ITEM_ID 类型
        // 假设 ConfigContext.canSee 等方法能处理 string 类型的 idKey
        const config = ConfigContext.getPurchasableItemConfig(lowerCaseItemId as PURCHASABLE_ITEM_ID);
        if (!config) {
            console.warn(`[PurchaseContext] 物品配置不存在: ${lowerCaseItemId}`);
            return false;
        }

        const purchaseAmount = this.getPurchaseAmount(lowerCaseItemId);
        return ConfigContext.canSee(lowerCaseItemId, {
            ...this.getGameStateForContext(),
            purchase_amount: purchaseAmount
        });
    }

    /**
     * 检查是否满足物品的初始购买条件 (不包括货币和是否可再次购买)
     * @param itemId 物品ID (在内部处理为小写)
     * @returns 是否满足初始购买条件
     */
    public static meetsInitialBuyRequirements(itemId: string): boolean {
        const lowerCaseItemId = itemId.toLowerCase();
        const purchaseAmount = this.getPurchaseAmount(lowerCaseItemId);
        return ConfigContext.canBuy(lowerCaseItemId, {
            ...this.getGameStateForContext(),
            purchase_amount: purchaseAmount
        });
    }
    
    /**
     * 检查物品是否可以再次购买 (例如未达到最大购买量)
     * @param itemId 物品ID (在内部处理为小写)
     * @returns 是否可以再次购买
     */
    public static canBuyAgain(itemId: string): boolean {
        const lowerCaseItemId = itemId.toLowerCase();
        const purchaseAmount = this.getPurchaseAmount(lowerCaseItemId);
        return ConfigContext.canBuyAgain(lowerCaseItemId, purchaseAmount);
    }

    /**
     * 获取物品的当前价格
     * @param itemId 物品ID (在内部处理为小写)
     * @returns 物品价格
     */
    public static getItemPrice(itemId: string): number {
        const lowerCaseItemId = itemId.toLowerCase();
        const purchaseAmount = this.getPurchaseAmount(lowerCaseItemId);
        return ConfigContext.calculatePrice(lowerCaseItemId, purchaseAmount);
    }

    /**
     * 尝试执行购买操作
     * @param itemId 物品ID (大小写不敏感，内部转为小写处理)
     * @returns boolean 是否购买成功
     */
    public static attemptPurchase(itemId: string): boolean {
        const lowerCaseItemId = itemId.toLowerCase();
        const config = ConfigContext.getPurchasableItemConfig(lowerCaseItemId as PURCHASABLE_ITEM_ID);

        if (!config) {
            console.warn(`[PurchaseContext] 尝试购买失败: 物品配置不存在 ${lowerCaseItemId}`);
            return false;
        }

        // 1. 检查显示条件 (如果一个物品不能显示，通常也不应该能被购买)
        if (!this.canShowItem(lowerCaseItemId)) {
            console.log(`[PurchaseContext] 尝试购买失败: ${lowerCaseItemId} 不满足显示条件`);
            return false;
        }

        // 2. 检查初始购买条件
        if (!this.meetsInitialBuyRequirements(lowerCaseItemId)) {
            console.log(`[PurchaseContext] 尝试购买失败: ${lowerCaseItemId} 不满足初始购买条件`);
            return false;
        }
        
        // 3. 检查是否可再次购买 (例如数量上限)
        if (!this.canBuyAgain(lowerCaseItemId)) {
            console.log(`[PurchaseContext] 尝试购买失败: ${lowerCaseItemId} 无法再次购买 (可能已达上限)`);
            return false;
        }

        // 4. 检查货币是否足够
        const price = this.getItemPrice(lowerCaseItemId);
        if (StatsContext.diamond < price) {
            console.log(`[PurchaseContext] 尝试购买失败: 钻石不足，购买 ${lowerCaseItemId} 需要 ${price}, 当前拥有 ${StatsContext.diamond}`);
            return false;
        }

        // 执行购买
        // a. 扣除货币
        StatsContext.diamond -= price;

        // b. 更新购买记录
        if (!StatsContext.purchases) {
            // 初始化 _purchases 对象，如果它还不存在
            (StatsContext as any)._purchases = {};
        }
        StatsContext.purchases[lowerCaseItemId] = (StatsContext.purchases[lowerCaseItemId] || 0) + 1;
        
        // c. 应用物品效果
        const increaseAmount = config.increase_amounts; // 这个值应该由 ConfigContext.getPurchasableItem 提供
        const keyCategory = StatsContext.getKeyCategory(lowerCaseItemId); // 这个方法需要存在于 StatsContext

        if (keyCategory === 'quantity') {
            StatsContext.addItemQuantity(lowerCaseItemId as PURCHASABLE_ITEM_ID, increaseAmount);
        } else if (keyCategory === 'modifier') {
            StatsContext.setUpgradeModifier(lowerCaseItemId as PURCHASABLE_ITEM_ID, increaseAmount);
        } else if (keyCategory === 'unlock') {
            // 对于解锁类型的物品，增加量通常不重要，关键是状态变为 true
            StatsContext.setFeatureUnlock(lowerCaseItemId as PURCHASABLE_ITEM_ID, true);
        }else if(keyCategory === 'upgrade'){
            StatsContext.setUpgrade(lowerCaseItemId as PURCHASABLE_ITEM_ID, increaseAmount);
        }else if(keyCategory === 'employee'){
            StatsContext.setEmployee(lowerCaseItemId as PURCHASABLE_ITEM_ID, TimeHelper.getCurrentTimestampInSeconds());
        }else if(keyCategory === 'scene'){
            StatsContext.setScene(lowerCaseItemId as PURCHASABLE_ITEM_ID, TimeHelper.getCurrentTimestampInSeconds());
        }
        else {
            console.warn(`[PurchaseContext] 未知的物品效果类别 '${keyCategory}' for item '${lowerCaseItemId}'`);
        }
        
        console.log(`[PurchaseContext] 成功购买物品: ${lowerCaseItemId}, 价格: ${price}, 新购买数量: ${StatsContext.purchases[lowerCaseItemId]}`);
        // TODO: 触发购买成功事件 EventManager.emit('itemPurchased', lowerCaseItemId);
        return true;
    }

    /**
     * 尝试执行升级操作
     * @param itemId 物品ID (大小写不敏感，内部转为小写处理)
     * @returns boolean 是否购买成功
     */
    public static attemptUpgradePurchase(itemId: string): boolean {
        const lowerCaseItemId = itemId.toLowerCase();
        const config = ConfigContext.getPurchasableItemConfig(lowerCaseItemId as PURCHASABLE_ITEM_ID);

        if (!config) {
            console.warn(`[PurchaseContext] 尝试升级失败: 物品配置不存在 ${lowerCaseItemId}`);
            return false;
        }

        // 1. 检查显示条件 (如果一个物品不能显示，通常也不应该能被购买)
        // if (!this.canShowItem(lowerCaseItemId)) {
        //     console.log(`[PurchaseContext] 尝试购买失败: ${lowerCaseItemId} 不满足显示条件`);
        //     return false;
        // }

        // 2. 检查初始升级条件
        // if (!this.meetsInitialUpgradeRequirements(lowerCaseItemId)) {
        //     console.log(`[PurchaseContext] 尝试升级失败: ${lowerCaseItemId} 不满足初始升级条件`);
        //     return false;
        // }
        
        // 3. 检查是否可再次升级 (例如数量上限)
        if (!this.canBuyAgain(lowerCaseItemId)) {
            console.log(`[PurchaseContext] 尝试升级失败: ${lowerCaseItemId} 无法再次升级 (可能已达上限)`);
            return false;
        }

        // 4. 检查碎片是否足够
        const level = StatsContext.getSkill(lowerCaseItemId);
        let hasFragment = StatsContext.getSkillFragment(lowerCaseItemId+"_fragment");
        const needFragment = config.price_expressions.base + level*config.price_expressions.multiplier + config.price_expressions.flat_offset;
        if (hasFragment < needFragment) {
            console.log(`[PurchaseContext] 尝试升级失败: 碎片不足，升级 ${lowerCaseItemId} 需要 ${needFragment}, 当前拥有 ${hasFragment}`);
            return false;
        }

        // 执行升级
        // a. 扣除碎片
        hasFragment -= needFragment;
        StatsContext.setSkillFragment(lowerCaseItemId+"_fragment", hasFragment);

        // b. 更新购买记录
        if (!StatsContext.purchases) {
            // 初始化 _purchases 对象，如果它还不存在
            (StatsContext as any)._purchases = {};
        }
        StatsContext.purchases[lowerCaseItemId] = (StatsContext.purchases[lowerCaseItemId] || 0) + 1;
        
        // c. 应用物品效果
        const increaseAmount = config.increase_amounts; // 这个值应该由 ConfigContext.getPurchasableItem 提供
        const keyCategory = StatsContext.getKeyCategory(lowerCaseItemId); // 这个方法需要存在于 StatsContext

        if(keyCategory === 'skill'){
            StatsContext.setSkill(lowerCaseItemId as PURCHASABLE_ITEM_ID, increaseAmount);
        }
        else {
            console.warn(`[PurchaseContext] 未知的物品效果类别 '${keyCategory}' for item '${lowerCaseItemId}'`);
        }
        
        console.log(`[PurchaseContext] 成功升级技能: ${lowerCaseItemId}, 消耗碎片: ${needFragment}, 新技能等级: ${StatsContext.purchases[lowerCaseItemId]}`);
        // TODO: 触发购买成功事件 EventManager.emit('itemPurchased', lowerCaseItemId);
        return true;
    }

    /**
     * 获取当前游戏状态，用于ConfigContext条件评估
     * @returns 包含当前游戏关键数据的对象
     */
    private static getGameStateForContext(): Record<string, number | boolean> {
        // 确保这里的键名与 ConfigContext 中条件检查所期望的一致
        return {
            small_coin: StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.SMALL_COIN),//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.SMALL_COIN),
            medium_coin: StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.CHANGE_SMALL_TO_MIDDLE),//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.MEDIUM_COIN),
            large_coin: StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.CHANGE_MIDDLE_TO_LARGE),//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.LARGE_COIN),
            helper: StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.HELPER),
            // 原 Purchasable.ts 中用 'die' 作为键名
            die: StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.DICE), 
            money: StatsContext.money
            // 未来可能需要添加已解锁特性等，例如：
            // feature_x_unlocked: StatsContext.isFeatureUnlocked(PURCHASABLE_ITEM_ID.FEATURE_X) 
        };
    }
} 