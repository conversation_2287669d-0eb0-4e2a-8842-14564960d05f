import { _decorator, Component, JsonAsset, resources, ValueType } from 'cc';
import { StatsContext } from './StatsContext';
const { ccclass, property } = _decorator;

// 商店物品ID枚举
export enum PURCHASABLE_ITEM_ID {
    DICE = 'dice', // 骰子

    SMALL_COIN = 'small_coin', // 小硬币
    MEDIUM_COIN = 'medium_coin', // 中硬币
    LARGE_COIN = 'large_coin', // 大硬币

    REFLIP_CHANCE = 'reflip_chance', // 重翻几率
    FLIP_SMALL_COIN_ON_HOVER = 'flip_small_coin_on_hover', // 小硬币翻转特效
    LARGE_COINS_TRIGGER_COINS = 'large_coins_trigger_coins', // 大硬币触发硬币

    HELPER_EFFICIENCY = 'helper_efficiency', // 员工效率倍率   
    HELPER_TRIGGER_MEDIUM_COINS = 'helper_trigger_medium_coins', // 员工触发中硬币
    HELPER_TRIGGER_LARGE_COINS = 'helper_trigger_large_coins', // 员工触发大硬币
    HELPER_TRIGGER_DIE = 'helper_trigger_die', // 员工触发骰子
    HELPER = 'helper', // 员工

    COIN_FLIP_SPEED_SMALL = 'coin_flip_speed_small', // 小硬币翻转速度倍率
    COIN_FLIP_SPEED_MEDIUM = 'coin_flip_speed_medium', // 中硬币翻转速度倍率
    COIN_FLIP_SPEED_LARGE = 'coin_flip_speed_large', // 大硬币翻转速度倍率

    ADDITIONAL_COIN_VALUE_SMALL = 'additional_coin_value_small', // 小硬币增加值   
    ADDITIONAL_COIN_VALUE_MEDIUM = 'additional_coin_value_medium', // 中硬币增加值
    ADDITIONAL_COIN_VALUE_LARGE = 'additional_coin_value_large', // 大硬币增加值

    ADDITIONAL_COIN_VALUE_MULTIPLIER_SMALL = 'additional_coin_value_multiplier_small', // 小硬币增加值倍率
    ADDITIONAL_COIN_VALUE_MULTIPLIER_MEDIUM = 'additional_coin_value_multiplier_medium', // 中硬币增加值倍率
    ADDITIONAL_COIN_VALUE_MULTIPLIER_LARGE = 'additional_coin_value_multiplier_large', // 大硬币增加值倍率

    CHANGE_SMALL_TO_MIDDLE = 'change_small_to_middle', // 小硬币变成中硬币
    CHANGE_MIDDLE_TO_LARGE = 'change_middle_to_large', // 中硬币变成大硬币

    SKILL_TEMP_VALUE_ADD = 'skill_temp_value_add', // 临时增值
    SKILL_EMERGENCY_MOBILIZATION = 'skill_emergency_mobilization', // 紧急动员
    SKILL_IMMEDIATE_PRODUCTION = 'skill_immediate_production', // 立即生产
    SKILL_HIGH_EFFICIENT_WORK = 'skill_high_efficient_work', // 高效工作

    EMPLOYEE_BEAR = 'employee_bear', // 熊
    EMPLOYEE_CAT = 'employee_cat', // 猫
    EMPLOYEE_CHOOK = 'employee_chook', // 鸡
    EMPLOYEE_COW = 'employee_cow', // 牛
    EMPLOYEE_DINOSAUR = 'employee_dinosaur', // 恐龙
    EMPLOYEE_DOG = 'employee_dog', // 狗
    EMPLOYEE_FROG = 'employee_frog', // 青蛙
    EMPLOYEE_HORSE = 'employee_horse', // 马
    EMPLOYEE_PANDA = 'employee_panda', // 熊猫
    EMPLOYEE_PENGUIN = 'employee_penguin', // 企鹅
    EMPLOYEE_RABBIT = 'employee_rabbit', // 兔子
    
    SCENE_BASKETBALL_COURT = 'scene_basketball_court', // 篮球场
    SCENE_GRASSLAND = 'scene_grassland', // 绿地
    SCENE_SEASIDE = 'scene_seaside', // 沙滩
    SCENE_WAREHOUSE = 'scene_warehouse', // 仓库

    TASK_COIN = 'task_coin', // 金币任务
}
export enum SHOP_ITEM_TYPE {
    UPGRADE = 'upgrade', // 升级
    EMPLOYEE = 'employee', // 雇员
    SKILL = 'skill', // 技能
    SCENE = 'scene', // 场景
}

// 商店物品
export interface PurchasableItemConfig {
    name: string;
    desc: string;
    category: string;
    requirement_to_buy: string;
    requirement_to_see: string;
    increase_amounts: number;
    max_purchase_amounts: number;
    price_expressions: { base: number, multiplier: number, flat_offset: number };
    expiration_data:number;
    sellType:number; // 1:AD 2:金币购买
    spriteName:string;// 图标名称
    skill: { cd: number, cd_addition: number, validity_time: number, validity_time_multiplier: number, result:number,result_multiplier:number };
}

/**
 * 游戏配置上下文类
 * 管理游戏中各种配置
 */
@ccclass('ConfigContext')
export class ConfigContext extends Component {
    @property(JsonAsset)
    gameValuesAsset: JsonAsset = null;

    @property(JsonAsset)
    purchasableItemsAsset: JsonAsset = null;

    // 大中小硬币基础值
    static get smallCoinBaseValue(): number { return this._baseValues.small_coin; }
    static get mediumCoinBaseValue(): number { return this._baseValues.medium_coin; }
    static get largeCoinBaseValue(): number { return this._baseValues.large_coin; }

    // 大中小硬币基础值
    private static _baseValues: Record<string, number> = {};

    // 商店物品
    private static _purchasableItems: Record<string, PurchasableItemConfig> = {};

    onLoad() {
        const gameValues = this.gameValuesAsset.json;
        const purchasableItems = this.purchasableItemsAsset.json;

        // 加载基础值
        ConfigContext._baseValues = gameValues.base_values || {};

        // 价格表达式配置
        ConfigContext._purchasableItems = purchasableItems || {};
    }

    start() {

    }

    /**
     * 获取大中小硬币基础值
     * @param key 基础值键名
     * @returns 基础值
     */
    static getBaseValue(key: string): number {
        const baseValue = ConfigContext._baseValues[key.toLowerCase()];
        if (!baseValue) {
            console.error(`未找到基础值: ${key}`);
            return 0;
        }
        return baseValue;
    }

    /**
     * 根据商店物品ID枚举获取商店物品配置PurchasableItem
     * @param id 商店物品ID枚举
     * @returns 商店物品配置PurchasableItem
     */
    static getPurchasableItemConfig(id: PURCHASABLE_ITEM_ID): PurchasableItemConfig {
        const item = ConfigContext._purchasableItems[id.toLowerCase()];
        if (!item) {
            console.error(`未找到商店物品配置: ${id}`);
            return null;
        }
        return item;
    }

    /**
     * 计算价格
     * @param key 价格表达式键名
     * @param level 当前等级
     * @returns 计算后的价格
     */
    static calculatePrice(key: string, level: number): number {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到价格表达式: ${key}`);
            return 0;
        }

        const { base, multiplier, flat_offset } = item.price_expressions;
        // 使用与Godot版本相同的公式: multiplier * (base^level) + flat_offset
        const price = multiplier * Math.pow(base, level) + flat_offset;
        return Math.floor(price);
    }

    /**
     * 评估需求条件
     * @param requirement 需求条件表达式
     * @param gameState 游戏状态
     * @returns 是否满足需求
     */
    static evaluateRequirement(requirement: string, gameState: Record<string, number>): boolean {
        if (!requirement) return true;

        // 处理AND条件
        if (requirement.includes('&&')) {
            const conditions = requirement.split('&&');
            return conditions.every(cond => ConfigContext.evaluateRequirement(cond.trim(), gameState));
        }

        // 处理简单条件
        const parts = requirement.split(' ');
        if (parts.length === 3) {
            const [variable, operator, value] = parts;
            const stateValue = gameState[variable] || 0;
            const compareValue = Number(value);

            switch (operator) {
                case '>': return stateValue > compareValue;
                case '>=': return stateValue >= compareValue;
                case '<': return stateValue < compareValue;
                case '<=': return stateValue <= compareValue;
                case '==': return stateValue === compareValue;
                case '!=': return stateValue !== compareValue;
            }
        }

        return false;
    }

    /**
     * 检查购买需求是否满足
     * @param key 项目键名
     * @param gameState 游戏状态
     * @returns 是否满足购买需求
     */
    static canBuy(key: string, gameState: Record<string, number>): boolean {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到购买需求: ${key}`);
            return false;
        }
        const requirement = item.requirement_to_buy;
        return ConfigContext.evaluateRequirement(requirement, gameState);
    }
    /**
     * 检查升级需求是否满足
     * @param key 项目键名
     * @param gameState 游戏状态
     * @returns 是否满足购买需求
     */
    static canUpgrade(key: string, gameState: Record<string, number>): boolean {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到购买需求: ${key}`);
            return false;
        }
        //升级需要的碎片数量
        const level = StatsContext.getSkill(key);
        const hasFragment = StatsContext.getSkillFragment(key+"_fragment");
        const needFragment = level * item.price_expressions.multiplier + item.price_expressions.flat_offset;
        return hasFragment>0 && hasFragment>=needFragment;
    }

    /**
     * 检查查看需求是否满足
     * @param key 项目键名
     * @param gameState 游戏状态
     * @returns 是否满足查看需求
     */
    static canSee(key: string, gameState: Record<string, number>): boolean {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到查看需求: ${key}`);
            return false;
        }
        const requirement = item.requirement_to_see;
        return ConfigContext.evaluateRequirement(requirement, gameState);
    }

    /**
     * 获取增加量
     * @param key 项目键名
     * @returns 增加量
     */
    static getIncreaseAmount(key: string): number {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到增加量: ${key}`);
            return 0;
        }
        return item.increase_amounts;
    }

    /**
     * 获取最大购买量
     * @param key 项目键名
     * @returns 最大购买量
     */
    static getMaxPurchaseAmount(key: string): number {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到最大购买量: ${key}`);
            return 0;
        }
        return item.max_purchase_amounts;
    }


    /**
     * 检查是否已达到最大购买量
     * @param key 项目键名
     * @param currentAmount 当前拥有数量
     * @returns 是否可以继续购买
     */
    static canBuyAgain(key: string, currentAmount: number): boolean {
        const item = ConfigContext.getPurchasableItemConfig(key as PURCHASABLE_ITEM_ID);
        if (!item) {
            console.error(`未找到最大购买量: ${key}`);
            return false;
        }
        const maxAmount = item.max_purchase_amounts;
        return currentAmount < maxAmount;
    }
}