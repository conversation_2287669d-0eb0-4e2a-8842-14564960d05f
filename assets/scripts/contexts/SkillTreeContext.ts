import { _decorator, Component, Node } from 'cc';
import { SaveContext } from './SaveContext';

const { ccclass, property } = _decorator;

/**
 * 技能信息接口
 */
interface SkillInfo {
    id: string;           // 技能ID
    name: string;         // 技能名称
    description: string;  // 技能描述
    maxLevel: number;     // 最大等级
    baseCost: number;     // 基础升级消耗
    costMultiplier: number; // 升级消耗倍率
    baseEffect: number;   // 基础技能效果
    type: SkillType;      // 技能类型
    prerequisiteSkills: string[]; // 前置技能要求
}

/**
 * 技能类型枚举
 */
enum SkillType {
    CoinBonus,            // 硬币产出加成
    HelperBonus,          // 员工产出加成
    DiceBonus,            // 骰子产出加成
    SpecialAbility,       // 特殊能力
    CriticalChance,       // 暴击几率
    CriticalMultiplier,   // 暴击倍率
    Utility               // 功能性技能
}

/**
 * 技能树上下文 - 管理技能树状态和升级
 */
@ccclass('SkillTreeContext')
export class SkillTreeContext extends Component {

    private static _instance: SkillTreeContext | null = null;
    private static readonly SAVE_KEY_PREFIX: string = 'gamblers_table_skill_tree';

    // 所有技能数据
    private _allSkills: Map<string, SkillInfo> = new Map();

    // 已解锁技能等级
    private _unlockedSkills: Map<string, number> = new Map();

    // 总共已投入的技能点
    private _totalSkillPointsSpent: number = 0;

    // 可用技能点
    private _availableSkillPoints: number = 0;

    onLoad() {
        // 单例模式
        if (SkillTreeContext._instance !== null) {
            this.node.destroy();
            return;
        }

        SkillTreeContext._instance = this;

        // 初始化技能树数据
        this.initializeSkillTree();

        // 从存档加载数据
        this.loadFromSave();
    }

    /**
     * 获取单例
     */
    static get instance(): SkillTreeContext {
        return SkillTreeContext._instance!;
    }

    /**
     * 初始化技能树数据
     */
    private initializeSkillTree() {
        // 添加硬币加成技能
        this._allSkills.set("coin_bonus_1", {
            id: "coin_bonus_1",
            name: "硬币加成 I",
            description: "增加基础硬币产出 10% (每级)",
            maxLevel: 10,
            baseCost: 100,
            costMultiplier: 1.5,
            baseEffect: 0.1,
            type: SkillType.CoinBonus,
            prerequisiteSkills: []
        });

        // 添加员工加成技能
        this._allSkills.set("helper_bonus_1", {
            id: "helper_bonus_1",
            name: "员工加成 I",
            description: "增加员工产出 5% (每级)",
            maxLevel: 10,
            baseCost: 500,
            costMultiplier: 1.5,
            baseEffect: 0.05,
            type: SkillType.HelperBonus,
            prerequisiteSkills: ["coin_bonus_1"]
        });

        // 添加骰子加成技能
        this._allSkills.set("dice_bonus_1", {
            id: "dice_bonus_1",
            name: "骰子加成 I",
            description: "增加骰子产出 15% (每级)",
            maxLevel: 5,
            baseCost: 1000,
            costMultiplier: 2.0,
            baseEffect: 0.15,
            type: SkillType.DiceBonus,
            prerequisiteSkills: ["coin_bonus_1"]
        });

        // 添加暴击几率技能
        this._allSkills.set("crit_chance_1", {
            id: "crit_chance_1",
            name: "暴击几率 I",
            description: "增加暴击几率 2% (每级)",
            maxLevel: 20,
            baseCost: 2000,
            costMultiplier: 1.5,
            baseEffect: 0.02,
            type: SkillType.CriticalChance,
            prerequisiteSkills: ["helper_bonus_1", "dice_bonus_1"]
        });

        // 添加暴击倍率技能
        this._allSkills.set("crit_multi_1", {
            id: "crit_multi_1",
            name: "暴击倍率 I",
            description: "增加暴击倍率 0.2x (每级)",
            maxLevel: 10,
            baseCost: 5000,
            costMultiplier: 2.0,
            baseEffect: 0.2,
            type: SkillType.CriticalMultiplier,
            prerequisiteSkills: ["crit_chance_1"]
        });

        // 添加更多技能...
    }

    /**
     * 从存档加载数据
     */
    private loadFromSave() {
        // 加载技能数据
        const savedSkills = this.getObjectFromSave('unlockedSkills');
        if (savedSkills) {
            this._unlockedSkills = new Map(Object.entries(savedSkills));
        }

        // 加载技能点数据
        this._totalSkillPointsSpent = this.getNumberFromSave('totalSkillPointsSpent') || 0;
        this._availableSkillPoints = this.getNumberFromSave('availableSkillPoints') || 0;
    }

    /**
     * 从存档中获取对象
     */
    private getObjectFromSave(key: string): Record<string, number> | null {
        try {
            const saveData = localStorage.getItem(`${SkillTreeContext.SAVE_KEY_PREFIX}_${key}`);
            return saveData ? JSON.parse(saveData) : null;
        } catch (e) {
            console.error(`Error loading ${key} from save:`, e);
            return null;
        }
    }

    /**
     * 从存档中获取数字
     */
    private getNumberFromSave(key: string): number | null {
        try {
            const saveData = localStorage.getItem(`${SkillTreeContext.SAVE_KEY_PREFIX}_${key}`);
            return saveData ? Number(saveData) : null;
        } catch (e) {
            console.error(`Error loading ${key} from save:`, e);
            return null;
        }
    }

    /**
     * 保存数据
     */
    saveData() {
        // 将Map转换为普通对象以便存储
        const skillsObject: Record<string, number> = {};
        this._unlockedSkills.forEach((level, id) => {
            skillsObject[id] = level;
        });

        // 保存技能数据
        this.setObjectToSave('unlockedSkills', skillsObject);
        this.setNumberToSave('totalSkillPointsSpent', this._totalSkillPointsSpent);
        this.setNumberToSave('availableSkillPoints', this._availableSkillPoints);

        // 触发保存
        SaveContext.saveGame();
    }

    /**
     * 保存对象到存档
     */
    private setObjectToSave(key: string, value: any): void {
        try {
            localStorage.setItem(`${SkillTreeContext.SAVE_KEY_PREFIX}_${key}`, JSON.stringify(value));
        } catch (e) {
            console.error(`Error saving ${key} to save:`, e);
        }
    }

    /**
     * 保存数字到存档
     */
    private setNumberToSave(key: string, value: number): void {
        try {
            localStorage.setItem(`${SkillTreeContext.SAVE_KEY_PREFIX}_${key}`, value.toString());
        } catch (e) {
            console.error(`Error saving ${key} to save:`, e);
        }
    }

    /**
     * 检查技能是否可解锁
     * @param skillId 技能ID
     */
    canUnlockSkill(skillId: string): boolean {
        const skill = this._allSkills.get(skillId);
        if (!skill) return false;

        // 检查前置技能
        for (const prereqId of skill.prerequisiteSkills) {
            const prereqLevel = this._unlockedSkills.get(prereqId) || 0;
            if (prereqLevel <= 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查技能是否可升级
     * @param skillId 技能ID
     */
    canUpgradeSkill(skillId: string): boolean {
        // 检查技能是否存在
        const skill = this._allSkills.get(skillId);
        if (!skill) return false;

        // 检查当前等级是否已达最大
        const currentLevel = this._unlockedSkills.get(skillId) || 0;
        if (currentLevel >= skill.maxLevel) return false;

        // 检查可用技能点
        if (this._availableSkillPoints <= 0) return false;

        // 如果是0级(未解锁)，检查前置条件
        if (currentLevel === 0 && !this.canUnlockSkill(skillId)) {
            return false;
        }

        return true;
    }

    /**
     * 升级技能
     * @param skillId 技能ID
     * @returns 升级是否成功
     */
    upgradeSkill(skillId: string): boolean {
        if (!this.canUpgradeSkill(skillId)) return false;

        // 消耗技能点
        this._availableSkillPoints--;
        this._totalSkillPointsSpent++;

        // 增加技能等级
        const currentLevel = this._unlockedSkills.get(skillId) || 0;
        this._unlockedSkills.set(skillId, currentLevel + 1);

        // 保存
        this.saveData();

        return true;
    }

    /**
     * 获取技能等级
     * @param skillId 技能ID
     */
    getSkillLevel(skillId: string): number {
        return this._unlockedSkills.get(skillId) || 0;
    }

    /**
     * 获取技能效果值
     * @param skillId 技能ID
     */
    getSkillEffect(skillId: string): number {
        const skill = this._allSkills.get(skillId);
        if (!skill) return 0;

        const level = this._unlockedSkills.get(skillId) || 0;
        return skill.baseEffect * level;
    }

    /**
     * 获取技能升级成本
     * @param skillId 技能ID
     */
    getSkillUpgradeCost(skillId: string): number {
        const skill = this._allSkills.get(skillId);
        if (!skill) return 0;

        const currentLevel = this._unlockedSkills.get(skillId) || 0;
        const multiplier = Math.pow(skill.costMultiplier, currentLevel);

        return skill.baseCost * multiplier;
    }

    /**
     * 获取可用技能点
     */
    getAvailableSkillPoints(): number {
        return this._availableSkillPoints;
    }

    /**
     * 增加可用技能点
     * @param amount 增加数量
     */
    addSkillPoints(amount: number) {
        if (amount <= 0) return;

        this._availableSkillPoints += amount;
        this.saveData();
    }

    /**
     * 获取所有技能信息
     */
    getAllSkills(): Map<string, SkillInfo> {
        return this._allSkills;
    }

    /**
     * 获取按类型的技能加成总和
     * @param type 技能类型
     */
    getTotalBonusByType(type: SkillType): number {
        let totalBonus = 0;

        // 遍历所有技能
        this._allSkills.forEach((skill, id) => {
            if (skill.type === type) {
                const level = this._unlockedSkills.get(id) || 0;
                totalBonus += skill.baseEffect * level;
            }
        });

        return totalBonus;
    }

    /**
     * 获取硬币产出加成
     */
    getCoinBonus(): number {
        return 1 + this.getTotalBonusByType(SkillType.CoinBonus);
    }

    /**
     * 获取员工产出加成
     */
    getHelperBonus(): number {
        return 1 + this.getTotalBonusByType(SkillType.HelperBonus);
    }

    /**
     * 获取骰子产出加成
     */
    getDiceBonus(): number {
        return 1 + this.getTotalBonusByType(SkillType.DiceBonus);
    }

    /**
     * 获取暴击几率
     */
    getCriticalChance(): number {
        return this.getTotalBonusByType(SkillType.CriticalChance);
    }

    /**
     * 获取暴击倍率
     */
    getCriticalMultiplier(): number {
        return 2 + this.getTotalBonusByType(SkillType.CriticalMultiplier);
    }
}