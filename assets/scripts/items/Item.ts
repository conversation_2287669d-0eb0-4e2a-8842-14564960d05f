import { _decorator, Component, Node, Vec2, Vec3, tween, Tween } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 基础物品类
 * 所有游戏中的物品都继承自此类
 */
@ccclass('Item')
export class Item extends Component {
    @property
    can_be_pushed: boolean = true;

    private push_force: Vec2 = new Vec2(0, 0);
    private push_timer: number = null;
    private screen_limit: Vec2 = new Vec2(260, 170);
    private screen_limit_offset: Vec2 = new Vec2(0, -20);
    protected should_push: boolean = true;
    
    public id: string;
    
    static collision_distance_diameter_squared: number = Math.pow(32, 2) * 2;
    static item_ids: number = 0;
    static available_items: Record<string, Item> = {};

    start() {
        this.id = Item.item_ids.toString();
        Item.item_ids += 1;
        
        // 设置定时器替代Godot的Timer
        this.push_timer = setInterval(() => {
            this.pushNeighboursAway();
        }, 100); // 0.1秒
        
        this.pushNeighboursAway();
        Item.collision_distance_diameter_squared = Math.pow(32, 2) * 2;
    }

    onDestroy() {
        if (this.push_timer !== null) {
            clearInterval(this.push_timer);
            this.push_timer = null;
        }
    }

    update(deltaTime: number) {
        if (this.can_be_pushed) {
            const pos = this.node.getPosition();
            pos.x += this.push_force.x * deltaTime;
            pos.y += this.push_force.y * deltaTime;
            this.node.setPosition(pos);
        }
        
        // 限制在屏幕范围内
        const pos = this.node.getPosition();
        const minX = -this.screen_limit.x + this.screen_limit_offset.x;
        const maxX = this.screen_limit.x + this.screen_limit_offset.x;
        const minY = -this.screen_limit.y + this.screen_limit_offset.y;
        const maxY = this.screen_limit.y + this.screen_limit_offset.y;
        
        pos.x = Math.max(minX, Math.min(pos.x, maxX));
        pos.y = Math.max(minY, Math.min(pos.y, maxY));
        this.node.setPosition(pos);
    }

    pushNeighboursAway() {
        this.push_force = new Vec2(0, 0);
        if (!Item.collision_distance_diameter_squared) return;
        if (!this.should_push) return;
        
        let max_pushes = 0;
        for (const id in Item.available_items) {
            if (max_pushes > 12) break;
            
            const neighbour = Item.available_items[id];
            const neighbourPos = neighbour.node.getWorldPosition();
            const myPos = this.node.getWorldPosition();
            
            const distanceSquared = Vec3.squaredDistance(neighbourPos, myPos);
            if (distanceSquared > Item.collision_distance_diameter_squared) continue;
            
            const direction = new Vec2(
                myPos.x - neighbourPos.x,
                myPos.y - neighbourPos.y
            );
            
            const distance = Math.sqrt(distanceSquared);
            const min_dist = 15.0;
            
            if (distance === 0) {
                const random_offset = new Vec2(
                    (Math.random() - 0.5) * 0.1,
                    (Math.random() - 0.5) * 0.1
                ).normalize();
                
                this.push_force.add(random_offset);
                if (neighbour.push_force) {
                    neighbour.push_force.subtract(random_offset);
                }
            } else if (distance < min_dist) {
                const push_strength_away = 15.0;
                const overlap = min_dist - distance;
                const push_vector = direction.normalize().multiplyScalar(overlap * push_strength_away);
                
                this.push_force.add(push_vector);
                if (neighbour.push_force) {
                    neighbour.push_force.subtract(push_vector);
                }
            }
            
            max_pushes += 1;
        }
        
        const MAX_PUSH_FORCE = 20.0;
        this.push_force.x = Math.max(-MAX_PUSH_FORCE, Math.min(this.push_force.x, MAX_PUSH_FORCE));
        this.push_force.y = Math.max(-MAX_PUSH_FORCE, Math.min(this.push_force.y, MAX_PUSH_FORCE));
    }
    
    // 用于在子类中重写
    resetPhysicsInterpolation() {
        // 在Cocos Creator中不需要此功能，但保留方法以保持兼容性
    }
} 