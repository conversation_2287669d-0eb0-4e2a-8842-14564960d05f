import { _decorator, Component, Node, Sprite, Animation, Vec2, Vec3, tween, Tween, input, Input, sys, UITransform, Enum } from 'cc';
import { Interactable } from './Interactable';
import { StatsContext } from '../../contexts/StatsContext';
import { AudioManager } from '../../managers/AudioManager';
import { ConfigContext, PURCHASABLE_ITEM_ID, PurchasableItemConfig } from '../../contexts/ConfigContext';
import { GameEvent } from '../../event/GameEvent';
import { FloatingText } from '../../ui/floating_text/FloatingText';
import { SaveContext } from '../../contexts/SaveContext';
import { GuideManager } from '../../managers/GuideManager';

const { ccclass, property } = _decorator;

export enum SOURCE {
    EMPLOYEE, // 员工     
    REFLIP, // 重翻
    SHOCKWAVE, // 冲击波
    MANUAL // 手动
}

export enum COIN_TYPE {
    SMALL_COIN, // 小硬币
    MEDIUM_COIN, // 中硬币
    LARGE_COIN // 大硬币
}

Enum(SOURCE);
Enum(COIN_TYPE);

@ccclass('Coin')
export class Coin extends Interactable {
    // 硬币正面
    @property(Sprite)
    sprite_head: Sprite = null;
    
    // 硬币反面
    @property(Sprite)
    sprite_tails: Sprite = null;
    
    // 翻转动画
    @property(Animation)
    flip_animation: Animation = null;
    
    // 硬币容器
    @property(Node)
    coin_sprite_container: Node = null;
    
    // 阴影    
    @property(Sprite)
    shadow_sprite: Sprite = null;

    // 新手指引
    @property(Node)
    node_finger_guide: Node = null;
    
    // 最小翻转时间
    @property
    flip_duration_min: number = 1.8;
    
    // 最大翻转时间
    @property
    flip_duration_max: number = 1.4;
    
    // 最小跳跃距离
    @property
    min_jump_distance: number = 100.0;
    
    // 最大跳跃距离
    @property
    max_jump_distance: number = 160.0;
    
    // 最小移动力
    @property
    min_move_force: Vec2 = new Vec2(2.5, 2.5);
    
    // 最大移动力
    @property
    max_move_force: Vec2 = new Vec2(4.0, 4.0);
    
    // 硬币类型
    @property({ type: COIN_TYPE })
    type: COIN_TYPE = COIN_TYPE.SMALL_COIN;
    
    // 是否正在下降
    private is_falling: boolean = false;
    // 跳跃高度
    private _jump_height: number = 0;
    // 是否开始翻转
    public has_flip_started: boolean = false;
    // 是否在空中
    private is_in_air: boolean = false;
    // 硬币旋转
    private coin_rotation: number = 0.0;
    // 当前向上的力
    private current_upwards_force: number = 0.0;
    // 当前向上的力
    private move_force: Vec2 = new Vec2(0, 0);
    // 阴影默认缩放
    private shadow_default_scale: Vec2 = null;
    // 是否在背面
    private is_tails: boolean = false;
    // 是否正在生成
    private is_spawning_in: boolean = false;
    // 基础速度
    private readonly BASE_SPEED: number = 50.0;
    // 来源
    private source: SOURCE;
    
    // 反弹组
    private group_to_bounce: string = "";
    // 可用硬币 - 修改为Map数组，每种类型存储多个硬币
    private static available_coins: Record<string, Coin[]> = {
        'small': [],
        'medium': [],
        'large': []
    };
    // 反弹组字典
    private group_to_bounce_dict: Record<COIN_TYPE, string> = {
        [COIN_TYPE.SMALL_COIN]: "",
        [COIN_TYPE.MEDIUM_COIN]: "small_coins",
        [COIN_TYPE.LARGE_COIN]: "medium_and_small_coins"
    };
    
    // 上次悬停检测时间，用于限制悬停触发频率
    private lastHoverCheckTime: number = 0;

    //技能生效中
    private skillStatus: boolean = false;
    private _config: PurchasableItemConfig = null;
    private skillKey:string = "";
    
    private stageW: number = 750;
    private stageH: number = 1334;
    
    start() {
        super.start();
        
        // 舞台尺寸
        const stageWidth = this.stageW;
        const stageHeight = this.stageH;
        
        // 随机位置偏移，确保在舞台范围内
        const pos = this.node.getPosition();
        
        // 舞台中心坐标基础上的随机偏移
        const maxOffsetX = stageWidth / 2 - 30; // 留出一定边距
        const maxOffsetY = stageHeight / 2 - 30;
        
        // 在有效范围内随机位置
        pos.x = (Math.random() * 2 - 1) * maxOffsetX;
        pos.y = (Math.random() * 2 - 1) * maxOffsetY;

        //新手
        if(StatsContext.newFingerGuide == 0){
            pos.x = 0;
            pos.y = 0;
        }
        
        this.node.setPosition(pos);
        
        this.spawn_in_animation();
        
        this.group_to_bounce = this.group_to_bounce_dict[this.type] || "";
        this.shadow_default_scale = new Vec2(this.shadow_sprite.node.scale.x, this.shadow_sprite.node.scale.y);
        this._register_idle_coin();
        
        // 监听鼠标事件
        this.mouse_clicked.addEventListener('mouse_clicked', this.on_mouse_clicked.bind(this));
        // this.mouse_hovered.addEventListener('mouse_hovered', this._on_mouse_hovered.bind(this));
        
        // 打印当前重翻概率
        console.log(`[Coin] ${this.idKey} 硬币重翻概率: ${StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.REFLIP_CHANCE)}`);

        //新手判断
        if(StatsContext.newFingerGuide == 0){
            setTimeout(() => {
                if(this.node_finger_guide) this.node_finger_guide.active = true;    
            }, 1000);
        }

        StatsContext.events.on(GameEvent.SKILL_LEVEL_CHANGED, this.onSkillLevelChanged, this);
        StatsContext.events.on(GameEvent.USE_SKILL, this.onUseSkill, this);
        StatsContext.events.on(GameEvent.USE_SKILL_FUNISH, this.onUseSkillFinish, this);
    }
    
    onDestroy() {
        super.onDestroy();
        this._unregister_idle_coin();

        this.mouse_clicked.removeEventListener('mouse_clicked', this.on_mouse_clicked.bind(this));
        // this.mouse_hovered.removeEventListener('mouse_hovered', this._on_mouse_hovered.bind(this));
        StatsContext.events.off(GameEvent.SKILL_LEVEL_CHANGED, this.onSkillLevelChanged, this);
        StatsContext.events.off(GameEvent.USE_SKILL, this.onUseSkill, this);
        StatsContext.events.off(GameEvent.USE_SKILL_FUNISH, this.onUseSkillFinish, this);
    }
    
    spawn_in_animation() {
        this.coin_sprite_container.setPosition(0, -400, 0);
        this.is_spawning_in = true;
        this.reset_physics_interpolation();
        
        // 创建下落动画
        const fallTween = tween(this.coin_sprite_container)
            .to(0.3, { position: new Vec3(this.coin_sprite_container.position.x, 0, 0) }, { easing: 'quadOut' })
            .call(() => {
                AudioManager.playSoundEffect("flip_end_sound");
                AudioManager.playSoundEffect("jump_end_sound");
            })
            .to(0.1, { position: new Vec3(this.coin_sprite_container.position.x, -5, 0) }, { easing: 'quadOut' })
            .to(0.2, { position: new Vec3(this.coin_sprite_container.position.x, 0, 0) }, { easing: 'elasticOut' })
            .call(() => {
                this.is_spawning_in = false;
            })
            .start();
    }
    
    on_mouse_clicked() {
        // 将 this.node 移动到父节点子节点列表的末尾
        if(this.node.parent){
            this.node.setSiblingIndex(this.node.parent.children.length - 1);
        }
        if (!this.has_flip_started) {
            this.start_flip_action(SOURCE.MANUAL);
        }

        //新手
        if(StatsContext.newFingerGuide == 0){
            // StatsContext.newFingerGuide = 1;
            GuideManager.instance.nextGuide();
            if(this.node_finger_guide) this.node_finger_guide.active = false;
            SaveContext.saveGame();
        }
    }
    
    _on_mouse_hovered() {
        // 添加时间检查，限制触发频率
        const now = Date.now();
        if (now - this.lastHoverCheckTime < 500) { // 500ms内不重复触发
            return;
        }
        this.lastHoverCheckTime = now;
        
        if (this.type === COIN_TYPE.SMALL_COIN && 
            StatsContext.isFeatureUnlocked(PURCHASABLE_ITEM_ID.FLIP_SMALL_COIN_ON_HOVER) && 
            !this.has_flip_started && 
            !this.is_spawning_in) {
            console.log("[Coin] 鼠标悬停触发翻转");
            this.start_flip_action(SOURCE.MANUAL);
        }
    }
    
    start_flip_action(source: number = 0): void {
        if (this.has_flip_started) return;
        
        this._unregister_idle_coin();
        this.source = source as SOURCE;
        this.should_push = true;
        
        if (!this.check_sound_cap()) {
            AudioManager.playSoundEffect("jump_start_sound");
        }
        AudioManager.playSoundEffect("flip_start_sound");
        
        if (this.sprite_head) this.sprite_head.node.active = false;
        if (this.sprite_tails) this.sprite_tails.node.active = false;
        this.flip_animation.node.active = true;
        
        this.has_flip_started = true;
        if(StatsContext.newFingerGuide == 0){
            this.is_tails = true;
        }else{
            this.is_tails = Math.random() > 0.5;
        }
        
        // 根据硬币类型加载不同的动画
        const animName = this.getCoinAnimationName();
        
        // 播放翻转动画
        const anim = this.flip_animation.getComponent(Animation);
        if (anim) {
            anim.play(animName);
        } else {
            // 如果没有Animation组件，立即结束翻转
            this.on_coin_flip_end();
        }
        
        // 获取硬币翻转速度倍率
        let flipSpeedMultiplier = 1.0; // 默认倍率
        switch (this.type) {
            case COIN_TYPE.SMALL_COIN:
                flipSpeedMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.COIN_FLIP_SPEED_SMALL);
                break;
            case COIN_TYPE.MEDIUM_COIN:
                flipSpeedMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.COIN_FLIP_SPEED_MEDIUM);
                break;
            case COIN_TYPE.LARGE_COIN:
                flipSpeedMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.COIN_FLIP_SPEED_LARGE);
                break;
            default:
                console.warn(`[Coin] 未知的硬币类型 ${this.type} 用于获取翻转速度倍率，将使用默认值 1.0`);
                break;
        }
        // 确保倍率至少为1，避免除以0或过小的值导致动画时间过长或NaN
        if (isNaN(flipSpeedMultiplier) || flipSpeedMultiplier <= 0) {
            console.warn(`[Coin] 获取到的翻转速度倍率异常 (${flipSpeedMultiplier})，已重置为 1.0`);
            flipSpeedMultiplier = 1.0;
        }

        // 计算随机翻转时间
        // 基础翻转时间随机范围
        const baseFlipDuration = Math.random() * (this.flip_duration_max - this.flip_duration_min) + this.flip_duration_min;
        // 应用速度倍率，并额外增加20%的基础速度调整 (1.2)
        // 注意：flipSpeedMultiplier 越大，实际duration越小，速度越快
        const flip_duration = baseFlipDuration / (flipSpeedMultiplier * 1.2); 
        
        // 设置定时器
        setTimeout(() => {
            this.on_coin_flip_end();
        }, (flip_duration - flip_duration / 2.13) * 1000);
        
        // 设置移动方向和力度
        let moveAngle = 0;
        let moveForce = new Vec2(0, 0);
        
        // 舞台尺寸
        const stageWidth = this.stageW;
        const stageHeight = this.stageH;
        const stageCenter = new Vec3(0, 0, 0);
        
        // 获取当前位置
        const currentPos = this.node.getPosition();
        
        // 根据来源确定移动方向
        if (source === SOURCE.MANUAL) {
            // 手动点击：向远离中心的方向移动
            const dirToCenter = new Vec3(
                stageCenter.x - currentPos.x,
                stageCenter.y - currentPos.y,
                0
            ).normalize();
            
            // 反向移动
            moveAngle = Math.atan2(-dirToCenter.y, -dirToCenter.x);
        } else if (source === SOURCE.SHOCKWAVE && Math.random() > 0.33) {
            // 震荡波：随机方向
            moveAngle = Math.random() * Math.PI * 2;
        } else {
            // 其他情况：朝向舞台中心
            // const dirToCenter = new Vec3(
            //     stageCenter.x - currentPos.x,
            //     stageCenter.y - currentPos.y,
            //     0
            // ).normalize();
            
            // moveAngle = Math.atan2(dirToCenter.y, dirToCenter.x);
            moveAngle = Math.random() * Math.PI * 2;
        }
        
        // 根据来源调整力度
        let forceMultiplier = 1.5; // 基础倍率提高
        if (source === SOURCE.MANUAL) {
            forceMultiplier = 2.0; // 手动点击时力度更大
        } else if (source === SOURCE.SHOCKWAVE) {
            forceMultiplier = 1.2; // 被触发时力度适中
        } else if (source === SOURCE.REFLIP) {
            forceMultiplier = 1.8; // 重新翻转时力度较大
        }
        
        // 根据硬币类型额外调整力度
        switch(this.type) {
            case COIN_TYPE.SMALL_COIN:
                forceMultiplier *= 1.2; // 小硬币移动更快
                break;
            case COIN_TYPE.MEDIUM_COIN:
                forceMultiplier *= 1.0; // 中硬币标准力度
                break;
            case COIN_TYPE.LARGE_COIN:
                forceMultiplier *= 0.8; // 大硬币移动稍慢
                break;
        }
        
        const moveForceMin = this.min_move_force;
        const moveForceMax = this.max_move_force;
        
        // 生成随机力度
        const forceX = (Math.random() * (moveForceMax.x - moveForceMin.x) + moveForceMin.x) * forceMultiplier;
        const forceY = (Math.random() * (moveForceMax.y - moveForceMin.y) + moveForceMin.y) * forceMultiplier;
        
        // 应用到移动力上
        this.move_force.x = Math.cos(moveAngle) * forceX;
        this.move_force.y = Math.sin(moveAngle) * forceY;
        
        // 预测最终位置并限制在舞台范围内
        const predictedX = currentPos.x + this.move_force.x * this.BASE_SPEED * (flip_duration / 1.5);
        const predictedY = currentPos.y + this.move_force.y * this.BASE_SPEED * (flip_duration / 1.5);
        
        // 边界约束
        const maxOffsetX = stageWidth / 2 - 30;
        const maxOffsetY = stageHeight / 2 - 30;
        
        // 如果预测位置超出边界，调整移动方向朝向舞台中心
        if (Math.abs(predictedX) > maxOffsetX || Math.abs(predictedY) > maxOffsetY) {
            console.log("硬币预测位置超出边界，调整为朝向舞台中心");
            
            // 计算到舞台中心的方向向量
            const dirToCenter = new Vec3(
                stageCenter.x - currentPos.x,
                stageCenter.y - currentPos.y,
                0
            ).normalize();
            
            // 计算当前移动方向与中心方向的点积
            // 点积为负表示硬币正在远离中心
            const dotProduct = this.move_force.x * dirToCenter.x + this.move_force.y * dirToCenter.y;
            
            if (dotProduct < 0) {
                // 如果硬币正在远离中心，完全反转方向
                moveAngle = Math.atan2(dirToCenter.y, dirToCenter.x);
                
                // 保持原有力度但方向朝向中心
                const forceMagnitude = Math.sqrt(this.move_force.x * this.move_force.x + this.move_force.y * this.move_force.y);
                this.move_force.x = Math.cos(moveAngle) * forceMagnitude;
                this.move_force.y = Math.sin(moveAngle) * forceMagnitude;
            } else {
                // 硬币已经朝向中心，但力度可能过大，减小力度
                const distanceToCenter = Math.sqrt(
                    (stageCenter.x - currentPos.x) * (stageCenter.x - currentPos.x) +
                    (stageCenter.y - currentPos.y) * (stageCenter.y - currentPos.y)
                );
                
                // 根据到中心的距离动态调整力度
                const maxDistance = Math.max(maxOffsetX, maxOffsetY);
                const distanceRatio = Math.min(distanceToCenter / maxDistance, 1);
                
                // 距离中心越远，力度越小
                this.move_force.x *= distanceRatio * 0.8;
                this.move_force.y *= distanceRatio * 0.8;
            }
            
            // 重新预测位置，确保在舞台内
            const newPredictedX = currentPos.x + this.move_force.x * this.BASE_SPEED * (flip_duration / 1.5);
            const newPredictedY = currentPos.y + this.move_force.y * this.BASE_SPEED * (flip_duration / 1.5);
            
            // console.log(`调整后预测位置: (${newPredictedX.toFixed(2)}, ${newPredictedY.toFixed(2)})`);
        }
        
        // 创建移动力量的动画 - 使用更快的减速
        tween(this.move_force)
            .to(flip_duration / 2.0, { // 更快减速
                x: 0, 
                y: 0 
            }, { easing: 'quadOut' }) // 使用quadOut缓动，更符合物理感
            .start();
        
        // 创建跳跃动画 - 调整为更符合物理重力的动画
        const jumpHeight = Math.random() * (this.max_jump_distance - this.min_jump_distance) + this.min_jump_distance;
        
        // 使用自定义属性更新跳跃高度
        const self = this;
        const startHeight = this._jump_height;
        
        // 上升动画 - 更快上升
        tween(this.node)
            .to(flip_duration / 5.0, {}, { // 上升时间更短
                onUpdate: (target, ratio) => {
                    self.jump_height = startHeight + (jumpHeight - startHeight) * ratio;
                },
                easing: 'quadOut' // 上升使用quadOut更符合投掷物理
            })
            .call(() => { self.is_falling = true; })
            // 下降动画 - 更快下降
            .to(flip_duration / 2.5, {}, { // 下降时间略长
                onUpdate: (target, ratio) => {
                    self.jump_height = jumpHeight * (1 - ratio);
                    
                    // 更新阴影缩放，更好地表现高度
                    if (self.shadow_default_scale) {
                        const scale = 1.0 - self.jump_height / 200.0;
                        self.shadow_sprite.node.setScale(
                            self.shadow_default_scale.x * scale,
                            self.shadow_default_scale.y * scale,
                            1
                        );
                    }
                },
                easing: 'quadIn' // 下降使用quadIn，模拟重力加速
            })
            .start();
    }
    
    on_coin_flip_end() {
        this.flip_animation.node.active = false;
        this.has_flip_started = false;
        this.should_push = true;
        this._register_idle_coin();
        
        // 根据翻转结果显示正面或反面
        if (this.sprite_head) this.sprite_head.node.active = !this.is_tails;
        if (this.sprite_tails) this.sprite_tails.node.active = this.is_tails;
        
        if (this.is_tails) {
            // 新的 coinValue 计算逻辑开始
            let additionalBonusValue = 0; // 额外奖励值
            let bonusMultiplier = 1.0;    // 奖励倍率

            // 根据硬币类型 (this.type) 从 StatsContext 获取对应的附加值和倍率修饰符
            // 假设 COIN_TYPE 和 PURCHASABLE_ITEM_ID 已被正确导入和定义
            if (this.type === COIN_TYPE.SMALL_COIN) {
                additionalBonusValue = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_SMALL);
                bonusMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_MULTIPLIER_SMALL);
            } else if (this.type === COIN_TYPE.MEDIUM_COIN) {
                additionalBonusValue = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_MEDIUM);
                bonusMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_MULTIPLIER_MEDIUM);
            } else if (this.type === COIN_TYPE.LARGE_COIN) {
                additionalBonusValue = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_LARGE);
                bonusMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_MULTIPLIER_LARGE);
            }
            // else {
            //     // 可以选择为其他类型的硬币或默认情况记录警告，如果存在其他类型的话
            //     // console.warn(`[Coin] 未处理的硬币类型 ${this.type} 用于计算附加值和倍率`);
            // }

            // 计算最终硬币价值
            // this.value 是硬币的基础价值
            // additionalBonusValue 是通过升级获得的额外固定价值
            // bonusMultiplier 是对这个额外固定价值的倍率提升
            let coinValue = this.value + (additionalBonusValue * bonusMultiplier);
            
            //临时增值技能生效
            if(this.skillStatus && this._config){
                const level = StatsContext.getSkill(this.skillKey);
                coinValue = Math.floor(coinValue*(1+level/20));
            }
            // 新的 coinValue 计算逻辑结束

            // 使用FloatingText显示收益
            const textPos = new Vec3(
                this.node.position.x,
                this.node.position.y + 50,
                this.node.position.z
            );
            // TODO: 调用FloatingText.create显示收益
            FloatingText.create(this.node.parent, textPos, `+${coinValue.toLocaleString()}`);
            
            this.emitValueGained(coinValue, SOURCE[this.source]);
            
        } else {
            if (!this.check_sound_cap()) {
                AudioManager.playSoundEffect("flip_fail_sound");
            }
        }
        
        if (this.type === COIN_TYPE.LARGE_COIN && StatsContext.isFeatureUnlocked(PURCHASABLE_ITEM_ID.LARGE_COINS_TRIGGER_COINS)) {
            // 查找附近的硬币，只有大硬币才触发
            const nearbyCoins = this.getNearbyCoins();
            
            // 打印附近硬币数量
            console.log(`[Coin] 大硬币附近找到 ${nearbyCoins.length} 个硬币`);
            
            // 限制最多触发8个硬币
            const maxCoins = Math.min(8, nearbyCoins.length);
            for (let i = 0; i < maxCoins; i++) {
                const coin = nearbyCoins[i];
                if (coin && coin !== this && !coin.has_flip_started) {
                    coin.start_flip_action(SOURCE.SHOCKWAVE);
                }
            }
        }
        
        // 检查是否要重翻
        const reflipChance = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.REFLIP_CHANCE);
        
        // 打印当前硬币的重翻概率检查
        console.log(`[Coin] ${this.idKey} 硬币重翻检查: 概率=${reflipChance}, 随机值=${Math.random()}`);
        
        if (reflipChance > 0 && Math.random() < reflipChance) {
            console.log(`[Coin] ${this.idKey} 硬币将在500ms后重翻`);
            setTimeout(() => {
                if (!this.has_flip_started) {
                    this.start_flip_action(SOURCE.REFLIP);
                }
            }, 500);
        }

        this.node.setSiblingIndex(0);
        //主线任务
        StatsContext.events.emit(GameEvent.MAIN_TASK_CLICK_COIN, this.type);
    }


    check_sound_cap(limit: number = 10): boolean {
        // TODO: 如果需要限制声音，在这里实现
        return false;
    }

    update(deltaTime: number) {
        super.update(deltaTime);
        
        if (this.has_flip_started) {
            const position = this.node.getPosition();
            position.x += this.move_force.x * this.BASE_SPEED * deltaTime;
            position.y += this.move_force.y * this.BASE_SPEED * deltaTime;
            this.node.setPosition(position);
        }
    }

    set jump_height(value: number) {
        // 添加掉落音效逻辑
        if (value < this._jump_height && value < 50.0 && this.is_falling) {
            if (!this.check_sound_cap()) {
                AudioManager.playSoundEffect("flip_end_sound");
                AudioManager.playSoundEffect("jump_end_sound");
            }
            this.is_falling = false;
        }
        
        this._jump_height = value;
        
        // 更新高度
        const coinPos = new Vec3(0, this._jump_height, 0);
        this.coin_sprite_container.setPosition(coinPos);
        
        // 更新阴影
        const scale = 1.0 - this._jump_height / 200.0;
        const shadowPos = new Vec3(0, -this._jump_height * 0.15, 0);
        
        if (this.shadow_default_scale) {
            this.shadow_sprite.node.setScale(
                this.shadow_default_scale.x * scale, 
                this.shadow_default_scale.y * scale, 
                1
            );
        }
        
        this.shadow_sprite.node.setPosition(shadowPos);
    }
    
    get jump_height(): number {
        return this._jump_height;
    }
    
    reset_physics_interpolation() {
        // 重置物理插值状态
    }
    
    _register_idle_coin() {
        // 将硬币添加到对应类型的数组中
        const type = this.idKey;
        if (!Coin.available_coins[type]) {
            Coin.available_coins[type] = [];
        }
        
        // 确保不重复添加
        if (!Coin.available_coins[type].includes(this)) {
            Coin.available_coins[type].push(this);
        }
    }
    
    _unregister_idle_coin() {
        // 从可用硬币池中移除
        const type = this.idKey;
        if (Coin.available_coins[type]) {
            const index = Coin.available_coins[type].indexOf(this);
            if (index !== -1) {
                Coin.available_coins[type].splice(index, 1);
            }
        }
    }
    
    getNearbyCoins(): Coin[] {
        const coinObjects: Coin[] = [];
        const currentPos = this.node.getPosition();
        const nearbyDistance = 150; // 只查找150单位范围内的硬币
        
        // 根据反弹组确定要搜索的硬币类型
        const typesToSearch: string[] = [];
        
        switch (this.group_to_bounce) {
            case "small_coins":
                typesToSearch.push("small");
                break;
            case "medium_and_small_coins":
                typesToSearch.push("small");
                typesToSearch.push("medium");
                break;
        }
        
        // 如果没有指定类型，返回空数组
        if (typesToSearch.length === 0) {
            return coinObjects;
        }
        
        // 遍历所有指定类型的硬币，检查距离
        for (const type of typesToSearch) {
            if (Coin.available_coins[type] && Coin.available_coins[type].length > 0) {
                for (const coin of Coin.available_coins[type]) {
                    if (coin !== this && !coin.has_flip_started) {
                        const coinPos = coin.node.getPosition();
                        const distanceSquared = 
                            (coinPos.x - currentPos.x) * (coinPos.x - currentPos.x) + 
                            (coinPos.y - currentPos.y) * (coinPos.y - currentPos.y);
                        
                        if (distanceSquared <= nearbyDistance * nearbyDistance) {
                            coinObjects.push(coin);
                        }
                    }
                }
            }
        }
        
        return coinObjects;
    }

    private getCoinAnimationName(): string {
        // 根据硬币类型返回对应的动画名称
        switch(this.type) {
            case COIN_TYPE.SMALL_COIN:
                return 'small_coin_new';
            case COIN_TYPE.MEDIUM_COIN:
                return 'medium_coin_new';
            case COIN_TYPE.LARGE_COIN:
                return 'large_coin';
            default:
                return 'coin_flip'; // 默认动画
        }
    }

    onSkillLevelChanged(key:string, value:number, oldValue:number){
        if(key === PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION && oldValue==0){
            this.on_mouse_clicked();
        }
    }
    onUseSkill(key:string){
        this.skillKey = key;
        if(this.skillKey === PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION){
            this.on_mouse_clicked();
        }
        if(this.skillKey === PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD){
            this.skillStatus = true;
            this._config = ConfigContext.getPurchasableItemConfig(this.skillKey.toLowerCase() as PURCHASABLE_ITEM_ID);
            if (!this._config) {
                console.warn(`[Purchasable] 配置不存在: ${this.skillKey} (尝试使用 ${this.skillKey.toLowerCase()})`);
            } else {
                console.log(`[Purchasable] 配置初始化成功: ${this.skillKey}`);
            }
        }
    }
    onUseSkillFinish(key:string){
        if(key === PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD){
            this.skillStatus =false;
        }
    }
}