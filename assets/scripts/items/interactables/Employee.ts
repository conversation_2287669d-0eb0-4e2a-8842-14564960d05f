
import { Interactable } from './Interactable';
import { Coin, SOURCE } from './Coin';
import { StatsContext } from '../../contexts/StatsContext';
import { ConfigContext, PURCHASABLE_ITEM_ID, PurchasableItemConfig } from '../../contexts/ConfigContext';
import { _decorator, Animation, Sprite, Label, Vec3 } from 'cc';
import { GameEvent } from '../../event/GameEvent';
import { FloatingText } from '../../ui/floating_text/FloatingText';

const { ccclass, property } = _decorator;

enum STATE {
    IDLING,
    MOVING_TO_TARGET,
    INTERACTING,
    SLEEPING,
    ENTER1,
    ENTER2,
    EXIT1,
    EXIT2,
    EXIT3,
}

/**
 * employee类 - 游戏中的员工，会自动产生收益
 */
@ccclass('Employee')
export class Employee extends Interactable {
    // 静态属性，用于跟踪所有员工当前正在目标的交互对象
    private static targetedInteractables: Map<string, Interactable> = new Map();

    @property(Animation)
    animated_sprite: Animation = null;

    @property
    move_speed: number = 100.0;

    @property
    minimal_idle_time: number = 3.0;

    @property
    maximum_idle_time: number = 5.0;

    @property
    base_sleep_chance: number = 0.01;

    @property
    minimal_sleep_time: number = 10.0;

    @property
    maximum_sleep_time: number = 30.0;

    @property({ type: [Interactable] })
    available_targets: Interactable[] = [];

    private needed_minimal_distance: number = 15.0;
    private current_target: Interactable = null;
    private _state: STATE = STATE.IDLING;
    private state_time_current: number = 0.0;
    private state_time: number = 0.0;
    private vertical_orientation: string = "up";

    // UI相关属性
    @property(Sprite)
    private employeeSprite: Sprite | null = null;

    @property(Label)
    private levelLabel: Label | null = null;

    @property(Label)
    private incomeLabel: Label | null = null;

    @property
    private employeeId: string = '';
    public get employeeID(): string {
        return "employee_"+this.employeeId;
    }

    private _level: number = 0;
    private _baseIncome: number = 0;
    private _incomePerSecond: number = 0;
    private _statsContext: StatsContext | null = null;
    private _ConfigContext: ConfigContext | null = null;
    private _lastIncomeTime: number = 0;
    private _incomeInterval: number = 1.0; // 秒

    // 添加新的变量来跟踪上一帧的方向
    private last_vertical_orientation: string = "";
    private current_animation: string = "";

    //技能生效中
    private skillEmergencyMobilization: boolean = false;
    private skillHighEfficientWork: boolean = false;
    private _config: PurchasableItemConfig = null;
    private skillKey: string = "";

    private movePos: Vec3 = null;

    private borthPOS = new Vec3(426, -382, 0);
    private enterPos1 = new Vec3(0, -382, 0);
    private enterPos2 = new Vec3(0, -252, 0);
    private exitPos = new Vec3(-426, -382, 0);

    onLoad() {
        super.onLoad();

        // 获取上下文
        this._statsContext = StatsContext.instance;

        // ConfigContext不是单例，直接使用静态方法


        // 初始化员工等级和收入
        this._level = StatsContext.getSkill(this.skillKey);//this._statsContext?.getHelperLevel(this.helperId) || 0;
        this.updateIncomeRate();

        StatsContext.events.on(GameEvent.USE_SKILL, this.onUseSkill, this);
        StatsContext.events.on(GameEvent.USE_SKILL_FUNISH, this.onUseSkillFinish, this);
        StatsContext.events.on(GameEvent.EMPLOYEE_EXPIRED, this.onEmployeeExpired, this);
    }

    start() {
        super.start();

        if (!this.animated_sprite) this.animated_sprite = this.getComponent(Animation);

        // 监听动画完成事件
        if (this.animated_sprite) {
            this.animated_sprite.on(Animation.EventType.FINISHED, this.onAnimationFinished, this);
        }

        // 更新UI显示
        this.updateUI();

        //TODO 测试代码
        // setTimeout(() => {
        //     this.state = STATE.EXIT1;
        // }, 8000);
    }

    onDestroy() {
        super.onDestroy();

        StatsContext.events.off(GameEvent.USE_SKILL, this.onUseSkill, this);
        StatsContext.events.off(GameEvent.USE_SKILL_FUNISH, this.onUseSkillFinish, this);
        StatsContext.events.off(GameEvent.EMPLOYEE_EXPIRED, this.onEmployeeExpired, this);

        // 移除事件监听
        if (this.animated_sprite) {
            this.animated_sprite.off(Animation.EventType.FINISHED, this.onAnimationFinished, this);
        }

        // 从目标列表中释放当前目标
        this.releaseCurrentTarget();
    }

    update(deltaTime: number) {
        // super.update(deltaTime);

        // 处理自动收益逻辑
        if (this._level > 0) {
            // 累计收益时间
            this._lastIncomeTime += deltaTime;

            // 达到收益间隔时产生收益
            if (this._lastIncomeTime >= this._incomeInterval) {
                this.generateIncome();
                this._lastIncomeTime = 0;
            }
        }

        // 处理状态机逻辑
        switch (this.state) {
            case STATE.ENTER1:
                this.enter_state1(deltaTime);
                break;
            case STATE.ENTER2:
                this.enter_state2(deltaTime);
                break;
            case STATE.IDLING:
                this.idle_state(deltaTime);
                break;
            case STATE.MOVING_TO_TARGET:
                this.move_to_target_state(deltaTime);
                break;
            case STATE.SLEEPING:
                this.sleep_state(deltaTime);
                break;
            case STATE.INTERACTING:
                // 设置新位置
                if (this.node) this.node.setPosition(this.movePos);
                break;
            case STATE.EXIT1:
                this.exit_state1(deltaTime);
                break;
            case STATE.EXIT2:
                this.exit_state2(deltaTime);
                break;
            case STATE.EXIT3:
                this.exit_state3(deltaTime);
                break;
            // INTERACTING状态由动画完成事件处理，不需要在update中处理
        }
    }
    onEnter(needEnter: boolean) {
        // 设置初始状态
        if (needEnter) {
            this.node.setPosition(this.borthPOS);
            this.state = STATE.ENTER1;
        }else{
            this.state = STATE.IDLING;
        }
    }
    enter_state1(deltaTime: number) {
        // 移动向目标
        const myPos = this.node.getPosition();
        const targetPos = this.enterPos1;

        // console.log(`当前位置: (${myPos.x.toFixed(2)}, ${myPos.y.toFixed(2)}), 目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);

        // 计算新位置
        const direction = new Vec3(
            targetPos.x - myPos.x,
            targetPos.y - myPos.y,
            0
        ).normalize();

        // 使用静态属性获取效率乘数
        let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
        if (this.skillHighEfficientWork) {
            const level = StatsContext.getSkill(this.skillKey);
            helperEfficiency *= (level + 1);
        }
        const newPos = new Vec3(
            myPos.x + direction.x * deltaTime * this.move_speed * helperEfficiency,
            myPos.y + direction.y * deltaTime * this.move_speed * helperEfficiency,
            myPos.z
        );

        // console.log(`新位置: (${newPos.x.toFixed(2)}, ${newPos.y.toFixed(2)}), 移动速度: ${this.move_speed}, 效率: ${helperEfficiency}`);
        if (newPos.x != 0 && newPos.y != 0) {
            this.movePos = newPos;
        }
        // 设置新位置
        if (this.movePos) {
            this.node.setPosition(this.movePos);
        }

        // 更新方向
        let new_orientation = this.vertical_orientation;
        if (direction.y >= 0.5) {
            new_orientation = "up";
        } else if (direction.y < -0.5) {
            new_orientation = "down";
        }
        if (direction.x >= 0) {
            this.node.setScale(new Vec3(1, 1, 0));
        } else if (direction.x < 0) {
            this.node.setScale(new Vec3(-1, 1, 0));
        }

        // 只有在方向改变时才更新动画
        if (new_orientation !== this.vertical_orientation || this.current_animation !== this.employeeId + `_walk_side_${new_orientation}`) {
            this.vertical_orientation = new_orientation;
            this.current_animation = this.employeeId + `_walk_side_${this.vertical_orientation}`;

            if (this.animated_sprite) {
                console.log(`播放新动画: ${this.current_animation}`);
                this.animated_sprite.play(this.current_animation);
            }
        }

        // 检查是否接近目标
        const dist = Vec3.distance(newPos, targetPos);
        // console.log(`距离目标: ${dist.toFixed(2)}, 需要最小距离: ${this.needed_minimal_distance}`);

        if (dist <= 5) {
            // 到达目标，切换到交互状态
            console.log("到达目标，切换到 IDLING 状态");

            this.state = STATE.ENTER2;
        }
    }
    enter_state2(deltaTime: number) {
        // 移动向目标
        const myPos = this.node.getPosition();
        const targetPos = this.enterPos2;

        // console.log(`当前位置: (${myPos.x.toFixed(2)}, ${myPos.y.toFixed(2)}), 目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);

        // 计算新位置
        const direction = new Vec3(
            targetPos.x - myPos.x,
            targetPos.y - myPos.y,
            0
        ).normalize();

        // 使用静态属性获取效率乘数
        let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
        if (this.skillHighEfficientWork) {
            const level = StatsContext.getSkill(this.skillKey);
            helperEfficiency *= (level + 1);
        }
        const newPos = new Vec3(
            myPos.x + direction.x * deltaTime * this.move_speed * helperEfficiency,
            myPos.y + direction.y * deltaTime * this.move_speed * helperEfficiency,
            myPos.z
        );

        // console.log(`新位置: (${newPos.x.toFixed(2)}, ${newPos.y.toFixed(2)}), 移动速度: ${this.move_speed}, 效率: ${helperEfficiency}`);
        if (newPos.x != 0 && newPos.y != 0) {
            this.movePos = newPos;
        }
        // 设置新位置
        if (this.movePos) {
            this.node.setPosition(this.movePos);
        }

        // 更新方向
        let new_orientation = this.vertical_orientation;
        if (direction.y >= 0.5) {
            new_orientation = "up";
        } else if (direction.y < -0.5) {
            new_orientation = "down";
        }
        if (direction.x >= 0) {
            this.node.setScale(new Vec3(1, 1, 0));
        } else if (direction.x < 0) {
            this.node.setScale(new Vec3(-1, 1, 0));
        }

        // 只有在方向改变时才更新动画
        if (new_orientation !== this.vertical_orientation || this.current_animation !== this.employeeId + `_walk_side_${new_orientation}`) {
            this.vertical_orientation = new_orientation;
            this.current_animation = this.employeeId + `_walk_side_${this.vertical_orientation}`;

            if (this.animated_sprite) {
                console.log(`播放新动画: ${this.current_animation}`);
                this.animated_sprite.play(this.current_animation);
            }
        }

        // 检查是否接近目标
        const dist = Vec3.distance(newPos, targetPos);
        // console.log(`距离目标: ${dist.toFixed(2)}, 需要最小距离: ${this.needed_minimal_distance}`);

        if (dist <= 5) {
            // 到达目标，切换到交互状态
            console.log("到达目标，切换到 IDLING 状态");

            this.state = STATE.IDLING;
        }
    }
    exit_state1(deltaTime: number) {
        // 移动向目标
        const myPos = this.node.getPosition();
        const targetPos = this.enterPos2;

        // console.log(`当前位置: (${myPos.x.toFixed(2)}, ${myPos.y.toFixed(2)}), 目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);

        // 计算新位置
        const direction = new Vec3(
            targetPos.x - myPos.x,
            targetPos.y - myPos.y,
            0
        ).normalize();

        // 使用静态属性获取效率乘数
        let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
        if (this.skillHighEfficientWork) {
            const level = StatsContext.getSkill(this.skillKey);
            helperEfficiency *= (level + 1);
        }
        const newPos = new Vec3(
            myPos.x + direction.x * deltaTime * this.move_speed * helperEfficiency,
            myPos.y + direction.y * deltaTime * this.move_speed * helperEfficiency,
            myPos.z
        );

        // console.log(`新位置: (${newPos.x.toFixed(2)}, ${newPos.y.toFixed(2)}), 移动速度: ${this.move_speed}, 效率: ${helperEfficiency}`);
        if (newPos.x != 0 && newPos.y != 0) {
            this.movePos = newPos;
        }
        // 设置新位置
        if (this.movePos) {
            this.node.setPosition(this.movePos);
        }

        // 更新方向
        let new_orientation = this.vertical_orientation;
        if (direction.y >= 0.5) {
            new_orientation = "up";
        } else if (direction.y < -0.5) {
            new_orientation = "down";
        }
        if (direction.x >= 0) {
            this.node.setScale(new Vec3(1, 1, 0));
        } else if (direction.x < 0) {
            this.node.setScale(new Vec3(-1, 1, 0));
        }

        // 只有在方向改变时才更新动画
        if (new_orientation !== this.vertical_orientation || this.current_animation !== this.employeeId + `_walk_side_${new_orientation}`) {
            this.vertical_orientation = new_orientation;
            this.current_animation = this.employeeId + `_walk_side_${this.vertical_orientation}`;

            if (this.animated_sprite) {
                console.log(`播放新动画: ${this.current_animation}`);
                this.animated_sprite.play(this.current_animation);
            }
        }

        // 检查是否接近目标
        const dist = Vec3.distance(newPos, targetPos);
        // console.log(`距离目标: ${dist.toFixed(2)}, 需要最小距离: ${this.needed_minimal_distance}`);

        if (dist <= 5) {
            // 到达目标，切换到交互状态
            console.log("到达目标，切换到 EXIT2 状态");

            this.state = STATE.EXIT2;
        }
    }
    exit_state2(deltaTime: number) {
        // 移动向目标
        const myPos = this.node.getPosition();
        const targetPos = this.enterPos1;

        // console.log(`当前位置: (${myPos.x.toFixed(2)}, ${myPos.y.toFixed(2)}), 目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);

        // 计算新位置
        const direction = new Vec3(
            targetPos.x - myPos.x,
            targetPos.y - myPos.y,
            0
        ).normalize();

        // 使用静态属性获取效率乘数
        let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
        if (this.skillHighEfficientWork) {
            const level = StatsContext.getSkill(this.skillKey);
            helperEfficiency *= (level + 1);
        }
        const newPos = new Vec3(
            myPos.x + direction.x * deltaTime * this.move_speed * helperEfficiency,
            myPos.y + direction.y * deltaTime * this.move_speed * helperEfficiency,
            myPos.z
        );

        // console.log(`新位置: (${newPos.x.toFixed(2)}, ${newPos.y.toFixed(2)}), 移动速度: ${this.move_speed}, 效率: ${helperEfficiency}`);
        if (newPos.x != 0 && newPos.y != 0) {
            this.movePos = newPos;
        }
        // 设置新位置
        if (this.movePos) {
            this.node.setPosition(this.movePos);
        }

        // 更新方向
        let new_orientation = this.vertical_orientation;
        if (direction.y >= 0.5) {
            new_orientation = "up";
        } else if (direction.y < -0.5) {
            new_orientation = "down";
        }
        if (direction.x >= 0) {
            this.node.setScale(new Vec3(1, 1, 0));
        } else if (direction.x < 0) {
            this.node.setScale(new Vec3(-1, 1, 0));
        }

        // 只有在方向改变时才更新动画
        if (new_orientation !== this.vertical_orientation || this.current_animation !== this.employeeId + `_walk_side_${new_orientation}`) {
            this.vertical_orientation = new_orientation;
            this.current_animation = this.employeeId + `_walk_side_${this.vertical_orientation}`;

            if (this.animated_sprite) {
                console.log(`播放新动画: ${this.current_animation}`);
                this.animated_sprite.play(this.current_animation);
            }
        }

        // 检查是否接近目标
        const dist = Vec3.distance(newPos, targetPos);
        // console.log(`距离目标: ${dist.toFixed(2)}, 需要最小距离: ${this.needed_minimal_distance}`);

        if (dist <= 5) {
            // 到达目标，切换到交互状态
            console.log("到达目标，切换到 EXIT3 状态");

            this.state = STATE.EXIT3;
        }
    }
    exit_state3(deltaTime: number) {
        // 移动向目标
        const myPos = this.node.getPosition();
        const targetPos = this.exitPos;

        // console.log(`当前位置: (${myPos.x.toFixed(2)}, ${myPos.y.toFixed(2)}), 目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);

        // 计算新位置
        const direction = new Vec3(
            targetPos.x - myPos.x,
            targetPos.y - myPos.y,
            0
        ).normalize();

        // 使用静态属性获取效率乘数
        let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
        if (this.skillHighEfficientWork) {
            const level = StatsContext.getSkill(this.skillKey);
            helperEfficiency *= (level + 1);
        }
        const newPos = new Vec3(
            myPos.x + direction.x * deltaTime * this.move_speed * helperEfficiency,
            myPos.y + direction.y * deltaTime * this.move_speed * helperEfficiency,
            myPos.z
        );

        // console.log(`新位置: (${newPos.x.toFixed(2)}, ${newPos.y.toFixed(2)}), 移动速度: ${this.move_speed}, 效率: ${helperEfficiency}`);
        if (newPos.x != 0 && newPos.y != 0) {
            this.movePos = newPos;
        }
        // 设置新位置
        if (this.movePos) {
            this.node.setPosition(this.movePos);
        }

        // 更新方向
        let new_orientation = this.vertical_orientation;
        if (direction.y >= 0.5) {
            new_orientation = "up";
        } else if (direction.y < -0.5) {
            new_orientation = "down";
        }
        if (direction.x >= 0) {
            this.node.setScale(new Vec3(1, 1, 0));
        } else if (direction.x < 0) {
            this.node.setScale(new Vec3(-1, 1, 0));
        }

        // 只有在方向改变时才更新动画
        if (new_orientation !== this.vertical_orientation || this.current_animation !== this.employeeId + `_walk_side_${new_orientation}`) {
            this.vertical_orientation = new_orientation;
            this.current_animation = this.employeeId + `_walk_side_${this.vertical_orientation}`;

            if (this.animated_sprite) {
                console.log(`播放新动画: ${this.current_animation}`);
                this.animated_sprite.play(this.current_animation);
            }
        }

        // 检查是否接近目标
        const dist = Vec3.distance(newPos, targetPos);
        // console.log(`距离目标: ${dist.toFixed(2)}, 需要最小距离: ${this.needed_minimal_distance}`);

        if (dist <= 5) {
            // 到达目标，切换到交互状态
            console.log("到达目标，切换到 IDLING 状态");

            //通知角色离场
            StatsContext.events.emit(GameEvent.EMPLOYEE_EXITED, "employee_"+this.employeeId);
        }
    }

    // 状态处理方法
    idle_state(deltaTime: number) {
        this.state_time_current += deltaTime;
        if (this.state_time_current > this.state_time) {
            this.state_time_current = 0;
            console.log("从IDLE状态转为选择目标");
            // 检查available_targets是否有效
            if (!this.available_targets || this.available_targets.length === 0) {
                console.log("没有可用目标，继续等待", this.employeeId);
                // 没有目标时，延长等待时间
                this.state_time = 3.0;
                return;
            }

            this.assign_target_interactable();

            // 如果没有找到目标，继续等待
            if (!this.current_target) {
                console.log("未找到有效目标，继续等待", this.employeeId);
                return;
            }

            this.set_orientation();
            this.state = STATE.MOVING_TO_TARGET;
        }
    }

    sleep_state(deltaTime: number) {
        this.state_time_current += deltaTime;
        if (this.state_time_current > this.state_time) {
            this.state_time_current = 0;
            console.log("从SLEEP状态转为选择目标");

            // 检查available_targets是否有效
            if (!this.available_targets || this.available_targets.length === 0) {
                console.log("没有可用目标，继续睡眠", this.employeeId);
                // 没有目标时，继续睡眠
                this.state_time = 3.0;
                return;
            }

            this.assign_target_interactable();

            // 如果没有找到目标，继续睡眠
            if (!this.current_target) {
                console.log("未找到有效目标，继续睡眠", this.employeeId);
                return;
            }

            this.set_orientation();
            this.state = STATE.MOVING_TO_TARGET;
        }
    }

    move_to_target_state(deltaTime: number) {
        if (!this.current_target) {
            console.log("移动状态中没有目标，回到IDLE状态");
            this.state = STATE.IDLING;
            // 没有目标时设置较长的等待时间
            this.state_time = 2.0;
            return;
        }

        // 检查目标是否仍然有效(未被销毁)
        if (!this.current_target.node || !this.current_target.node.isValid) {
            console.log("目标已失效，回到IDLE状态");
            this.current_target = null;
            this.state = STATE.IDLING;
            this.state_time = 2.0;
            return;
        }

        if (!this.node || !this.node.parent) return;

        // 移动向目标
        const myPos = this.node.getPosition();
        const targetPos = new Vec3(this.current_target.node.x, this.current_target.node.y, 0);

        // console.log(`当前位置: (${myPos.x.toFixed(2)}, ${myPos.y.toFixed(2)}), 目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);

        // 计算新位置
        const direction = new Vec3(
            targetPos.x - myPos.x,
            targetPos.y - myPos.y,
            0
        ).normalize();

        // 使用静态属性获取效率乘数
        let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
        if (this.skillHighEfficientWork) {
            const level = StatsContext.getSkill(this.skillKey);
            helperEfficiency *= (level + 1);
        }
        const newPos = new Vec3(
            myPos.x + direction.x * deltaTime * this.move_speed * helperEfficiency,
            myPos.y + direction.y * deltaTime * this.move_speed * helperEfficiency,
            myPos.z
        );

        // console.log(`新位置: (${newPos.x.toFixed(2)}, ${newPos.y.toFixed(2)}), 移动速度: ${this.move_speed}, 效率: ${helperEfficiency}`);
        if (newPos.x != 0 && newPos.y != 0) {
            this.movePos = newPos;
        }
        // 设置新位置
        if (this.movePos) {
            this.node.setPosition(this.movePos);
        }

        // 更新方向
        let new_orientation = this.vertical_orientation;
        if (direction.y >= 0.5) {
            new_orientation = "up";
        } else if (direction.y < -0.5) {
            new_orientation = "down";
        }
        if (direction.x >= 0) {
            this.node.setScale(new Vec3(1, 1, 0));
        } else if (direction.x < 0) {
            this.node.setScale(new Vec3(-1, 1, 0));
        }

        // 只有在方向改变时才更新动画
        if (new_orientation !== this.vertical_orientation || this.current_animation !== this.employeeId + `_walk_side_${new_orientation}`) {
            this.vertical_orientation = new_orientation;
            this.current_animation = this.employeeId + `_walk_side_${this.vertical_orientation}`;

            if (this.animated_sprite) {
                console.log(`播放新动画: ${this.current_animation}`);
                this.animated_sprite.play(this.current_animation);
            }
        }

        // 检查是否接近目标
        const dist = Vec3.distance(newPos, targetPos);
        // console.log(`距离目标: ${dist.toFixed(2)}, 需要最小距离: ${this.needed_minimal_distance}`);

        if (dist <= this.needed_minimal_distance) {
            // 到达目标，切换到交互状态
            console.log("到达目标，切换到INTERACTING状态");

            // 将 this.current_target.node 移动到父节点子节点列表的末尾
            if (this.current_target.node.parent) {
                this.current_target.node.setSiblingIndex(this.current_target.node.parent.children.length - 1);
            }

            this.state = STATE.INTERACTING;
        }
    }

    // 辅助方法
    assign_target_interactable() {
        // 释放当前目标
        this.releaseCurrentTarget();

        if (!this.available_targets || this.available_targets.length === 0) {
            console.log("没有可用目标，设置短暂等待时间", this.employeeId);
            console.log("available_targets:", this.available_targets);
            this.current_target = null;
            this.state_time = 3.0; // 如果没有可用目标，等待时间延长
            return;
        }

        console.log(`可用目标数量: ${this.available_targets.length}`, this.employeeId);

        // 检查targets是否都是有效的
        const validTargets = this.available_targets.filter(target =>
            target && target.node && target.node.isValid
        );

        if (validTargets.length < this.available_targets.length) {
            console.log(`检测到 ${this.available_targets.length - validTargets.length} 个无效目标，已过滤`);
            this.available_targets = validTargets;
        }

        // 获取可用的未被其他员工选中的目标
        const availableTargets = this.available_targets.filter(target => {
            // 如果是硬币类型，检查是否已被其他员工选为目标
            if (target instanceof Coin) {
                const targetId = target.node.uuid;
                const isTargeted = Employee.targetedInteractables.has(targetId);
                const isFlipping = (target as Coin).has_flip_started;

                console.log(`硬币ID: ${targetId}, 已被目标: ${isTargeted}, 正在翻转: ${isFlipping}`);

                if (isTargeted) {
                    return false;
                }

                // 检查硬币是否已经在翻转中
                if (isFlipping) {
                    return false;
                }
            }
            return true;
        });

        console.log(`过滤后的可用目标数量: ${availableTargets.length}`, this.employeeId);

        if (availableTargets.length === 0) {
            console.log("过滤后没有可用目标，设置等待时间", this.employeeId);
            this.current_target = null;
            this.state_time = 3.0; // 如果没有可用目标，等待时间延长
            return;
        }

        // 随机选择一个目标
        const randomIndex = Math.floor(Math.random() * availableTargets.length);
        this.current_target = availableTargets[randomIndex];

        console.log(`选择了目标索引: ${randomIndex}, 目标ID: ${this.current_target.node.uuid}`, this.employeeId);

        // 将目标添加到已选中目标列表
        if (this.current_target instanceof Coin) {
            Employee.targetedInteractables.set(this.current_target.node.uuid, this.current_target);
            console.log(`将硬币添加到已选中目标列表, 当前列表大小: ${Employee.targetedInteractables.size}`);
        }
    }

    // 释放当前目标
    private releaseCurrentTarget(): void {
        if (this.current_target) {
            if (this.current_target instanceof Coin) {
                const targetId = this.current_target.node.uuid;
                if (Employee.targetedInteractables.get(targetId) === this.current_target) {
                    Employee.targetedInteractables.delete(targetId);
                }
            }
            this.current_target = null;
        }
    }

    set_orientation() {
        if (!this.animated_sprite || !this.current_target) return;

        // 计算垂直差异
        const myPos = this.node.getPosition();
        const targetPos = this.current_target.node.getPosition();

        const vDiff = targetPos.y - myPos.y;

        // 设置垂直方向
        if (vDiff !== 0) {
            this.vertical_orientation = vDiff < 0 ? "up" : "down";
        }
    }

    throwCoin() {
        if (this.current_target instanceof Coin) {
            console.log("目标是硬币，触发翻转");
            // 假设Coin.SOURCE.HELPER是0
            (this.current_target as Coin).start_flip_action(SOURCE.EMPLOYEE);

            // 交互完成后释放目标
            this.releaseCurrentTarget();
        }
    }

    onAnimationFinished(type: string, state: any) {
        const stateName = state.name;

        console.log(`动画完成: ${stateName}`);

        // 处理throw_动画完成后的行为
        if (stateName.startsWith(this.employeeId + "_throw_")) {
            if (this.current_target instanceof Coin) {
                // console.log("目标是硬币，触发翻转");
                // // 假设Coin.SOURCE.HELPER是0
                // (this.current_target as Coin).start_flip_action(SOURCE.HELPER);

                // // 交互完成后释放目标
                // this.releaseCurrentTarget();
            } else if (this.current_target) {
                console.log("目标不是硬币，触发点击事件");
                // 触发目标的鼠标点击事件
                this.current_target.mouse_clicked.emit();

                // 交互完成后释放目标
                this.releaseCurrentTarget();
            }

            // 随机决定是否睡眠
            if (Math.random() <= this.base_sleep_chance && !this.skillEmergencyMobilization) {
                console.log("随机进入睡眠状态");
                this.state = STATE.SLEEPING;

                const helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
                this.state_time = Math.random() * (this.maximum_sleep_time - this.minimal_sleep_time) + this.minimal_sleep_time;
                this.state_time /= helperEfficiency; // 效率越高，睡眠时间越短
                console.log(`睡眠时间: ${this.state_time.toFixed(2)}秒`);
            } else {
                console.log("返回到IDLE状态");
                this.state = STATE.IDLING;

                let helperEfficiency = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;
                if (this.skillHighEfficientWork) {
                    const level = StatsContext.getSkill(this.skillKey);
                    helperEfficiency *= (level + 1);
                }
                this.state_time = Math.random() * (this.maximum_idle_time - this.minimal_idle_time) + this.minimal_idle_time;
                this.state_time /= helperEfficiency; // 效率越高，空闲时间越短
                console.log(`空闲时间: ${this.state_time.toFixed(2)}秒`);
            }
        }
    }

    /**
     * 鼠标点击事件处理
     * 重写父类的方法
     */
    _handle_click() {
        // 如果是睡眠状态，唤醒员工
        if (this.state === STATE.SLEEPING) {
            this.state_time_current = this.state_time + 1;
            return;
        }

        // 如果有等级，点击后会展示员工信息或升级界面
        console.log(`Helper clicked: ${this.employeeId}, Level: ${this._level}`);

        // 如果可以升级，自动尝试升级
        if (this.canLevelUp()) {
            this.levelUp();
        }
    }

    /**
     * 状态访问器
     */
    get state(): STATE {
        return this._state;
    }

    /**
     * 状态设置器
     */
    set state(value: STATE) {
        if (this._state === value) return;

        this._state = value;
        this.state_time_current = 0;

        // 根据状态设置动画
        if (!this.animated_sprite) return;

        let animation_name = "";

        switch (value) {
            case STATE.IDLING:
                animation_name = this.employeeId + `_idle_${this.vertical_orientation}`;
                console.log(`Playing idle animation: ${animation_name}`);
                break;
            case STATE.MOVING_TO_TARGET:
                animation_name = this.employeeId + `_walk_side_${this.vertical_orientation}`;
                console.log(`Playing walk animation: ${animation_name}`);
                break;
            case STATE.INTERACTING:
                animation_name = this.employeeId + `_throw_${this.vertical_orientation}`;
                console.log(`Playing throw animation: ${animation_name}`);
                break;
            case STATE.SLEEPING:
                animation_name = this.employeeId + "_sleep";
                console.log(`Playing sleep animation: ${animation_name}`);
                break;
        }

        if (animation_name && this.current_animation !== animation_name) {
            this.current_animation = animation_name;
            this.animated_sprite.play(animation_name);
        }
    }

    /**
     * 升级员工
     * @returns 是否成功升级
     */
    levelUp(): boolean {
        if (!this._statsContext) return false;

        // 获取升级成本
        const upgradeCost = this.getUpgradeCost();

        // 检查玩家是否有足够硬币
        if (!this._statsContext.consumeCoins(upgradeCost)) return false;

        // 升级
        this._level++;
        this._statsContext.setHelperLevel(this.employeeId, this._level);

        // 更新收入率
        this.updateIncomeRate();

        // 更新UI
        this.updateUI();

        return true;
    }

    /**
     * 更新收益率
     */
    updateIncomeRate(): void {
        if (!this._level) {
            this._baseIncome = 0;
            this._incomePerSecond = 0;
            return;
        }

        // 基础收入值
        const baseIncomeValue = 1 + this._level * 0.5;
        this._baseIncome = baseIncomeValue;

        // 获取员工加成倍率
        const helperBonus = this._statsContext?.getHelperBonus() || 1;

        // 获取效率乘数
        const efficiencyMultiplier = StatsContext.getUpgradeModifier(PURCHASABLE_ITEM_ID.HELPER_EFFICIENCY) || 1.0;

        // 计算每秒收益
        this._incomePerSecond = this._baseIncome * helperBonus * efficiencyMultiplier;
    }

    /**
     * 生成收益
     */
    generateIncome(): void {
        if (!this._level || this._incomePerSecond <= 0) return;

        // 产生收益
        this.emitValueGained(this._incomePerSecond, "HELPER");

        // 显示收益文本
        this.showIncomeText(this._incomePerSecond);
    }

    /**
     * 更新UI显示
     */
    updateUI(): void {
        // 更新等级标签
        if (this.levelLabel) {
            this.levelLabel.string = `Lv.${this._level}`;
        }

        // 更新收益标签
        if (this.incomeLabel && this._level > 0) {
            this.incomeLabel.string = `+${this._incomePerSecond.toFixed(1)}/s`;
        } else if (this.incomeLabel) {
            this.incomeLabel.string = "";
        }
    }

    /**
     * 获取升级成本
     * @returns 升级成本
     */
    getUpgradeCost(): number {
        // 基础成本
        const baseCost = 50;

        // 成本增长因子
        const growthFactor = 1.15;

        // 计算升级成本
        // 使用公式: baseCost * (growthFactor ^ level)
        return Math.floor(baseCost * Math.pow(growthFactor, this._level));
    }

    /**
     * 检查是否可以升级
     * @returns 是否可以升级
     */
    canLevelUp(): boolean {
        if (!this._statsContext) return false;

        // 获取玩家可用硬币
        const availableCoins = this._statsContext.getCoins();

        // 获取升级成本
        const upgradeCost = this.getUpgradeCost();

        // 检查是否够钱升级
        return availableCoins >= upgradeCost;
    }

    /**
     * 显示收益文本
     * @param income 收益值
     */
    showIncomeText(income: number): void {
        if (income <= 0) return;
        if (!this.node) return;

        // 使用FloatingText显示收益
        const textPos = new Vec3(
            this.node.position.x,
            this.node.position.y + 50,
            this.node.position.z
        );

        // TODO: 调用FloatingText.create显示收益
        FloatingText.create(
            this.node.parent,
            textPos,
            `+${income.toLocaleString()}`
        );
    }

    onUseSkill(key: string) {
        this.skillKey = key;
        if (!this._config) {
            this._config = ConfigContext.getPurchasableItemConfig(this.skillKey.toLowerCase() as PURCHASABLE_ITEM_ID);
            if (!this._config) {
                console.warn(`[Purchasable] 配置不存在: ${this.skillKey} (尝试使用 ${this.skillKey.toLowerCase()})`);
            } else {
                console.log(`[Purchasable] 配置初始化成功: ${this.skillKey}`);
            }
        }
        if (this.skillKey === PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION) {
            this.skillEmergencyMobilization = true;
        } else if (this.skillKey == PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK) {
            this.skillHighEfficientWork = true;
        }
    }
    onUseSkillFinish(key: string) {
        if (key === PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION) {
            this.skillEmergencyMobilization = false;
        } else if (key === PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK) {
            this.skillHighEfficientWork = false;
        }
    }
    onEmployeeExpired(key: string) {
        if (key == this.employeeID) {
            this.state = STATE.EXIT1;
        }
    }
}