import { _decorator, Component, Node, EventMouse, input, Input, Vec3, UITransform, EventTouch, sys, game, v2, EventKeyboard } from 'cc';
import { Item } from '../Item';
import { StatsContext } from '../../contexts/StatsContext';
import { ConfigContext } from '../../contexts/ConfigContext';
import { AutomationContext } from '../../contexts/AutomationContext';
import { FloatingText, FloatingTextSource, FloatingTextType } from '../../ui/FloatingText';

const { ccclass, property } = _decorator;

// 定义金钱统计类型
interface MoneyEarned {
    total: number;
    sources: Record<string, number>;
}

@ccclass('Interactable')
export class Interactable extends Item {
    @property
    value_key: string = "NO_KEY";

    @property
    idKey: string = "NO_KEY";

    value: number = 0;

    private _is_hovered: boolean = false;

    // 事件
    value_gained = null;
    mouse_clicked = null;
    mouse_hovered = null;

    onLoad() {
         // 初始化事件
        this.value_gained = new EventTarget();
        this.mouse_clicked = new EventTarget();
        this.mouse_hovered = new EventTarget();
    }

    start() {
        super.start();

        // 初始化金钱统计
        if (!StatsContext.moneyEarnedByInteractable) {
            // 使用类型断言来避免只读属性错误
            (StatsContext as any)._moneyEarnedByInteractable = new Map<string, MoneyEarned>();
        }

        if (!StatsContext.moneyEarnedByInteractable.has(this.idKey)) {
            const new_value_earned: MoneyEarned = {
                "total": 0,
                "sources": {}
            };
            (StatsContext.moneyEarnedByInteractable as unknown as Map<string, MoneyEarned>).set(this.idKey, new_value_earned);
        }

        // 设置价值
        this.value = ConfigContext.getBaseValue(this.value_key);

        // 监听鼠标事件
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);

        // 监听value_gained事件
        this.value_gained.addEventListener('value_gained', this._on_value_gained.bind(this));
    }

    onDestroy() {
        super.onDestroy();

        // 移除事件监听
        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
    }

    update(deltaTime: number) {
        super.update(deltaTime);

        if (!Item.collision_distance_diameter_squared) return;

        const was_hovered = this._is_hovered;

        // 获取触摸/鼠标位置
        let touchPos = { x: 0, y: 0 };
        const touches = input.getAllTouches();
        if (touches.length > 0) {
            const touch = touches[0];
            touchPos = touch.getLocation();
        }

        const worldPos = this.node.getWorldPosition();
        const uiTransform = this.getComponent(UITransform);

        // 计算距离
        if (uiTransform) {
            const localPos = uiTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0));
            const distanceSquared = localPos.x * localPos.x + localPos.y * localPos.y;

            this._is_hovered = distanceSquared < Item.collision_distance_diameter_squared;

            if (this._is_hovered) {
                // 检查触摸/鼠标输入
                if (touches.length > 0) {
                    const event = new Event('mouse_clicked');
                    this.mouse_clicked.dispatchEvent(event);
                }

                if (was_hovered !== this._is_hovered) {
                    const event = new Event('mouse_hovered');
                    this.mouse_hovered.dispatchEvent(event);
                }
            }
        }
    }

    onTouchStart(event: EventTouch) {
        const event2 = new Event('mouse_clicked');
        this.mouse_clicked.dispatchEvent(event2);
    }

    onMouseEnter(event: EventMouse) {
        const event2 = new Event('mouse_hovered');
        this.mouse_hovered.dispatchEvent(event2);
    }

    _on_value_gained(event: any) {
        const given_value = event.detail.value as number;
        const source = event.detail.source as string;

        if (given_value > 0) {
            // 类型转换，确保能够读取total和sources属性
            const moneyEarnedMap = StatsContext.moneyEarnedByInteractable as unknown as Map<string, MoneyEarned>;
            const value_earned = moneyEarnedMap.get(this.idKey);

            if (value_earned) {
                value_earned.total += given_value;
                
                if (value_earned.sources[source]) {
                    value_earned.sources[source] += given_value;
                } else {
                    value_earned.sources[source] = given_value;
                }
            }

            // 比较值大小
            const highestValue = StatsContext.highestValueGained;

            if (given_value > highestValue) {
                StatsContext.highestValueGained = given_value;
            }

            // 如果自动点击器开启，则不显示浮动文本
            if (AutomationContext.IS_ACTIVE) return;

            let size_factor = 1.0;
            if (StatsContext.highestValueGained >= 1) {
                size_factor = given_value / StatsContext.highestValueGained;
            }

            this.reset_physics_interpolation();

            // 使用 FloatingText.create
            FloatingText.create(
                this.node.getWorldPosition(),
                given_value,
                FloatingTextType.COIN_VALUE,
                FloatingTextSource.PLAYER_CLICK
            );
        }
    }

    // 触发value_gained事件的辅助方法
    emitValueGained(value: number, source: string) {
        console.log("emitValueGained: ", value, source);
        const event = new CustomEvent('value_gained', {
            detail: {
                value: value,
                source: source
            }
        });
        this.value_gained.dispatchEvent(event);
    }

    reset_physics_interpolation() {
        // 重置物理插值，用于动画结束后的状态恢复
    }
}