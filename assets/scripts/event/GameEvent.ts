import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 事件监听器接口
 */
interface EventListener {
    callback: Function;
    target?: any;
    once?: boolean;
}

@ccclass('GameEvent')
export class GameEvent {
    // === 游戏业务事件常量 ===
    static readonly SHOP_ITEM_INTRODUCE_BUY: string = "shop_item_introduce_buy";//在道具详情页上购买

    static readonly EMPLOYEE_CHANGED:string = "employee_changed";//增加雇员
    static readonly SCENE_CHANGED: string = "scene_changed";//场景改变
    static readonly SKILL_FRAGMENT_CHANGED: string = "skill_fragment_changed";//技能碎片数量改变
    static readonly SKILL_LEVEL_CHANGED: string = "skill_level_changed";//技能等级改变
    static readonly USE_SKILL : string = "use_skill";//释放技能
    static readonly USE_SKILL_FUNISH : string = "use_skill_finish";//技能结束

    static readonly SELECT_SKILL_FRAGMENT: string = "select_skill_fragment";//选择技能碎片
    static readonly SKILL_FRAGMENT_TASK_COMPLETE: string = "skill_fragment_task_complete";//技能碎片任务完成

    static readonly EMPLOYEE_EXPIRED: string = "employee_expired";//雇员过期
    static readonly EMPLOYEE_EXITED: string = "employee_exited";//雇员离场
    static readonly EMPLOYEE_EXIT_COMPLETE: string = "employee_exit_complete";//雇员离场完成

    static readonly RESET_GAME: string = "reset_game";//重置游戏

    static readonly MAIN_TASK_CLICK_COIN = "main_task_click_coin";//主线任务点击硬币
    static readonly MAIN_TASK_COMPLETE_REWARD = "main_task_complete_reward";//主线任务完成奖励

    static readonly GUIDE_CHANGED = "guide_changed";//新手指引改变

    static readonly MONEY_CHANGE = "money_changed";//钱数变化
    static readonly MAKE_MONEY = "make_money";//赚钱

    static readonly DIAMOND_CHANGE = "diamond_changed";//钻石变化

    // === 版本管理事件常量 ===
    static readonly VERSION_MANAGER_READY = "version-manager-ready";//版本管理器就绪
    static readonly VERSION_CHECK_START = "version-check-start";//版本检查开始
    static readonly VERSION_CHECK_COMPLETE = "version-check-complete";//版本检查完成
    static readonly VERSION_CHECK_ERROR = "version-check-error";//版本检查错误
    static readonly HOT_UPDATE_START = "hot-update-start";//热更新开始
    static readonly HOT_UPDATE_PROGRESS = "hot-update-progress";//热更新进度
    static readonly HOT_UPDATE_COMPLETE = "hot-update-complete";//热更新完成
    static readonly HOT_UPDATE_ERROR = "hot-update-error";//热更新错误
    static readonly RESOURCES_UPDATED = "resources-updated";//资源更新完成

    // === 事件管理系统 ===
    private static _eventMap: Map<string, EventListener[]> = new Map();
    private static _isDestroyed: boolean = false;

    /**
     * 注册事件监听器
     * @param eventName 事件名称
     * @param callback 回调函数
     * @param target 目标对象（用于自动清理）
     */
    public static on(eventName: string, callback: Function, target?: any): void {
        if (this._isDestroyed) {
            console.warn('[GameEvent] 事件系统已销毁，无法注册事件:', eventName);
            return;
        }

        if (!eventName || typeof callback !== 'function') {
            console.error('[GameEvent] 注册事件失败，参数无效:', eventName, callback);
            return;
        }

        if (!this._eventMap.has(eventName)) {
            this._eventMap.set(eventName, []);
        }

        const listeners = this._eventMap.get(eventName)!;

        // 检查是否已经注册过相同的监听器
        const existingListener = listeners.find(listener =>
            listener.callback === callback && listener.target === target
        );

        if (existingListener) {
            console.warn('[GameEvent] 重复注册事件监听器:', eventName);
            return;
        }

        listeners.push({
            callback,
            target,
            once: false
        });

        console.log(`[GameEvent] 注册事件监听器: ${eventName}, 当前监听器数量: ${listeners.length}`);
    }

    /**
     * 注册一次性事件监听器
     * @param eventName 事件名称
     * @param callback 回调函数
     * @param target 目标对象
     */
    public static once(eventName: string, callback: Function, target?: any): void {
        if (this._isDestroyed) {
            console.warn('[GameEvent] 事件系统已销毁，无法注册事件:', eventName);
            return;
        }

        if (!eventName || typeof callback !== 'function') {
            console.error('[GameEvent] 注册一次性事件失败，参数无效:', eventName, callback);
            return;
        }

        if (!this._eventMap.has(eventName)) {
            this._eventMap.set(eventName, []);
        }

        const listeners = this._eventMap.get(eventName)!;
        listeners.push({
            callback,
            target,
            once: true
        });

        console.log(`[GameEvent] 注册一次性事件监听器: ${eventName}`);
    }

    /**
     * 移除事件监听器
     * @param eventName 事件名称
     * @param callback 回调函数（可选，不传则移除该事件的所有监听器）
     * @param target 目标对象（可选）
     */
    public static off(eventName: string, callback?: Function, target?: any): void {
        if (!this._eventMap.has(eventName)) {
            return;
        }

        const listeners = this._eventMap.get(eventName)!;

        if (!callback) {
            // 如果没有指定回调函数，移除所有监听器
            if (target) {
                // 只移除指定target的监听器
                const remainingListeners = listeners.filter(listener => listener.target !== target);
                this._eventMap.set(eventName, remainingListeners);
                console.log(`[GameEvent] 移除目标对象的所有监听器: ${eventName}, target:`, target);
            } else {
                // 移除所有监听器
                this._eventMap.delete(eventName);
                console.log(`[GameEvent] 移除事件的所有监听器: ${eventName}`);
            }
            return;
        }

        // 移除指定的监听器
        const index = listeners.findIndex(listener =>
            listener.callback === callback &&
            (target === undefined || listener.target === target)
        );

        if (index !== -1) {
            listeners.splice(index, 1);
            console.log(`[GameEvent] 移除事件监听器: ${eventName}`);

            // 如果没有监听器了，删除整个事件
            if (listeners.length === 0) {
                this._eventMap.delete(eventName);
            }
        }
    }

    /**
     * 触发事件
     * @param eventName 事件名称
     * @param data 事件数据
     */
    public static emit(eventName: string, data?: any): void {
        if (this._isDestroyed) {
            console.warn('[GameEvent] 事件系统已销毁，无法触发事件:', eventName);
            return;
        }

        if (!this._eventMap.has(eventName)) {
            console.log(`[GameEvent] 没有找到事件监听器: ${eventName}`);
            return;
        }

        const listeners = this._eventMap.get(eventName)!.slice(); // 复制数组，避免在回调中修改原数组
        const onceListeners: EventListener[] = [];

        console.log(`[GameEvent] 触发事件: ${eventName}, 监听器数量: ${listeners.length}, 数据:`, data);

        for (const listener of listeners) {
            try {
                if (listener.target && listener.target.isValid === false) {
                    // 目标对象已被销毁，标记为需要清理
                    onceListeners.push(listener);
                    continue;
                }

                // 执行回调
                if (listener.target) {
                    listener.callback.call(listener.target, data);
                } else {
                    listener.callback(data);
                }

                // 如果是一次性监听器，标记为需要移除
                if (listener.once) {
                    onceListeners.push(listener);
                }

            } catch (error) {
                console.error(`[GameEvent] 事件回调执行失败: ${eventName}`, error);
            }
        }

        // 清理一次性监听器和无效监听器
        if (onceListeners.length > 0) {
            const remainingListeners = this._eventMap.get(eventName)!.filter(
                listener => !onceListeners.includes(listener)
            );

            if (remainingListeners.length === 0) {
                this._eventMap.delete(eventName);
            } else {
                this._eventMap.set(eventName, remainingListeners);
            }
        }
    }

    /**
     * 检查是否有指定事件的监听器
     * @param eventName 事件名称
     * @returns 是否有监听器
     */
    public static hasListener(eventName: string): boolean {
        return this._eventMap.has(eventName) && this._eventMap.get(eventName)!.length > 0;
    }

    /**
     * 获取指定事件的监听器数量
     * @param eventName 事件名称
     * @returns 监听器数量
     */
    public static getListenerCount(eventName: string): number {
        if (!this._eventMap.has(eventName)) {
            return 0;
        }
        return this._eventMap.get(eventName)!.length;
    }

    /**
     * 清理指定目标对象的所有事件监听器
     * @param target 目标对象
     */
    public static offTarget(target: any): void {
        if (!target) {
            return;
        }

        let removedCount = 0;
        const eventsToDelete: string[] = [];

        this._eventMap.forEach((listeners, eventName) => {
            const remainingListeners = listeners.filter(listener => {
                if (listener.target === target) {
                    removedCount++;
                    return false;
                }
                return true;
            });

            if (remainingListeners.length === 0) {
                eventsToDelete.push(eventName);
            } else if (remainingListeners.length !== listeners.length) {
                this._eventMap.set(eventName, remainingListeners);
            }
        });

        // 删除没有监听器的事件
        eventsToDelete.forEach(eventName => {
            this._eventMap.delete(eventName);
        });

        if (removedCount > 0) {
            console.log(`[GameEvent] 清理目标对象的事件监听器: ${removedCount} 个, target:`, target);
        }
    }

    /**
     * 清理所有事件监听器
     */
    public static clear(): void {
        const eventCount = this._eventMap.size;
        this._eventMap.clear();
        console.log(`[GameEvent] 清理所有事件监听器: ${eventCount} 个事件`);
    }

    /**
     * 获取调试信息
     */
    public static getDebugInfo(): {
        totalEvents: number;
        totalListeners: number;
        eventDetails: { [eventName: string]: number };
        isDestroyed: boolean;
    } {
        const eventDetails: { [eventName: string]: number } = {};
        let totalListeners = 0;

        this._eventMap.forEach((listeners, eventName) => {
            eventDetails[eventName] = listeners.length;
            totalListeners += listeners.length;
        });

        return {
            totalEvents: this._eventMap.size,
            totalListeners,
            eventDetails,
            isDestroyed: this._isDestroyed
        };
    }

    /**
     * 打印调试信息
     */
    public static printDebugInfo(): void {
        const debugInfo = this.getDebugInfo();
        console.log('[GameEvent] === 事件系统调试信息 ===');
        console.log('总事件数:', debugInfo.totalEvents);
        console.log('总监听器数:', debugInfo.totalListeners);
        console.log('是否已销毁:', debugInfo.isDestroyed);
        console.log('事件详情:', debugInfo.eventDetails);
    }
}
