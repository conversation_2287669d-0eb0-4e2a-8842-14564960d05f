import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameEvent')
export class GameEvent {
    static readonly SHOP_ITEM_INTRODUCE_BUY: string = "shop_item_introduce_buy";//在道具详情页上购买

    static readonly EMPLOYEE_CHANGED:string = "employee_changed";//增加雇员
    static readonly SCENE_CHANGED: string = "scene_changed";//场景改变
    static readonly SKILL_FRAGMENT_CHANGED: string = "skill_fragment_changed";//技能碎片数量改变
    static readonly SKILL_LEVEL_CHANGED: string = "skill_level_changed";//技能等级改变
    static readonly USE_SKILL : string = "use_skill";//释放技能
    static readonly USE_SKILL_FUNISH : string = "use_skill_finish";//技能结束
    
    static readonly SELECT_SKILL_FRAGMENT: string = "select_skill_fragment";//选择技能碎片 
    static readonly SKILL_FRAGMENT_TASK_COMPLETE: string = "skill_fragment_task_complete";//技能碎片任务完成
    
    static readonly EMPLOYEE_EXPIRED: string = "employee_expired";//雇员过期
    static readonly EMPLOYEE_EXITED: string = "employee_exited";//雇员离场
    static readonly EMPLOYEE_EXIT_COMPLETE: string = "employee_exit_complete";//雇员离场完成

    static readonly RESET_GAME: string = "reset_game";//重置游戏

    static readonly MAIN_TASK_CLICK_COIN = "main_task_click_coin";//主线任务点击硬币
    static readonly MAIN_TASK_COMPLETE_REWARD = "main_task_complete_reward";//主线任务完成奖励

    static readonly GUIDE_CHANGED = "guide_changed";//新手指引改变

    static readonly MONEY_CHANGE = "money_changed";//钱数变化
    static readonly MAKE_MONEY = "make_money";//赚钱

    static readonly DIAMOND_CHANGE = "diamond_changed";//钻石变化

}


