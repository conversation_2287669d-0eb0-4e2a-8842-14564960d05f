import { _decorator, Component, Node, Sprite, Label, instantiate, Prefab, log, profiler, director, ProgressBar, Button, Vec3 } from 'cc';
import { StatsContext } from './contexts/StatsContext';
import { AutomationContext } from './contexts/AutomationContext';
import { Interactable } from './items/interactables/Interactable';
import { Coin } from './items/interactables/Coin';
import { AudioManager } from './managers/AudioManager';
import { ConfigContext, PURCHASABLE_ITEM_ID } from './contexts/ConfigContext';
import { SaveContext } from './contexts/SaveContext';
import { GameEvent } from './event/GameEvent';
import { PurchaseContext } from './contexts/PurchaseContext';
import { UIManager } from './managers/UIManager';
import { TimeHelper } from './utils/TimeHelper';
import { Employee } from './items/interactables/Employee';
import { NumberHelper } from './utils/NumberHelper';

const { ccclass, property } = _decorator;

/**
 * 游戏主场景控制类
 */
@ccclass('Game')
export class Game extends Component {
    // 桌面精灵帧数组
    @property({ type: [Sprite] })
    tableSprites: Sprite[] = [];

    // 金钱标签
    @property(Label)
    labelMoney: Label = null!;

    // 钻石标签
    @property(Label)
    labelDiamond: Label = null!;

    // 赚钱目标进度条
    @property(ProgressBar)
    moneyProgressBar: ProgressBar = null!;
    // 赚钱目标进度条标签
    @property(Label)
    moneyProgressLabel: Label = null!;
    // 赚钱目标
    @property(Label)
    labelTarget: Label = null!;

    // 技能碎片任务
    @property(ProgressBar)
    targetProgressBar: ProgressBar = null!;
    @property(Label)
    labelProgress: Label = null!;

    // 金钱符号标签
    @property(Label)
    moneySymbolLabel: Label = null!

    // 小硬币预制体
    @property(Prefab)
    smallCoinPrefab: Prefab = null!;
    // 中硬币预制体
    @property(Prefab)
    mediumCoinPrefab: Prefab = null!;
    // 大硬币预制体
    @property(Prefab)
    largeCoinPrefab: Prefab = null!;

    // 交互物体容器
    @property(Node)
    interactablesContainer: Node = null!;

    // 熊预制体
    @property(Prefab)
    empolyeeBearPrefab: Prefab = null!;
    // 猫预制体
    @property(Prefab)
    empolyeeCatPrefab: Prefab = null!;
    // 鸡预制体
    @property(Prefab)
    empolyeeChookPrefab: Prefab = null!;
    // 牛预制体
    @property(Prefab)
    empolyeeCowPrefab: Prefab = null!;
    // 恐龙预制体
    @property(Prefab)
    empolyeeDinosaurPrefab: Prefab = null!;
    // 狗预制体
    @property(Prefab)
    empolyeeDogPrefab: Prefab = null!;
    // 青蛙预制体
    @property(Prefab)
    empolyeeFrogPrefab: Prefab = null!;
    // 马预制体
    @property(Prefab)
    empolyeeHorsePrefab: Prefab = null!;
    // 熊猫预制体
    @property(Prefab)
    empolyeePandaPrefab: Prefab = null!;
    // 企鹅预制体
    @property(Prefab)
    empolyeePenguinPrefab: Prefab = null!;
    // 兔预制体
    @property(Prefab)
    empolyeeRabbitPrefab: Prefab = null!;

    // 技能-临时增值
    @property(Node)
    skillTempValueAdd: Node = null!;
    // 技能-紧急动员
    @property(Node)
    skillEmergencyMobilization: Node = null!;
    // 技能-立即生产
    @property(Node)
    skillImmediateProduction: Node = null!;
    // 技能-高效工作
    @property(Node)
    skillHighEfficientWork: Node = null!;

    //欢迎界面
    @property(Node)
    nodeWelcome: Node = null!;

    // 可用的员工目标
    private availableHelperTargets: Interactable[] = [];

    // 存储游戏对象
    private smallCoins: Coin[] = [];
    private mediumCoins: Coin[] = [];
    private largeCoins: Coin[] = [];
    private employees: Employee[] = [];

    // 金钱显示格式
    private readonly MONEY_STRING_FORMAT: string = "{amount}";

    // 雇员数量
    private employeeCount: number = 0;

    start() {
        console.log("[Main] start() 开始执行。");

        profiler.showStats();

        // 播放背景音乐
        // AudioManager.playBGM("bgm_main");
        AudioManager.playBGM("bgm_grassland");

        // 注册自动化点击器
        AutomationContext.enableAutomation();

        // 初始化浮动文本池
        this.initFloatingTextPool();

        this.onInit();

        // 监听 StatsContext 的全局事件
        StatsContext.events.on(`SMALL_COIN-changed`, this.onSmallCoinsChanged, this);
        StatsContext.events.on(`MEDIUM_COIN-changed`, this.onMediumCoinsChanged, this);
        StatsContext.events.on(`LARGE_COIN-changed`, this.onLargeCoinsChanged, this);
        StatsContext.events.on(`CHANGE_SMALL_TO_MIDDLE-changed`, this.onSmall2MiddleChanged, this);
        StatsContext.events.on(`CHANGE_MIDDLE_TO_LARGE-changed`, this.onMiddle2LargeChanged, this);
        StatsContext.events.on(GameEvent.EMPLOYEE_CHANGED, this.onEmployeeChanged, this);
        StatsContext.events.on(GameEvent.SCENE_CHANGED, this.setScene, this);
        StatsContext.events.on(GameEvent.SKILL_LEVEL_CHANGED, this.onSkillLevelChanged, this);
        StatsContext.events.on(GameEvent.EMPLOYEE_EXITED, this.onEmployeeExited, this);
        StatsContext.events.on(GameEvent.RESET_GAME, this.onInit, this);
        StatsContext.events.on(GameEvent.DIAMOND_CHANGE, this.updateDiamondLabel, this);

        console.log("[Main] 已在 StatsContext.events 上注册事件监听器。");

        director.getScheduler().setTimeScale(1);

        //离线收益判断
        this.checkOfflineIncome();

        //称号
        this.udpateTitle();
    }

    onInit(reset: boolean = false) {
        //清除所硬币和雇员
        while (this.interactablesContainer.children.length > 0) {
            this.interactablesContainer.removeChild(this.interactablesContainer.children[0]);
        }

        this.smallCoins = [];
        this.mediumCoins = [];
        this.largeCoins = [];

        // 重置存档
        // SaveContext.resetSaveData();
        // StatsContext.resetToDefault();
        if (PurchaseContext.getPurchaseAmount(PURCHASABLE_ITEM_ID.SMALL_COIN) == 0) {
            StatsContext.setUpgrade(PURCHASABLE_ITEM_ID.SMALL_COIN, 1);
            StatsContext.purchases[PURCHASABLE_ITEM_ID.SMALL_COIN] = (StatsContext.purchases[PURCHASABLE_ITEM_ID.SMALL_COIN] || 0) + 1;
            SaveContext.saveGame();
        }
        // TODO 临时处理 =========== 
        // StatsContext.money = 100000;
        // StatsContext.setSkillFragment(PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION, 10);
        // StatsContext.setSkillFragment(PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD, 10);
        // StatsContext.setSkillFragment(PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION, 10);
        // StatsContext.setSkillFragment(PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK, 10);
        //=========================

        // 更新金钱显示
        this.updateMoneyLabel();

        // 输出所有统计信息 (初始数量)
        const initialSmallCoins = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.SMALL_COIN);//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.SMALL_COIN);
        const initialMediumCoins = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.CHANGE_SMALL_TO_MIDDLE);//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.MEDIUM_COIN);
        const initialLargeCoins = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.CHANGE_MIDDLE_TO_LARGE);//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.LARGE_COIN);
        const initialEmployeeBear = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_BEAR);
        const initialEmployeeCat = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_CAT);
        const initialEmployeeChook = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_CHOOK);
        const initialEmployeeCow = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_COW);//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.HELPER);
        const initialEmployeeDinosaur = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_DINOSAUR);
        const initialEmployeeDog = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_DOG);
        const initialEmployeeFrog = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_FROG);
        const initialEmployeeHorse = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_HORSE);
        const initialEmployeePanda = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_PANDA);
        const initialEmployeePenguin = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_PENGUIN);
        const initialEmployeeRabbit = StatsContext.getEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_RABBIT);

        const initialDie = 0;//StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.DICE);//StatsContext.getItemQuantity(PURCHASABLE_ITEM_ID.DICE);

        console.log(`[Main] 初始数量 - 小硬币: ${initialSmallCoins}, 中硬币: ${initialMediumCoins}, 大硬币: ${initialLargeCoins}, 员工: ${initialEmployeeCow}, 骰子: ${initialDie}`);

        // 根据统计信息生成初始对象
        if (reset) {

        } else {
            for (let i = 0; i < initialSmallCoins; i++) {
                this.spawnCoin("small");
            }
        }

        for (let i = 0; i < initialMediumCoins; i++) {
            this.spawnCoin("medium");
        }
        for (let i = 0; i < initialLargeCoins; i++) {
            this.spawnCoin("large");
        }
        
        if (initialEmployeeBear > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_BEAR)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_BEAR);//this.spawnHelper();
            }
        }
        if (initialEmployeeCat > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_CAT)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_CAT);//this.spawnHelper();
            }
        }
        if (initialEmployeeChook > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_CHOOK)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_CHOOK);//this.spawnHelper();
            }
        }
        if (initialEmployeeCow > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_COW)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_COW);//this.spawnHelper();
            }
        }
        if (initialEmployeeDinosaur > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_DINOSAUR)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_DINOSAUR);//this.spawnHelper();
            }
        }
        if (initialEmployeeDog > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_DOG)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_DOG);//this.spawnHelper();
            }
        }
        if (initialEmployeeFrog > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_FROG)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_FROG);//this.spawnHelper();
            }
        }
        if (initialEmployeeHorse > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_HORSE)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_HORSE);//this.spawnHelper();
            }
        }
        if (initialEmployeePanda > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_PANDA)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_PANDA);//this.spawnHelper();
            }
        }
        if (initialEmployeePenguin > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_PENGUIN)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_PENGUIN);//this.spawnHelper();
            }
        }
        if (initialEmployeeRabbit > 0) {
            this.employeeCount++;
            if (!this.checkEmployeeValidity(PURCHASABLE_ITEM_ID.EMPLOYEE_RABBIT)) {
                this.spawnEmployee(PURCHASABLE_ITEM_ID.EMPLOYEE_RABBIT);//this.spawnHelper();
            }
        }
        // for (let i = 0; i < initialDie; i++) {
        //     // 暂时不生成骰子
        //     this.spawnDice();
        // }

        // 定时更新金钱显示
        this.schedule(this.updateMoneyLabel, 0.2);

        // 更新钻石标签
        this.updateDiamondLabel();

        // 更新可用的员工目标
        this.updateAvailableHelpersTargets();

        this.setScene(StatsContext.currentScene.toLowerCase());

        //技能cd
        if (this.skillTempValueAdd) {
            this.skillTempValueAdd.active = StatsContext.getSkill(PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD) > 0;
        }
        if (this.skillEmergencyMobilization) {
            this.skillEmergencyMobilization.active = StatsContext.getSkill(PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION) > 0;
        }
        if (this.skillImmediateProduction) {
            this.skillImmediateProduction.active = StatsContext.getSkill(PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION) > 0;
        }
        if (this.skillHighEfficientWork) {
            this.skillHighEfficientWork.active = StatsContext.getSkill(PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK) > 0;
        }

        //欢迎界面
        if (this.nodeWelcome) this.nodeWelcome.active = true;
    }

    onDestroy() {
        // 移除 StatsContext 的全局事件监听
        StatsContext.events.off(`SMALL_COIN-changed`, this.onSmallCoinsChanged, this);
        StatsContext.events.off(`MEDIUM_COIN-changed`, this.onMediumCoinsChanged, this);
        StatsContext.events.off(`LARGE_COIN-changed`, this.onLargeCoinsChanged, this);
        StatsContext.events.off(`CHANGE_SMALL_TO_MIDDLE-changed`, this.onSmall2MiddleChanged, this);
        StatsContext.events.off(`CHANGE_MIDDLE_TO_LARGE-changed`, this.onMiddle2LargeChanged, this);
        StatsContext.events.off(GameEvent.EMPLOYEE_CHANGED, this.onEmployeeChanged, this);
        StatsContext.events.off(GameEvent.SCENE_CHANGED, this.setScene, this);
        StatsContext.events.off(GameEvent.SKILL_LEVEL_CHANGED, this.onSkillLevelChanged, this);
        StatsContext.events.off(GameEvent.EMPLOYEE_EXITED, this.onEmployeeExited, this);
        StatsContext.events.off(GameEvent.RESET_GAME, this.onInit, this);

        // 取消定时器
        this.unscheduleAllCallbacks();
    }

    /**
     * 连接交互物体的事件
     * @param interactable 交互物体
     */
    connectInteractable(interactable: Interactable) {
        interactable.value_gained.addEventListener('value_gained', (event: any) => {
            const value = event.detail.value as number;
            StatsContext.money += value;
            StatsContext.events.emit(GameEvent.MAKE_MONEY, value);
            // 触发保存 (可以考虑节流，避免过于频繁的保存)
            SaveContext.saveGame();
            this.udpateTitle();
        });
        // 每次有新的交互物体连接时，都可能影响员工的目标，所以更新一下
        this.updateAvailableHelpersTargets();
    }
    //称号 '$白手起家$','$万元户$','$小资$','$百万富翁$','$千万富翁$','$一个小目标$','$对钱不感兴趣$','$地方首富$','$全国首富$','$全球首富$','$宇宙首富$'
    udpateTitle(){
        let target = 100000000;//默认目标是一亿
        this.labelTarget.string = "1亿";
        if(StatsContext.money>=10000000000000 && StatsContext.money<100000000000000){//100000-1000000亿
            this.moneySymbolLabel.string = "宇宙首富";
            target = 100000000000000;//目标百万亿
            this.labelTarget.string = "百太";
        }else if(StatsContext.money>=1000000000000 && StatsContext.money<10000000000000){//10000-100000亿
            this.moneySymbolLabel.string = "全球首富";
            target = 10000000000000;//目标十万亿
            this.labelTarget.string = "十太";
        }else if(StatsContext.money>=100000000000 && StatsContext.money<1000000000000){//1000-10000亿
            this.moneySymbolLabel.string = "全国首富";
            target = 1000000000000;//目标万亿
            this.labelTarget.string = "1太";
        }else if(StatsContext.money>=10000000000 && StatsContext.money<100000000000){//100-1000亿
            this.moneySymbolLabel.string = "地方首富";
            target = 100000000000;//目标千亿
            this.labelTarget.string = "千亿";
        }else if(StatsContext.money>=1000000000 && StatsContext.money<10000000000){//10-100亿
            this.moneySymbolLabel.string = "对钱不感兴趣";
            target = 10000000000;//目标百亿
            this.labelTarget.string = "百亿";
        }else if(StatsContext.money>=100000000 && StatsContext.money<1000000000){//1-10亿
            this.moneySymbolLabel.string = "一个小目标";
            target = 1000000000;//目标十亿
            this.labelTarget.string = "十亿";
        }else if(StatsContext.money>=10000000 && StatsContext.money<100000000){//1000w-1亿
            this.moneySymbolLabel.string = "千万富翁";
        }else if(StatsContext.money>=1000000 && StatsContext.money<10000000){//100w-1000w
            this.moneySymbolLabel.string = "百万富翁";
        }else if(StatsContext.money>=100000 && StatsContext.money<1000000){//10w-100w
            this.moneySymbolLabel.string = "小资";
        }else if(StatsContext.money>=10000 && StatsContext.money<100000){//1w-10w
            this.moneySymbolLabel.string = "万元户";
        }else if(StatsContext.money<10000){
            this.moneySymbolLabel.string = "白手起家";
        } 
        const percent = StatsContext.money/target;
        this.moneyProgressLabel.string = Math.floor(percent*100)+"%";
        this.moneyProgressBar.progress = percent;
    }

    /**
     * 更新可用的员工目标
     */
    updateAvailableHelpersTargets() {
        this.availableHelperTargets = [];

        // 添加小硬币
        this.availableHelperTargets.push(...this.smallCoins.filter(c => c && c.isValid)); // 确保组件有效

        // 如果员工可以触发中硬币
        if (StatsContext.isFeatureUnlocked(PURCHASABLE_ITEM_ID.HELPER_TRIGGER_MEDIUM_COINS)) {
            this.availableHelperTargets.push(...this.mediumCoins.filter(c => c && c.isValid));
        }

        // 如果员工可以触发大硬币
        if (StatsContext.isFeatureUnlocked(PURCHASABLE_ITEM_ID.HELPER_TRIGGER_LARGE_COINS)) {
            this.availableHelperTargets.push(...this.largeCoins.filter(c => c && c.isValid));
        }

        for (const empolyee of this.employees.filter(h => h && h.isValid)) {
            empolyee.available_targets = this.availableHelperTargets;
        }
        console.log(`[Main] 更新员工可用目标，当前总目标数: ${this.availableHelperTargets.length}`);
    }

    /**
     * 更新金钱标签
     */
    updateMoneyLabel() {
        if (this.labelMoney) {
            this.labelMoney.string = NumberHelper.convert_number_to_text(StatsContext.money);
        }
        this.updateSkillFragmentTaskTarget();
    }

    /**
     * 更新钻石标签
     */
    updateDiamondLabel() {
        if (this.labelDiamond) {
            this.labelDiamond.string = NumberHelper.convert_number_to_text(StatsContext.diamond);
        }
    }


    /**
     * 更新任务目标
     */
    updateSkillFragmentTaskTarget() {
        const tasek_level = StatsContext.getSkillFragmentTask(PURCHASABLE_ITEM_ID.TASK_COIN);
        const taskMoney = tasek_level < StatsContext.skillFragmentTaskData.length ? StatsContext.skillFragmentTaskData[tasek_level] : StatsContext.skillFragmentTaskData[StatsContext.skillFragmentTaskData.length - 1];

        // if (this.targetLabel) {
        //     this.targetLabel.string = this.formatNumber(taskMoney);
        // }

        // let money = 0;
        // for(let i=1;i<tasek_level;i++){
        //     money += i*2+1;
        // }
        let curMoney = StatsContext.totalMoney - StatsContext.taskCompleteMoney;
        if (curMoney < 0) {
            curMoney = 0;
        }
        if (this.targetProgressBar) {
            this.targetProgressBar.progress = curMoney / taskMoney;
        }

        const taskProgress = Math.floor(curMoney*100 / taskMoney);

        this.labelProgress.string = taskProgress + "%";

        if (curMoney >= taskMoney) {
            //达成目标
            console.log(`[Main] 达成目标-前 StatsContext.taskCompleteMoney = : ${StatsContext.taskCompleteMoney}`);
            StatsContext.taskCompleteMoney += taskMoney;
            console.log(`[Main] 达成目标-后 StatsContext.taskCompleteMoney = : ${StatsContext.taskCompleteMoney}, taskMoney = ${taskMoney}`);
            StatsContext.setSkillFragmentTask(PURCHASABLE_ITEM_ID.TASK_COIN, tasek_level + 1);
            UIManager.instance.showTaskComplete();
            SaveContext.saveGame();
        }
    }

    /**
     * 格式化数字为易读的字符串
     * @param num 需要格式化的数字
     * @returns 格式化后的字符串
     */
    formatNumber(num: number): string {
        return num.toLocaleString();
    }

    /**
     * 初始化浮动文本池
     */
    initFloatingTextPool() {
        // 在Cocos Creator中，我们可以使用节点池来管理浮动文本
        // 这里简化处理，直接使用FloatingText类的静态方法创建
    }

    /**
     * 生成硬币
     * @param type 硬币类型 ("small", "medium", "large")
     */
    spawnCoin(type: string) {
        let prefab: Prefab | null = null;
        const lowerCaseType = type.toLowerCase();

        switch (lowerCaseType) {
            case 'small': prefab = this.smallCoinPrefab; break;
            case 'medium': prefab = this.mediumCoinPrefab; break;
            case 'large': prefab = this.largeCoinPrefab; break;
            default:
                console.log(`[Main] 未知的硬币类型: ${type}`);
                return;
        }

        if (!prefab) {
            console.log(`[Main] 缺少预制体: ${type} 硬币`);
            return;
        }

        const coinNode = instantiate(prefab);
        const coinComponent = coinNode.getComponent(Coin);

        if (coinComponent) {
            switch (lowerCaseType) {
                case 'small': this.smallCoins.push(coinComponent); break;
                case 'medium': this.mediumCoins.push(coinComponent); break;
                case 'large': this.largeCoins.push(coinComponent); break;
            }
            this.interactablesContainer.addChild(coinNode);
            // coinComponent.node.emit('set_coin_type', lowerCaseType); // Coin.ts 现在通过 @property type 设置
            this.connectInteractable(coinComponent);
            console.log(`[Main] 生成了一个 ${type} 硬币。`);
        } else {
            console.log(`[Main] ${type} 硬币预制体上没有 Coin 组件。`);
            coinNode.destroy();
        }
    }

    /**
     * 设置硬币背景模式
     * @param mode 模式名称
     */
    setCoinBackgroundMode(mode: string) {
        // TODO: 根据模式名称设置硬币背景
    }

    /**
     * 商店售出物品事件处理 (这个方法似乎与当前上下文不直接相关，但保留)
     */
    onShopSoldSomething() {
        // StatsContext.instance.consumeCoins(100); // 这个方法在 StatsContext 中未定义
        log("[Main] onShopSoldSomething 被调用，但 StatsContext.instance.consumeCoins 未实现。");
        SaveContext.saveGame();
    }

    /**
     * 选项按钮点击事件
     */
    onOptionsButtonPressed() {
        // TODO: 显示选项菜单
    }

    /**
     * 小硬币数量变化处理
     */
    onSmallCoinsChanged(newValue: number, oldValue: number) {
        console.log(`[Main] 小硬币数量变化: 旧=${oldValue}, 新=${newValue}`);
        const quantityToAdd = newValue - oldValue;
        if (quantityToAdd > 0) {
            for (let i = 0; i < quantityToAdd; i++) {
                this.spawnCoin("small");
            }
        }
        // 数量变化可能影响员工目标
        this.updateAvailableHelpersTargets();
    }

    /**
     * 中硬币数量变化处理
     */
    onMediumCoinsChanged(newValue: number, oldValue: number) {
        console.log(`[Main] 中硬币数量变化: 旧=${oldValue}, 新=${newValue}`);
        const quantityToAdd = newValue - oldValue;
        if (quantityToAdd > 0) {
            for (let i = 0; i < quantityToAdd; i++) {
                this.spawnCoin("medium");
            }
        }
        this.updateAvailableHelpersTargets();
    }

    /**
     * 大硬币数量变化处理
     */
    onLargeCoinsChanged(newValue: number, oldValue: number) {
        console.log(`[Main] 大硬币数量变化: 旧=${oldValue}, 新=${newValue}`);
        const quantityToAdd = newValue - oldValue;
        if (quantityToAdd > 0) {
            for (let i = 0; i < quantityToAdd; i++) {
                this.spawnCoin("large");
            }
        }
        this.updateAvailableHelpersTargets();
    }


    /**
     * 小硬币到中硬币的转换处理
     */
    onSmall2MiddleChanged(newValue: number, oldValue: number) {
        console.log(`[Main] 小硬币到中硬币的转换数量: ${newValue - oldValue}`);
        // TODO: 处理转换
        //取出第一个铜币
        const firstSmallCoin = this.smallCoins.shift();
        if (!firstSmallCoin) {
            console.error("[Main] 没有可用的小硬币进行转换");
            return;
        }
        //生成一个银币并移动到删除铜币的位置、播放一次动画
        const coinNode = instantiate(this.mediumCoinPrefab);
        if (!coinNode) {
            console.error("[Main] 中硬币预制体不存在");
            return;
        }
        const coinComponent = coinNode.getComponent(Coin);
        if (coinComponent) {
            this.mediumCoins.push(coinComponent);
            this.interactablesContainer.addChild(coinNode);
            this.connectInteractable(coinComponent);
            // 移动到铜币位置
            coinNode.setPosition(firstSmallCoin.node.getPosition());

            //从显示列表中移除
            this.interactablesContainer.removeChild(firstSmallCoin.node);
            firstSmallCoin.node.getComponent(Coin).onDestroy();
            // 播放动画
            coinComponent.on_mouse_clicked();
            console.log(`[Main] 一个铜币转换成了银币。`);
        } else {
            console.log(`[Main] middle硬币预制体上没有 Coin 组件。`);
            coinNode.destroy();
        }

        // 更新可用的员工目标
        this.updateAvailableHelpersTargets();
    }
    /**
     * 中硬币到大硬币的转换处理
     */
    onMiddle2LargeChanged(newValue: number, oldValue: number) {
        console.log(`[Main] 小硬币到中硬币的转换数量: ${newValue - oldValue}`);
        // TODO: 处理转换
        //取出第一个铜币
        const firstMiddleCoin = this.mediumCoins.shift();
        if (!firstMiddleCoin) {
            console.error("[Main] 没有可用的小硬币进行转换");
            return;
        }
        //生成一个银币并移动到删除铜币的位置、播放一次动画
        const coinNode = instantiate(this.largeCoinPrefab);
        if (!coinNode) {
            console.error("[Main] 中硬币预制体不存在");
            return;
        }
        const coinComponent = coinNode.getComponent(Coin);
        if (coinComponent) {
            this.largeCoins.push(coinComponent);
            this.interactablesContainer.addChild(coinNode);
            this.connectInteractable(coinComponent);
            // 移动到铜币位置
            coinNode.setPosition(firstMiddleCoin.node.getPosition());

            //从显示列表中移除
            this.interactablesContainer.removeChild(firstMiddleCoin.node);
            firstMiddleCoin.node.getComponent(Coin).onDestroy();
            // 播放动画
            coinComponent.on_mouse_clicked();
            console.log(`[Main] 一个银币转换成了金币。`);
        } else {
            console.log(`[Main] large硬币预制体上没有 Coin 组件。`);
            coinNode.destroy();
        }

        // 更新可用的员工目标
        this.updateAvailableHelpersTargets();
    }

    /**
     * 雇员数量变化处理
     */
    onEmployeeChanged(key: string, newValue: number, oldValue: number) {
        console.log(`[Main] 雇员数量变化: 旧=${oldValue}, 新=${newValue}`);
        // const quantityToAdd = newValue - oldValue;
        // if (quantityToAdd > 0) {
        //     for (let i = 0; i < quantityToAdd; i++) {
        //         this.spawnEmployee(key);
        //     }
        // }
        if (newValue != oldValue && newValue > 0) {
            this.spawnEmployee(key, true);
            this.updateAvailableHelpersTargets();
        }

    }
    /**
     * 生成雇员
     * @param type 雇员类型
     */
    spawnEmployee(type: string, needEnter: boolean = false) {
        let prefab: Prefab | null = null;
        const lowerCaseType = type.toLowerCase();

        switch (lowerCaseType) {
            case 'employee_bear': prefab = this.empolyeeBearPrefab; break;
            case 'employee_cat': prefab = this.empolyeeCatPrefab; break;
            case 'employee_chook': prefab = this.empolyeeChookPrefab; break;
            case 'employee_cow': prefab = this.empolyeeCowPrefab; break;
            case 'employee_dinosaur': prefab = this.empolyeeDinosaurPrefab; break;
            case 'employee_dog': prefab = this.empolyeeDogPrefab; break;
            case 'employee_frog': prefab = this.empolyeeFrogPrefab; break;
            case 'employee_horse': prefab = this.empolyeeHorsePrefab; break;
            case 'employee_panda': prefab = this.empolyeePandaPrefab; break;
            case 'employee_penguin': prefab = this.empolyeePenguinPrefab; break;
            case 'employee_rabbit': prefab = this.empolyeeRabbitPrefab; break;
            default:
                console.log(`[Main] 未知的硬币类型: ${type}`);
                return;
        }

        if (!prefab) {
            console.log(`[Main] 缺少预制体: ${type} 硬币`);
            return;
        }

        const employeeNode = instantiate(prefab);
        const employeeComponent = employeeNode.getComponent(Employee);
        if (employeeComponent) {
            this.employees.push(employeeComponent);
            this.interactablesContainer.addChild(employeeNode);
            employeeComponent.available_targets = this.availableHelperTargets; // 在添加后立即设置一次
            employeeComponent.onEnter(needEnter);
            console.log("[Main] 生成了一个员工。");
            this.updateAvailableHelpersTargets(); // 新员工加入，可能需要更新所有员工的列表（虽然单个设置也行）
        } else {
            console.log("[Main] 员工预制体上没有 Helper 组件。");
            employeeNode.destroy();
        }
    }

    /**
     * 场地发生变化
     */
    setScene(key: string) {
        console.log(`[Main] 场景变化: 新=${key}`);
        // 遍历所有桌精灵
        for (let tableSprite of this.tableSprites) {
            // 检查是否是当前场景的桌精灵
            if (tableSprite.name.indexOf(key.toLowerCase()) > -1) {
                // 激活当前场景桌精灵
                tableSprite.node.active = true;
            } else {
                // 非当前场景桌精灵，隐藏
                tableSprite.node.active = false;
            }
        }
        switch (key.toLowerCase()) {
            case PURCHASABLE_ITEM_ID.SCENE_GRASSLAND:
                AudioManager.playBGM("bgm_grassland");
                break;
            case PURCHASABLE_ITEM_ID.SCENE_SEASIDE:
                AudioManager.playBGM("bgm_seaside");
                break;
            case PURCHASABLE_ITEM_ID.SCENE_BASKETBALL_COURT:
                AudioManager.playBGM("bgm_basketball_court");
                break;
            case PURCHASABLE_ITEM_ID.SCENE_WAREHOUSE:
                AudioManager.playBGM("bgm_warehouse");
                break;
        }
    }

    onSkillLevelChanged(key: string, value: number, oldValue: number) {
        if (oldValue == 0) {
            switch (key) {
                case PURCHASABLE_ITEM_ID.SKILL_IMMEDIATE_PRODUCTION:
                    this.skillImmediateProduction.active = true;
                    break;
                case PURCHASABLE_ITEM_ID.SKILL_TEMP_VALUE_ADD:
                    this.skillTempValueAdd.active = true;
                    break;
                case PURCHASABLE_ITEM_ID.SKILL_EMERGENCY_MOBILIZATION:
                    this.skillEmergencyMobilization.active = true;
                    break;
                case PURCHASABLE_ITEM_ID.SKILL_HIGH_EFFICIENT_WORK:
                    this.skillHighEfficientWork.active = true;
                    break;
            }
        }
    }
    onSettingButtonClick() {
        AudioManager.playSoundEffect('button_open');
        UIManager.instance.showSetting();
    }

    onEmployeeExited(key: string) {
        console.log(`[Main] 雇员过期: ${key}`);
        for (let i = 0; i < this.employees.length; i++) {
            if (this.employees[i].employeeID == key) {
                this.interactablesContainer.removeChild(this.employees[i].node);
                this.employees[i].node.destroy();
                this.employees.splice(i, 1);
                StatsContext.events.emit(GameEvent.EMPLOYEE_EXIT_COMPLETE, key);
                break;
            }
        }
    }

    onRankButtonClick() {
        AudioManager.playSoundEffect('button_open');
        UIManager.instance.showRank();
    }

    checkOfflineIncome() {
        if (StatsContext.recordTime != 0) {
            //当前时间戳
            let recordTime = TimeHelper.getCurrentTimestamp();
            if (recordTime > StatsContext.recordTime) {
                //计算离线收益

                let smallCoinCount = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.SMALL_COIN);
                let mediumCoinCount = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.CHANGE_SMALL_TO_MIDDLE);
                let largeCoinCount = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.CHANGE_MIDDLE_TO_LARGE);

                let baseValueSmall = ConfigContext.getBaseValue("small_coin");
                let baseValueMiddle = ConfigContext.getBaseValue("medium_coin");
                let baseValueLarge = ConfigContext.getBaseValue("large_coin");

                let additionalCoinValueSmall = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_SMALL);; // 额外奖励值
                let additionalCoinValueMiddle = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_MEDIUM);; // 额外奖励值
                let additionalCoinValueLarge = StatsContext.getUpgrade(PURCHASABLE_ITEM_ID.ADDITIONAL_COIN_VALUE_LARGE);; // 额外奖励值

                //离线的毫秒数
                let offlineTime = recordTime - StatsContext.recordTime;
                //离线的分钟数
                let offlineTimeMinute = offlineTime / 1000 / 60;

                //员工每分钟通过铜币产出
                let smallCoinMoney = this.employeeCount * (smallCoinCount * (baseValueSmall + additionalCoinValueSmall)) * offlineTimeMinute;
                //员工每分钟通过银币产出
                let mediumCoinMoney = this.employeeCount * (mediumCoinCount * (baseValueMiddle + additionalCoinValueMiddle)) * offlineTimeMinute;
                //员工每分钟通过金币产出
                let largeCoinMoney = this.employeeCount * (largeCoinCount * (baseValueLarge + additionalCoinValueLarge)) * offlineTimeMinute;

                let offlineMoney = Math.floor(smallCoinMoney + mediumCoinMoney + largeCoinMoney);
                if (offlineMoney > 0) {
                    //弹出离线收益界面
                    UIManager.instance.showOfflineIncomeUI(offlineMoney);
                }
            }
        }
    }

    //检测员工是否过期
    checkEmployeeValidity(idkey:string, ):boolean{
        let config = ConfigContext.getPurchasableItemConfig(idkey.toLowerCase() as PURCHASABLE_ITEM_ID);
        if (!config) {
            console.warn(`[Game] 配置不存在: ${idkey} (尝试使用 ${idkey.toLowerCase()})`);
            return true;
        }
        if(config.expiration_data==0){
            //永久有效,不检查
            return false;
        }

        const buyTime = StatsContext.getEmployee(idkey);
        const curTime = TimeHelper.getCurrentTimestampInSeconds();
        const useTime = curTime - buyTime;//使用了多长时间
        const validity_time = config.expiration_data*60*60;//单位秒，this.config.expiration_data配置的是天数
        if(useTime>=validity_time){
            //已过期
            return true;
        }

        //未过期
        return false;
    }

    showTargetTips(){
        UIManager.instance.showTargetTips();
    }

}