import { _decorator } from 'cc';

const { ccclass } = _decorator;

/**
 * 版本配置类
 * 定义客户端版本管理的配置信息
 */
@ccclass('VersionConfig')
export class VersionConfig {
    
    /**
     * 当前客户端版本
     */
    public static readonly CLIENT_VERSION = '0.1.2';

    /**
     * 构建号
     */
    public static readonly BUILD_NUMBER = 1;

    /**
     * 版本检查配置
     */
    public static readonly VERSION_CHECK = {
        // 是否启用版本检查
        enabled: true,
        
        // 检查间隔（秒）
        interval: 300,
        
        // 服务器URL配置
        serverUrl: {
            // 开发环境
            development: 'https://dev-api.luckycoin.com/version',
            
            // 测试环境
            staging: 'https://staging-api.luckycoin.com/version',
            
            // 生产环境
            production: 'https://api.luckycoin.com/version'
        },
        
        // 当前环境
        currentEnv: 'development' as 'development' | 'staging' | 'production',
        
        // 请求超时时间（毫秒）
        timeout: 10000,
        
        // 重试次数
        retryCount: 3,
        
        // 重试间隔（毫秒）
        retryInterval: 2000
    };

    /**
     * 热更新配置
     */
    public static readonly HOT_UPDATE = {
        // 是否启用热更新
        enabled: true,
        
        // 资源服务器URL
        resourceServerUrl: {
            development: 'https://dev-cdn.luckycoin.com/resources',
            staging: 'https://staging-cdn.luckycoin.com/resources',
            production: 'https://cdn.luckycoin.com/resources'
        },
        
        // 下载超时时间（毫秒）
        downloadTimeout: 30000,
        
        // 并发下载数量
        concurrentDownloads: 3,
        
        // 最大缓存大小（MB）
        maxCacheSize: 100,
        
        // 缓存清理策略
        cacheCleanup: {
            // 保留版本数量
            keepVersions: 3,
            
            // 自动清理间隔（小时）
            autoCleanInterval: 24
        }
    };

    /**
     * 资源加载配置
     */
    public static readonly RESOURCE_LOADING = {
        // 是否启用版本化资源加载
        enableVersionedLoading: true,
        
        // 是否使用缓存
        useCache: true,
        
        // 缓存过期时间（小时）
        cacheExpireTime: 24,
        
        // 是否回退到本地资源
        fallbackToLocal: true,
        
        // 预加载资源列表
        preloadResources: [
            'textures/ui/common',
            'audio/bgm/main',
            'configs/game_config'
        ],
        
        // 关键资源列表（必须成功加载）
        criticalResources: [
            'configs/game_config',
            'textures/ui/loading'
        ]
    };

    /**
     * 调试配置
     */
    public static readonly DEBUG = {
        // 是否启用调试日志
        enableLog: true,
        
        // 日志级别
        logLevel: 'info' as 'debug' | 'info' | 'warn' | 'error',
        
        // 是否显示版本信息
        showVersionInfo: true,
        
        // 是否模拟更新
        simulateUpdate: false,
        
        // 模拟更新延迟（毫秒）
        simulateDelay: 2000
    };

    /**
     * 获取当前环境的服务器URL
     */
    public static getVersionServerUrl(): string {
        return this.VERSION_CHECK.serverUrl[this.VERSION_CHECK.currentEnv];
    }

    /**
     * 获取当前环境的资源服务器URL
     */
    public static getResourceServerUrl(): string {
        return this.HOT_UPDATE.resourceServerUrl[this.VERSION_CHECK.currentEnv];
    }

    /**
     * 获取完整版本信息
     */
    public static getVersionInfo(): {
        version: string;
        buildNumber: number;
        environment: string;
        timestamp: number;
    } {
        return {
            version: this.CLIENT_VERSION,
            buildNumber: this.BUILD_NUMBER,
            environment: this.VERSION_CHECK.currentEnv,
            timestamp: Date.now()
        };
    }

    /**
     * 检查是否为开发环境
     */
    public static isDevelopment(): boolean {
        return this.VERSION_CHECK.currentEnv === 'development';
    }

    /**
     * 检查是否为生产环境
     */
    public static isProduction(): boolean {
        return this.VERSION_CHECK.currentEnv === 'production';
    }

    /**
     * 设置环境
     */
    public static setEnvironment(env: 'development' | 'staging' | 'production'): void {
        this.VERSION_CHECK.currentEnv = env;
        console.log(`[VersionConfig] 环境已切换到: ${env}`);
    }

    /**
     * 获取平台特定配置
     */
    public static getPlatformConfig(): {
        platform: string;
        supportHotUpdate: boolean;
        supportLocalStorage: boolean;
    } {
        const platform = cc.sys.platform;
        
        return {
            platform: cc.sys.platformToString(platform),
            supportHotUpdate: platform !== cc.sys.WECHAT_GAME, // 微信小游戏暂不支持热更新
            supportLocalStorage: platform !== cc.sys.WECHAT_GAME
        };
    }

    /**
     * 验证配置
     */
    public static validateConfig(): boolean {
        try {
            // 检查必要的URL配置
            if (!this.getVersionServerUrl()) {
                console.error('[VersionConfig] 版本服务器URL未配置');
                return false;
            }

            if (!this.getResourceServerUrl()) {
                console.error('[VersionConfig] 资源服务器URL未配置');
                return false;
            }

            // 检查版本号格式
            const versionRegex = /^\d+\.\d+\.\d+$/;
            if (!versionRegex.test(this.CLIENT_VERSION)) {
                console.error('[VersionConfig] 版本号格式错误:', this.CLIENT_VERSION);
                return false;
            }

            console.log('[VersionConfig] 配置验证通过');
            return true;

        } catch (error) {
            console.error('[VersionConfig] 配置验证失败:', error);
            return false;
        }
    }

    /**
     * 打印配置信息
     */
    public static printConfig(): void {
        if (!this.DEBUG.enableLog) return;

        console.log('[VersionConfig] 当前配置:');
        console.log('  版本:', this.CLIENT_VERSION);
        console.log('  构建号:', this.BUILD_NUMBER);
        console.log('  环境:', this.VERSION_CHECK.currentEnv);
        console.log('  版本服务器:', this.getVersionServerUrl());
        console.log('  资源服务器:', this.getResourceServerUrl());
        console.log('  平台信息:', this.getPlatformConfig());
    }
}
