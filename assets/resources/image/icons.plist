<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>menu_active.png</key>
            <dict>
                <key>frame</key>
                <string>{{1030,2},{70,76}}</string>
                <key>offset</key>
                <string>{0,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,4},{70,76}}</string>
                <key>sourceSize</key>
                <string>{80,80}</string>
            </dict>
            <key>menu_helper.png</key>
            <dict>
                <key>frame</key>
                <string>{{1096,80},{52,66}}</string>
                <key>offset</key>
                <string>{0,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{14,10},{52,66}}</string>
                <key>sourceSize</key>
                <string>{80,80}</string>
            </dict>
            <key>menu_passive.png</key>
            <dict>
                <key>frame</key>
                <string>{{1030,80},{64,70}}</string>
                <key>offset</key>
                <string>{0,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{8,7},{64,70}}</string>
                <key>sourceSize</key>
                <string>{80,80}</string>
            </dict>
            <key>menu_table.png</key>
            <dict>
                <key>frame</key>
                <string>{{1030,152},{60,66}}</string>
                <key>offset</key>
                <string>{1,-3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{11,10},{60,66}}</string>
                <key>sourceSize</key>
                <string>{80,80}</string>
            </dict>
            <key>skill_active_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{246,128},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_active_1_s.png</key>
            <dict>
                <key>frame</key>
                <string>{{430,190},{60,60}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{60,60}}</string>
                <key>sourceSize</key>
                <string>{60,60}</string>
            </dict>
            <key>skill_active_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{124,128},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_active_2_s.png</key>
            <dict>
                <key>frame</key>
                <string>{{368,190},{60,60}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{60,60}}</string>
                <key>sourceSize</key>
                <string>{60,60}</string>
            </dict>
            <key>skill_active_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,128},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_active_3_s.png</key>
            <dict>
                <key>frame</key>
                <string>{{430,128},{60,60}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{60,60}}</string>
                <key>sourceSize</key>
                <string>{60,60}</string>
            </dict>
            <key>skill_active_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{908,124},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_active_4_s.png</key>
            <dict>
                <key>frame</key>
                <string>{{368,128},{60,60}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{60,60}}</string>
                <key>sourceSize</key>
                <string>{60,60}</string>
            </dict>
            <key>skill_passive_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{786,124},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_passive_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{664,124},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_passive_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{542,124},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_passive_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{908,2},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_passive_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{786,2},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_passive_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{664,2},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>skill_passive_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{542,2},{120,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,120}}</string>
                <key>sourceSize</key>
                <string>{120,120}</string>
            </dict>
            <key>table_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{407,2},{124,133}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{124,133}}</string>
                <key>sourceSize</key>
                <string>{124,133}</string>
            </dict>
            <key>table_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{272,2},{124,133}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{124,133}}</string>
                <key>sourceSize</key>
                <string>{124,133}</string>
            </dict>
            <key>table_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{137,2},{124,133}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{124,133}}</string>
                <key>sourceSize</key>
                <string>{124,133}</string>
            </dict>
            <key>table_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{124,133}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{124,133}}</string>
                <key>sourceSize</key>
                <string>{124,133}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>icons.png</string>
            <key>size</key>
            <string>{1150,252}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:0622a516433c652b8641f204ebcb0b84:832fe9e33c4eb659fb946447544ee801:a73c72aca107c42120aa7eba166486e5$</string>
            <key>textureFileName</key>
            <string>icons.png</string>
        </dict>
    </dict>
</plist>
