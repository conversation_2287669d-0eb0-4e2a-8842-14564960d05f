{"additional_coin_value_multiplier_large": {"name": "金币注入", "desc": "金币获得+{increase_amount}%更多币，显著提高其价值。", "category": "upgrades", "requirement_to_buy": "large_coins >= 5", "requirement_to_see": "large_coins >= 3", "increase_amounts": 0.2, "max_purchase_amounts": 20, "price_expressions": {"base": 1.9, "multiplier": 600, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "additional_coin_value_multiplier_medium": {"name": "银币嬗变", "desc": "银币获得+{increase_amount}%更多币，显著提高其价值。", "category": "upgrades", "requirement_to_buy": "medium_coins >= 10", "requirement_to_see": "medium_coins >= 5", "increase_amounts": 0.2, "max_purchase_amounts": 20, "price_expressions": {"base": 1.9, "multiplier": 150, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "additional_coin_value_multiplier_small": {"name": "铜币炼金", "desc": "铜币获得+{increase_amount}%更多币，显著提高其价值。", "category": "upgrades", "requirement_to_buy": "small_coins >= 20", "requirement_to_see": "small_coins >= 10", "increase_amounts": 0.2, "max_purchase_amounts": 20, "price_expressions": {"base": 1.9, "multiplier": 30, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "coins_trigger_coins": {"name": "硬币震荡", "desc": "金币和银币在落地时会触发较小的硬币，创造连锁翻转和收益。", "category": "upgrades", "requirement_to_buy": "large_coins > 0 && medium_coins > 0", "requirement_to_see": "large_coins > 0", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 0, "multiplier": 0, "flat_offset": 25000}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "coin_flip_speed_large": {"name": "金币速度", "desc": "将金币的翻转速度提高{increase_amount}%。", "category": "upgrades", "requirement_to_buy": "large_coins >= 5", "requirement_to_see": "large_coins >= 3", "increase_amounts": 0.2, "max_purchase_amounts": 20, "price_expressions": {"base": 1.5, "multiplier": 4000, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "coin_flip_speed_medium": {"name": "银币迅捷", "desc": "将银币的翻转速度提高{increase_amount}%。", "category": "upgrades", "requirement_to_buy": "medium_coins >= 10", "requirement_to_see": "medium_coins >= 5", "increase_amounts": 0.15, "max_purchase_amounts": 20, "price_expressions": {"base": 1.5, "multiplier": 1500, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "coin_flip_speed_small": {"name": "铜币快速", "desc": "将铜币的翻转速度提高{increase_amount}%。", "requirement_to_buy": "small_coins >= 20", "requirement_to_see": "small_coins >= 10", "increase_amounts": 0.1, "max_purchase_amounts": 20, "price_expressions": {"base": 1.5, "multiplier": 500, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "helper_efficiency": {"name": "高效员工", "desc": "员工效率提高{increase_amount}%，减少等待时间并加快移动。", "category": "upgrades", "requirement_to_buy": "helpers > 0", "requirement_to_see": "helpers > 0", "increase_amounts": 0.15, "max_purchase_amounts": 20, "price_expressions": {"base": 1.9, "multiplier": 30, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "helper_trigger_large_coins": {"name": "肌肉员工", "desc": "员工现在可以翻转金币，轻松处理最重的硬币。", "category": "upgrades", "requirement_to_buy": "large_coins > 0 && helpers > 0", "requirement_to_see": "large_coins > 0 && helpers > 0", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 0, "multiplier": 0, "flat_offset": 6000}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "helper_trigger_medium_coins": {"name": "灵巧之手", "desc": "员工现在可以翻转银币，扩展其能力和您的收益。", "category": "upgrades", "requirement_to_buy": "medium_coins > 0 && helpers > 0", "requirement_to_see": "medium_coins > 0 && helpers > 0", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 0, "multiplier": 0, "flat_offset": 600}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "hover_small_coins": {"name": "快速翻转", "desc": "只需将鼠标悬停在铜币上即可翻转，无需点击。", "category": "upgrades", "requirement_to_buy": "small_coins >= 50", "requirement_to_see": "small_coins >= 30", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 0, "multiplier": 0, "flat_offset": 1000}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "reflip_chance": {"name": "幸运回响", "desc": "获得+{increase_amount}%几率自动再次翻转硬币。", "category": "upgrades", "requirement_to_buy": "medium_coins > 0", "requirement_to_see": "medium_coins > 0", "increase_amounts": 0.05, "max_purchase_amounts": 15, "price_expressions": {"base": 1.9, "multiplier": 150, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "small_coin": {"name": "铜币", "desc": "增加铜币，铜币每次成功翻转到‘$’面，获得 1$", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 20, "price_expressions": {"base": 1.3, "multiplier": 2, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_7", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "medium_coin": {"name": "银币", "desc": "每次成功翻转到反面获得{increase_amount}币。一种更闪亮的方式来提高您的收入。", "category": "items", "requirement_to_buy": "small_coins >= 10", "requirement_to_see": "small_coins >= 5", "increase_amounts": 1.0, "max_purchase_amounts": 50, "price_expressions": {"base": 1.3, "multiplier": 200, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "large_coin": {"name": "金币", "desc": "每次成功翻转到反面获得{increase_amount}币。顶级收益的终极硬币。", "category": "items", "requirement_to_buy": "medium_coins >= 5", "requirement_to_see": "medium_coins >= 3", "increase_amounts": 1.0, "max_purchase_amounts": 30, "price_expressions": {"base": 1.3, "multiplier": 5000, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "helper": {"name": "员工", "desc": "自动激活铜币，使赚钱变得轻松。", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 30, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "change_small_to_middle": {"name": "转铜为银", "desc": "将一枚铜市升级为银币，银币每次成功翻转到$面，获得20$。", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 20, "price_expressions": {"base": 1.45, "multiplier": 20, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_6", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "change_middle_to_large": {"name": "点银成金", "desc": "将一枚银币升级为金币，金币每次成功翻转到'$'面，获得300$。", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 20, "price_expressions": {"base": 1.45, "multiplier": 300, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_4", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "additional_coin_value_small": {"name": "铜币增值", "desc": "铜币每次翻转额外获得$币。", "category": "upgrades", "requirement_to_buy": "small_coin > 0", "requirement_to_see": "small_coin > 0", "increase_amounts": 2.0, "max_purchase_amounts": 40, "price_expressions": {"base": 1.3, "multiplier": 2, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "additional_coin_value_medium": {"name": "银币增值", "desc": "银币每次翻转额外获得$币。", "category": "upgrades", "requirement_to_buy": "medium_coin > 0", "requirement_to_see": "medium_coin > 0", "increase_amounts": 20.0, "max_purchase_amounts": 40, "price_expressions": {"base": 1.45, "multiplier": 20, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_2", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "additional_coin_value_large": {"name": "金币增值", "desc": "金币每次翻转额外获得$币。", "category": "upgrades", "requirement_to_buy": "large_coin > 0", "requirement_to_see": "large_coin > 0", "increase_amounts": 300.0, "max_purchase_amounts": 40, "price_expressions": {"base": 1.45, "multiplier": 300, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_1", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_cow": {"name": "牛", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_horse": {"name": "马", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 0, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_bear": {"name": "熊", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 0.005, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_cat": {"name": "猫", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_chook": {"name": "鸡", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_dinosaur": {"name": "恐龙", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_dog": {"name": "狗", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_frog": {"name": "青蛙", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_panda": {"name": "熊猫", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_penguin": {"name": "企鹅", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "employee_rabbit": {"name": "兔", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 12, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "skill_emergency_mobilization": {"name": "紧急动员", "desc": "员工在未来一段时间内持续工作", "category": "skill", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 10, "price_expressions": {"base": 1, "multiplier": 2, "flat_offset": 0}, "expiration_data": 0, "sellType": 0, "spriteName": "skill_active_3", "skill": {"cd": 60, "cd_addition": 1, "validity_time": 30, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "skill_high_efficient_work": {"name": "高效工作", "desc": "员工在未来一段时间内工作效率大幅提升", "category": "skill", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 10, "price_expressions": {"base": 1, "multiplier": 2, "flat_offset": 0}, "expiration_data": 0, "sellType": 0, "spriteName": "skill_active_1", "skill": {"cd": 60, "cd_addition": 3, "validity_time": 20, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 1}}, "skill_immediate_production": {"name": "立即生产", "desc": "立即翻动所有硬币", "category": "skill", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 10, "price_expressions": {"base": 1, "multiplier": 2, "flat_offset": 0}, "expiration_data": 0, "sellType": 0, "spriteName": "skill_active_2", "skill": {"cd": 60, "cd_addition": 3, "validity_time": 0, "validity_time_multiplier": 0, "result": 1, "result_multiplier": 2}}, "skill_temp_value_add": {"name": "临时增值", "desc": "在未来一段时间内，临时增加所有硬币翻动后获得的收益", "category": "skill", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 20, "price_expressions": {"base": 1, "multiplier": 2, "flat_offset": 0}, "expiration_data": 10, "sellType": 0, "spriteName": "skill_active_4", "skill": {"cd": 60, "cd_addition": 2, "validity_time": 20, "validity_time_multiplier": 2, "result": 100, "result_multiplier": 1}}, "skill_speed_up": {"name": "全局加速", "desc": "增加员工移动速度和硬币翻转速度", "category": "skill", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 50, "price_expressions": {"base": 1, "multiplier": 2, "flat_offset": 0}, "expiration_data": 10, "sellType": 0, "spriteName": "skill_active_4", "skill": {"cd": 60, "cd_addition": 2, "validity_time": 20, "validity_time_multiplier": 2, "result": 100, "result_multiplier": 1}}, "scene_grassland": {"name": "草地", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 0, "sellType": 0, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "scene_seaside": {"name": "海边", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 999999999, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 0.0005, "sellType": 2, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "scene_basketball_court": {"name": "篮球场", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 3000000, "multiplier": 10, "flat_offset": 0}, "expiration_data": 7, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}, "scene_warehouse": {"name": "仓库", "desc": "", "category": "items", "requirement_to_buy": "", "requirement_to_see": "", "increase_amounts": 1.0, "max_purchase_amounts": 1, "price_expressions": {"base": 1.45, "multiplier": 10, "flat_offset": 0}, "expiration_data": 7, "sellType": 1, "spriteName": "skill_passive_5", "skill": {"cd": 5, "cd_addition": 1, "validity_time": 2, "validity_time_multiplier": 2, "result": 1, "result_multiplier": 2}}}