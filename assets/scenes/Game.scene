[{"__type__": "cc.SceneAsset", "_name": "Game", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "Game", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 422}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 462}, "_id": "0003f82c-28a4-4551-a91a-05423d4ff542"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 19}, {"__id__": 72}, {"__id__": 104}, {"__id__": 109}, {"__id__": 132}, {"__id__": 143}, {"__id__": 183}, {"__id__": 196}, {"__id__": 215}, {"__id__": 231}, {"__id__": 271}, {"__id__": 280}, {"__id__": 293}, {"__id__": 302}, {"__id__": 353}, {"__id__": 378}, {"__id__": 387}, {"__id__": 396}], "_active": true, "_components": [{"__id__": 410}, {"__id__": 411}, {"__id__": 412}, {"__id__": 413}, {"__id__": 414}, {"__id__": 415}, {"__id__": 416}, {"__id__": 417}, {"__id__": 418}, {"__id__": 419}, {"__id__": 420}, {"__id__": 421}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 667, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31U5k075FMTauV8pnzjQpv"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4dZcCJxENAE5s6jEYtO1Cj"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 667, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "cbQHF8emZF57TWf7XREN3v"}, {"__type__": "cc.Node", "_name": "scene", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}, {"__id__": 9}, {"__id__": 12}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 18}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6acbckVPdB2JR8faCcqJJK"}, {"__type__": "cc.Node", "_name": "scene_grassland", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 7}, {"__id__": 8}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4b2H+xIkNB44zFVocoRM8X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3341DWR2xPAYCOxm0pSieV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2aaa5959-8642-4ba1-bc25-fa7559daef82@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c96HQilcNGJYVqxkui+Zdm"}, {"__type__": "cc.Node", "_name": "scene_basketball_court", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": false, "_components": [{"__id__": 10}, {"__id__": 11}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "91+JELRQlGvJDlBbJQzVGa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bavbifYWlHGo+6cVkPEj3u"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34c2ee11-8ed4-45ba-b55b-526a160f892e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ab3eqXyyhByYMvK+8n3NRP"}, {"__type__": "cc.Node", "_name": "scene_seaside", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": false, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cdSvyR/2JCaJB1pdNDtHE4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "775bgN/F9MpJMLuUZj90dW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "797a534b-29e7-4185-9999-9b8f3d149f4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bcQkkeOvtBwI4g9CGkx6GO"}, {"__type__": "cc.Node", "_name": "scene_warehouse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": false, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "203/+0U9FCs4wRXj+VHJMP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a6l6rcxgpGz63ZtEMep35e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20beaf1b-a98d-4836-92f7-eb0a813b5c2f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "feZl0C0cxFrZ1RkbVXsR3h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a2kgsa8K9N3oVgirahvj/1"}, {"__type__": "cc.Node", "_name": "LayoutTop", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 20}, {"__id__": 31}, {"__id__": 61}], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 612.034, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "47JuwyuApA2o+PJlZ5WPWs"}, {"__type__": "cc.Node", "_name": "Node_user", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 21}], "_active": true, "_components": [{"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -337.793, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ccD3vvpqBIV6amIGPhFqTe"}, {"__type__": "cc.Node", "_name": "user_info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 22}, {"__id__": 25}], "_active": true, "_components": [{"__id__": 28}, {"__id__": 29}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 92.223, "y": -2.772, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eagWp4oixOMqXsUaLzfZ9R"}, {"__type__": "cc.Node", "_name": "Label_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 23}, {"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 35, "y": 17.9885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fg0vwkXFC2qI/qIMS0MjZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 140.719815218186, "height": 27.119}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7cwhg3ZH9Ajbf5HDzbUrCv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "用户名字五", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 19, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2ez0asrgZI6oMBXRO+UB12"}, {"__type__": "cc.Node", "_name": "MoneySymbolLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 35, "y": -12.720500000000213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31kci8ndFN+5mtYSbwpYPx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 127.92391422613078, "height": 24.133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d0dFJp/4dK05V4aAv2kAhn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 238, "b": 161, "a": 255}, "_string": "白手起家", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 15, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "ce4ODoruxHsrCxWAfv6FJr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 228, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cccebbB3pOlK6PRnp+Ezsu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@1d021", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "05TLlGJc1PKLa8fJHhGUYn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43Wf1F5V9BdawOLCZPe5zg"}, {"__type__": "cc.Node", "_name": "game_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 32}, {"__id__": 41}, {"__id__": 51}], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 11.055, "y": -9.07, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48Il8B7KFAmJgC5UQtbxKr"}, {"__type__": "cc.Node", "_name": "Layout_money", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [{"__id__": 33}, {"__id__": 36}], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 112.15199999999999, "y": -22.983000000000175, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0ddQnrIGZPmIxeyXuvBmrK"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -180.113, "y": 41.058, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ddrVPRp+9OcJrClYmeYPnZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "10F5qMoYBDqI4bmGaW1Oay"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@95610", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "a427OYcJdPta7nVKTu10dN"}, {"__type__": "cc.Node", "_name": "Label_money", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -144.729, "y": 47.815, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4f/JsNx9FHxLvQK01mPMpZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 173.************, "height": 54.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "12OLMHRjVP2qX80w41wbWL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 241, "g": 225, "b": 37, "a": 255}, "_string": "176 k", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 53, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "44+CYLt2NPA4LTgfOU1lrZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77TIJ+IhtE7IMAaKC2B2hX"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": false, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": "16zQK0V7FCl7rkcrfRK3Fo"}, {"__type__": "cc.Node", "_name": "ProgressBar_target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [{"__id__": 42}, {"__id__": 45}], "_active": true, "_components": [{"__id__": 48}, {"__id__": 49}, {"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -28.101999999999975, "y": -32.161000000000286, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d7AiJtiExPG6mMg2k91+EX"}, {"__type__": "cc.Node", "_name": "Bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 41}, "_children": [], "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -63.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3RfBf7JlDNqOP57mt28Zx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 127, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "92lfSdqsNKbbE6zP0OqhyQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@91b4f", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "84jpG+sf9L56kEy1Pua3pI"}, {"__type__": "cc.Node", "_name": "Label_percent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 41}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.6840000000000259, "y": 2.061000000000149, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "47GysqxSBFBaLT1LBoMlrE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 35.41531753948028, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "010R38U7NDvavUlcRvWI5v"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "93%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21.026986506746628, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "b6Ck2sq79ATL3vzXV+4bvI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "75Ci4oI2xD+JkKB5q3eCuH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@c914c", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "2aEY9ZcHZJY5YUia9Hg5pe"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 44}, "_mode": 0, "_totalLength": 127, "_progress": 1, "_reverse": false, "_id": "9f6RZ+skFP/6Np0yqoFcWM"}, {"__type__": "cc.Node", "_name": "Sprite_target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 31}, "_children": [{"__id__": 52}], "_active": true, "_components": [{"__id__": 55}, {"__id__": 56}, {"__id__": 57}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 81.854, "y": -32.983, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d9XX+ApFlLaYlIONqDrGGb"}, {"__type__": "cc.Node", "_name": "Label_target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 4.79, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9efqY3TaNLl4csKSH67qi7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 43.986688547933376, "height": 54.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "12/+i5G19AG6wHERSUpe5C"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 191, "a": 255}, "_string": "百亿", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21.026986506746628, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 125, "g": 72, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "41lh5poeNHhqXUhwRcWqAe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5fpQFkpwNPnLXnOvLXBKSj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@10b11", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "0eS+rDOaBJPIgDfi7WvM9r"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 58}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "3bYKQWkDZLbJM0J/zxJpXR"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "810144GGKhNG6x7V6tvERBM", "handler": "showTargetTips", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 252, "height": 129}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0djp+Y9vxAr69/Ry1x6IUL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@102cc", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d3LznF7FRO9K9Y205Fpi/c"}, {"__type__": "cc.Node", "_name": "Sprite_diamond", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 62}, {"__id__": 65}], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 215.756, "y": -4.852, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97yVG2odFCHbqBN0++Sftj"}, {"__type__": "cc.Node", "_name": "Sprite_icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 64}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 32.407, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4esjEz40xDsapg5yP/QYRM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5d2l72DNtNl75Bl/SQURc7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@7311f", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "baq3a40SxN679hG0yPEwUU"}, {"__type__": "cc.Node", "_name": "Label_diamond", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.288, "y": 3.6115, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7e0+lKBZVKdIcNARPfYgHS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 101.24601641095002, "height": 31.903000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c9CDqCNGtJTLW2ecIvqsqJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "280K", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "74kPsStMpPOblHy1mz7QGu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 108, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8cCQkJFENLmqNH81y4JGeA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@20031", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "82mDYIt/9DRqpawuMDZwqT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1107, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f8FZimY89J/rbS4s21xmXM"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": false, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 8, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": "a7EDtB0OZAFp0qXyXM+stA"}, {"__type__": "cc.Node", "_name": "Layout_right", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 73}, {"__id__": 78}, {"__id__": 83}, {"__id__": 88}], "_active": true, "_components": [{"__id__": 102}, {"__id__": 103}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 326.356, "y": 610.284, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "53VDVoZcZGs6dsDJQnHVzS"}, {"__type__": "cc.Node", "_name": "ButtonSetting", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}, {"__id__": 76}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.6140000000000327, "y": 12.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d6d0WkJhJLtqcOkO8BtCbi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "adAA8JHl5Fm7PszelVdn7o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@88524", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "05C+MJlT1MDYIg6SHVXXhx"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 77}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@88524", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@88524", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@88524", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@88524", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 73}, "_id": "fcBOoIlodIJ6DC8tJTPRiK"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "810144GGKhNG6x7V6tvERBM", "handler": "onSettingButtonClick", "customEventData": ""}, {"__type__": "cc.Node", "_name": "ButtonRank", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}, {"__id__": 81}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1.61099999999999, "y": -83, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e2h2rZ0dpBibru7FDtUy0T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b8txe7dgpPy5SRIDGPH+YW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@62165", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "d1lzBD24hMOKZzPrt1NLji"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 82}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 78}, "_id": "bei/9StddEqabtpxM0MvUZ"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "810144GGKhNG6x7V6tvERBM", "handler": "onRankButtonClick", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Sprite_seven", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}, {"__id__": 86}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -175.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a2IRafWdhAapQddJ6p06ql"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bd6rVu+RpMVJxnEu3QXXlp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@d9281", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "5b0JsIcatM75qX0zK1ti0k"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 87}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "eeih/YDppN97vvFvKsIK2Q"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "", "handler": "", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Node_online", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [{"__id__": 89}, {"__id__": 92}, {"__id__": 95}], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}, {"__id__": 101}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -285, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b73rgYqrNDOJXqIS8/MWOl"}, {"__type__": "cc.Node", "_name": "Sprite_online", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f5l5k7xk5K8qbIzbfVv99e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "27z+YH6nJFppITo6vpK4a8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@831fb", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "0bSbGQQ5pEGK970CFGq7TG"}, {"__type__": "cc.Node", "_name": "Label_time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [], "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.331, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85XhgynvtDTbdKAePktzXt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 83.97337709586675, "height": 39.27999999999997}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9rVrNi2BDoZ7KItBFq3I6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "在线奖励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21.026986506746628, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "8fjqL8NPJAcoSP/uz6ydFT"}, {"__type__": "cc.Node", "_name": "red_dot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 24.314, "y": 24.675, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fc446cvXFHfaFmUq7P/B+E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 21, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5dJXeZtK9Lgr+OGaUjPWgq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@13522", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "edZ6iBCJ9JIL2h0kam6FWX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7f4S3/K/5NM69xB41EhnGv"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 100}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 88}, "_id": "ad2nhvNORCQ6iwwef36EWC"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 88}, "component": "", "_componentId": "86a569Q61BK5Z/jX9Hxf49g", "handler": "getRewawrd", "customEventData": ""}, {"__type__": "86a569Q61BK5Z/jX9Hxf49g", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "nodeReddot": {"__id__": 95}, "labelTime": {"__id__": 94}, "_id": "56+yUWVaFNYJkk31O0ax54"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0f0zaDb15ClqeUfGG34zBR"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 25, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "a3422iMcJImrhNWVuOhfq2"}, {"__type__": "cc.Node", "_name": "Interactables", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 105}], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 210, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebOA/15tNHB7c9ZCjnNxXN"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_components": [{"__id__": 106}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 2.384, "y": -252.671, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3enbGM10NAp7kKCL9SzRNf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "44AvbZ1pVGM71pGG/UpC5L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 612}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "db/bM+vohKTI5s5Qq5KSwa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d4fdAA0j5D4o8iPb91xddO"}, {"__type__": "cc.Node", "_name": "SpeedUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 110}, {"__id__": 113}, {"__id__": 116}, {"__id__": 119}], "_active": false, "_components": [{"__id__": 129}, {"__id__": 130}, {"__id__": 131}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -313.787, "y": -219.352, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8dQquucv1JfZLXvCIRhtJA"}, {"__type__": "cc.Node", "_name": "SpriteCD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 111}, {"__id__": 112}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6V0lu+JJMdLE/ROsSFiTs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9bqw1stSJLs69bZiDRd4//"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 90}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@91216", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 2, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0.25, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "baB5g37/9Pmq05eeTSzJwZ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 115}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 1.67, "y": -20.64, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a1i5M8iPFMCbgLvFYfmsPI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 41.14438330826858, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fdD26mX7hApKpkrkekZWLb"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x1.0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20.53223388305847, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "b5cswgbs5FiajnYWfMSebf"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 117}, {"__id__": 118}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -51.448, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a7kIArbw1IhLBZTePsldBd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 43.99564566861194, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eeRi7cH99P1qPol44UnP25"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "生效", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20.53223388305847, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "6148AK1+ROibjeLUbeRGW4"}, {"__type__": "cc.Node", "_name": "ButtonAD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [{"__id__": 120}, {"__id__": 123}], "_active": true, "_components": [{"__id__": 126}, {"__id__": 127}, {"__id__": 128}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 57.484, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a326tMnGdICZdpqMJ018b/"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 121}, {"__id__": 122}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -22.145, "y": 4.923, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9egAvhg5JDToUJXdxsEsOD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "97xH1vdL5E5I2FtTHcYZW2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@8bd4f", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "22BwSooQZASLMRM4P6sRAe"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 125}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 17.46, "y": 6.601, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "50tbvLAZBIza73xz7CrLzL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1aCHTHhmZBdIhouxD7dacd"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "解锁", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20.53223388305847, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "f78VduhZZCx5eqfgtCcz1n"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "55tWERhPFG+qfsfj78+Uuf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@0e2ff", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "79JNjL0EBCcZ92JvNFNmGx"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@0e2ff", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@0e2ff", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@0e2ff", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@0e2ff", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 119}, "_id": "9c+G/0ztVA+JoRCJ0aYU0D"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77/0OevaxAI6yeb32ms00A"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@91216", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 2, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "37EuN4A0RKVJdHNkL+Rj/c"}, {"__type__": "a66ael0UQxIB5c+AKAPBut7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "cooldownTime": 10, "skillIconSprite": {"__id__": 112}, "labelSkillValidity": {"__id__": 118}, "idKey": "skill_speed_up", "_id": "4b5Z3AUNtERbvsO+jqGJFq"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 133}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 132}, "asset": {"__uuid__": "3226292d-3017-46a1-985f-10530df163e2", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 134}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "90RPP2X0JCBKdD5psy8Vui", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 135}, {"__id__": 137}, {"__id__": 139}, {"__id__": 141}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 136}, "propertyPath": ["_name"], "value": "Task"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -207.185, "y": -225.201, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 140}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_name": "skill", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 144}, {"__id__": 154}, {"__id__": 164}, {"__id__": 171}], "_active": true, "_components": [{"__id__": 181}, {"__id__": 182}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 182.713, "y": -220.352, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "24wJhkanRIYpOu7ENPtgxi"}, {"__type__": "cc.Node", "_name": "TempValueAdd", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 143}, "_children": [{"__id__": 145}, {"__id__": 148}], "_active": true, "_components": [{"__id__": 151}, {"__id__": 152}, {"__id__": 153}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 109.84750000000003, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3aBDMkVyBMx7ybriQo2evf"}, {"__type__": "cc.Node", "_name": "SpriteCD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [{"__id__": 146}, {"__id__": 147}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "55KsbctWtKeZxjU/izodDH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cdtVOe8exOeoeMJq9rzXu0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 80}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@f2f1f", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 2, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0.25, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "d46PAlFWJPcZV8qnzCrSNK"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [{"__id__": 149}, {"__id__": 150}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "14ty0bq3ZGTb1YWYuwAMz8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 148}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 39.5903664111241, "height": 54.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "17rijRbqZAtK6cWAZtUPee"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 148}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "27", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 33.643178410794604, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "68MtHlfrNIWIAbXCCAwUAz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bbu0pUXQ1DU7RJHNL7LRXX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@f2f1f", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 2, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "c9PGjV87hMypwj9kuafp1l"}, {"__type__": "a66ael0UQxIB5c+AKAPBut7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "cooldownTime": 10, "skillIconSprite": {"__id__": 147}, "labelSkillValidity": {"__id__": 150}, "idKey": "skill_temp_value_add", "_id": "24/wj306BK3KyW6EKoSl8R"}, {"__type__": "cc.Node", "_name": "EmergencyMobilization", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 143}, "_children": [{"__id__": 155}, {"__id__": 158}], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}, {"__id__": 163}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 44.847500000000025, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9aNSPb6zhJKrCFPvuDA2pG"}, {"__type__": "cc.Node", "_name": "SpriteCD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 154}, "_children": [], "_active": true, "_components": [{"__id__": 156}, {"__id__": 157}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5eEha/SbJFpqHd+fBIkYtF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aaVxGZjNtJyrzXrHQKhYML"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 70}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@55a22", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 2, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0.25, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "77aksEs+dIvLqECE3HS9B9"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 154}, "_children": [], "_active": true, "_components": [{"__id__": 159}, {"__id__": 160}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "74RvE8hIVN+KouJxeDmmuG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 39.5903664111241, "height": 54.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "42ETkNqaFMVbzMbNTjkF0c"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "25", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 33.643178410794604, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "92m2gy80NI+o8dc5Gi8O5Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e5r3jC0yBBB5SX6KOUv/4E"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@55a22", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 2, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "57Iu1xScJCSrrB+OeOiRh9"}, {"__type__": "a66ael0UQxIB5c+AKAPBut7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "cooldownTime": 10, "skillIconSprite": {"__id__": 157}, "labelSkillValidity": {"__id__": 160}, "idKey": "skill_emergency_mobilization", "_id": "07jeU9I7FKq4Y6SitYpJ4L"}, {"__type__": "cc.Node", "_name": "ImmediateProduction", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 143}, "_children": [{"__id__": 165}], "_active": true, "_components": [{"__id__": 168}, {"__id__": 169}, {"__id__": 170}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -20.152499999999975, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d60p7Tsh1Ixp5odVIvBv3o"}, {"__type__": "cc.Node", "_name": "SpriteCD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 166}, {"__id__": 167}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "83jnn+a3VBNLiGFIQbPA7N"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "caP8CtmONLe71a5mL+uUWe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 60}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@a6fce", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 2, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0.25, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "26UGjglvFDN7+4kTFz49Ns"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3aRf/Y0iJPI6VVfEVwG+KR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@a6fce", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 2, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "34r2uP+65EtqwTCtat5pfn"}, {"__type__": "a66ael0UQxIB5c+AKAPBut7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "cooldownTime": 10, "skillIconSprite": {"__id__": 167}, "labelSkillValidity": null, "idKey": "skill_immediate_production", "_id": "48dwJJMZtNl7FoG3WmnmsG"}, {"__type__": "cc.Node", "_name": "HighEfficientWork", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 143}, "_children": [{"__id__": 172}, {"__id__": 175}], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}, {"__id__": 180}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -85.15249999999997, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0fvyPYb7BHNLjnxQS6B/az"}, {"__type__": "cc.Node", "_name": "SpriteCD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [], "_active": true, "_components": [{"__id__": 173}, {"__id__": 174}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3aVNrLevhA/pTRatrT9VTu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c74DaiDTZHyqV7gH3ojVsG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 50}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@1724f", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 2, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0.25, "_fillRange": 0.5, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "11XKE4ZuxAObexrSeFWEGK"}, {"__type__": "cc.Node", "_name": "Label-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [], "_active": true, "_components": [{"__id__": 176}, {"__id__": 177}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8dNc3/Ai9EjZlJ1sGmjEFW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 39.5903664111241, "height": 54.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1ctdtsPQxBaZ6v9u+GAyze"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "30", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 33.643178410794604, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "42AnTAHGROy422X9XWJNWG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "075QdEC5FG8oZxC3EjXh4X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5@1724f", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 2, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "92a072d1-9af2-4083-8847-3e5d98bad5d5", "__expectedType__": "cc.SpriteAtlas"}, "_id": "09PDZsTVRJ+5yhU+sR2xlS"}, {"__type__": "a66ael0UQxIB5c+AKAPBut7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "cooldownTime": 10, "skillIconSprite": {"__id__": 174}, "labelSkillValidity": {"__id__": 177}, "idKey": "skill_high_efficient_work", "_id": "0e7Mf7iyxLNoGjaCF8/qi6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 279.69500000000005, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1e+9FAAXND2qT7ibCx9CvO"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 1, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "46Yp5KBjZCyr+JqopdO1Lt"}, {"__type__": "cc.Node", "_name": "ProgressBar_fragments", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 184}, {"__id__": 187}, {"__id__": 190}], "_active": true, "_components": [{"__id__": 193}, {"__id__": 194}, {"__id__": 195}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 346.904, "y": -257.868, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebSh0QgGNCoIFpqI/nHGN+"}, {"__type__": "cc.Node", "_name": "Bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 185}, {"__id__": 186}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e1EQEKWxFOoaNN7Re9Hqo/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 184}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 21, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "b0YSiKhVFI/Ysspe/wocrb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 184}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@e5af0", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "36+A38xgNNEaaVYYf242ly"}, {"__type__": "cc.Node", "_name": "Label_progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 189}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -8.61, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "50tQD3OslM361SZotqlAdd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 35.41531753948028, "height": 54.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b7eg9JuRhPfb/iHOi78TUs"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "93%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21.026986506746628, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "ccUXaHD0ROO5hrL1yAC+/t"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 191}, {"__id__": 192}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.96, "y": 114.394, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3aRBojeVIfbADPoJYaU8X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a2ANjlfnBN6ovPPcWnXF0H"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@52e34", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "43XgSZIppJO7gIAcyRE1+j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "52tQEnxDdH4JEdUlVnBOcq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@0df3b", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede", "__expectedType__": "cc.SpriteAtlas"}, "_id": "8ety9XlF5BV5Vna2K0QX3N"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 186}, "_mode": 1, "_totalLength": 90, "_progress": 1, "_reverse": false, "_id": "e6c9uetONHj5BmWdBSsK6A"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 197}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 196}, "asset": {"__uuid__": "d81bf671-abba-48d0-9098-01f246cfd81e", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 198}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c51KvLVhdPFZCFZTv8d100", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 199}, {"__id__": 201}, {"__id__": 203}, {"__id__": 205}, {"__id__": 207}, {"__id__": 209}, {"__id__": 211}, {"__id__": 213}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 200}, "propertyPath": ["_name"], "value": "ShopBar"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 202}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -616.867, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 204}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 208}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 51.99545711306539, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["d0V4mfLtlNw4fruXyrRDKt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 210}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 51.99545711306539, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["2cpmOr69dD6KpS4ik8APs5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 212}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 51.99545711306539, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["69Hcc+EstDfqMmgyBu079f"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 214}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 51.99545711306539, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["5dPYTuqgdOX75c6P2Lsjs2"]}, {"__type__": "cc.Node", "_name": "Node_tips", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 216}, {"__id__": 219}, {"__id__": 222}], "_active": false, "_components": [{"__id__": 230}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 92.407, "y": 488.902, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 6.123233995736766e-17, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 7.016709298534876e-15}, "_id": "1cxVAhXRRE+ouTWfxX7Ms7"}, {"__type__": "cc.Node", "_name": "dialog_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2.1100664349308917e-16, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "fdIWuVIkpNw5vIt3U+2epJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 281.658, "height": 116.692}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6bScDpSHZIsLLWmo3AT0nW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "153b4dfa-05f7-4f48-9e5d-f35fd6d36ede@ecf5a", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2dFeIDs/xC3bUhhUsJ7hju"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.334, "y": -4.743, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8byEUgbw9NM5AhY9d3y/a2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 231.0605412053671, "height": 79.34200000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4awnS5FBNLVZdi2Ke1eIIQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "赚到一个亿\n用时最短者可以上排行榜", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "ccb78b01-7389-4953-9d48-7b898b712d55", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "65J+HggVlE6oG88q46lFw8"}, {"__type__": "cc.Node", "_name": "Button_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [{"__id__": 223}], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}, {"__id__": 228}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -502.457, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "979ja9UpRO1Khog3zl+ZmE"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 224}, {"__id__": 225}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49Gw89Y/lDJYqqKi+sXeXF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "325Syul1lM04C6eotgVlQG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "button", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21.026986506746628, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "bdZP5O2+1MvImiKgyzU5q1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1a4DNOS8FI6Kj/VJCK/I5I"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3fPCMzc25Lkp5gVvfbOa1k"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 229}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 222}, "_id": "278AeUbtBMzoYAmXzTa59G"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 215}, "component": "", "_componentId": "204e9+/7tNHeoWAKlrG/tsY", "handler": "onClose", "customEventData": ""}, {"__type__": "204e9+/7tNHeoWAKlrG/tsY", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": null, "labelTargetTips": {"__id__": 221}, "_id": "8bwM2vXyFG0bdxn1I+Sx3G"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 232}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 231}, "asset": {"__uuid__": "7142c333-ccc3-49bb-ac3b-98151ce8deee", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 233}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "9eJfR7MKRC9KJjzaU+U5CB", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 234}, {"__id__": 236}, {"__id__": 237}, {"__id__": 238}, {"__id__": 239}, {"__id__": 240}, {"__id__": 241}, {"__id__": 243}, {"__id__": 245}, {"__id__": 247}, {"__id__": 249}, {"__id__": 251}, {"__id__": 253}, {"__id__": 255}, {"__id__": 257}, {"__id__": 259}, {"__id__": 261}, {"__id__": 263}, {"__id__": 265}, {"__id__": 267}, {"__id__": 269}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_name"], "value": "ShopItemIntroduce"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 242}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["42FoPOkNBNRKpAb0sdYqaz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["6ag98Ex5xBiYhjgZ0iu0Zn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 246}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["c69Ob1YDJGDKCFUeQwwbK1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 248}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["fd7HBraiNGGa39D8em0sfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["ca/fELzIlNoKTY4fhTEAMB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 252}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["204s/7qA1LUJd1P+eTUmoR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["49aaOR9PtGrpGTmg6wZ822"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 256}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["29nUbG0D5L0q8iUBBJomU6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 258}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["a9T1Ya/IxOv5dq+hdcFzws"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 260}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["f4XL+mtKVIe6T5i/c7ybLU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 262}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["5csHmXUvpBT76Ow8Bed2qn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 264}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["4a30LOlz1AF4VXcxjZDMrU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 266}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["c39HNBxZRDZZnl3J9w2EMA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 268}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["7eX4lT5spK2LmA4sZ67IF8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 270}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["e1SzbWHUJBVah+1v5UB1Iq"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 272}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 271}, "asset": {"__uuid__": "b968da2c-edb2-49a4-a30e-8bd57cee9b12", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 273}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "efKY9LiM1GmJmFQTlNyfA+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 274}, {"__id__": 276}, {"__id__": 277}, {"__id__": 278}, {"__id__": 279}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_name"], "value": "ShopItemSkillIntroduce"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 281}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 280}, "asset": {"__uuid__": "90c534b0-6a21-4cdc-9b56-0bd5f4c3df14", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 282}, "targetOverrides": [], "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c1YPV9UvVOWpGqSEEhFiIe", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 283}, {"__id__": 285}, {"__id__": 286}, {"__id__": 287}, {"__id__": 288}, {"__id__": 290}, {"__id__": 291}, {"__id__": 292}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 284}, "propertyPath": ["_name"], "value": "Setting"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 284}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 284}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 284}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 289}, "propertyPath": ["clickEvents", "0", "target"], "value": {"__id__": 280}}, {"__type__": "cc.TargetInfo", "localID": ["4fFIFFAxdA17HMnbinpJ2T"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 289}, "propertyPath": ["clickEvents", "0", "handler"], "value": "showCheck"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 284}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 289}, "propertyPath": ["_transition"], "value": 0}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 294}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 293}, "asset": {"__uuid__": "17412b4a-5bcb-4829-9fd2-80348e9637d6", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 295}, "targetOverrides": [], "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "35HlNjHzpL2bg8u4Q9qX9h", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 296}, {"__id__": 298}, {"__id__": 299}, {"__id__": 300}, {"__id__": 301}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_name"], "value": "Check"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 303}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 302}, "asset": {"__uuid__": "4293bee1-03a9-45d3-9791-25a31c2c75ad", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 304}, "targetOverrides": [{"__id__": 311}, {"__id__": 315}, {"__id__": 318}, {"__id__": 321}, {"__id__": 324}, {"__id__": 327}, {"__id__": 330}, {"__id__": 333}, {"__id__": 336}, {"__id__": 339}, {"__id__": 342}, {"__id__": 345}, {"__id__": 348}, {"__id__": 351}]}, {"__type__": "cc.PrefabInstance", "fileId": "1dd+ED2TdC47+rBWkuDI6l", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 305}, {"__id__": 307}, {"__id__": 308}, {"__id__": 309}, {"__id__": 310}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_name"], "value": "Rank"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 313}, "propertyPath": ["spriteBgDefault"], "target": {"__id__": 312}, "targetInfo": {"__id__": 314}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["bbGIvIW2xN/LHrS3rg2BgV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 316}, "propertyPath": ["spriteBg1"], "target": {"__id__": 312}, "targetInfo": {"__id__": 317}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["c4ki3SKCRJuY0PHD0tz+z1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 319}, "propertyPath": ["spriteBg2"], "target": {"__id__": 312}, "targetInfo": {"__id__": 320}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["9b66xek5pJMpJFqjmaWomh"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 322}, "propertyPath": ["spriteBg3"], "target": {"__id__": 312}, "targetInfo": {"__id__": 323}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["aaqSLp5hlHrZyDBd2RJDWk"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 325}, "propertyPath": ["spriteBgSelect"], "target": {"__id__": 312}, "targetInfo": {"__id__": 326}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["beO6sg/p1PpKL6Ii2u1fUl"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 328}, "propertyPath": ["labelRank"], "target": {"__id__": 312}, "targetInfo": {"__id__": 329}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["c4/TpS2+BIgq6WFtKLp7DA"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 331}, "propertyPath": ["spriteRank1"], "target": {"__id__": 312}, "targetInfo": {"__id__": 332}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["61ifb4k8dMFpJvx4WCpTty"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 334}, "propertyPath": ["spriteRank2"], "target": {"__id__": 312}, "targetInfo": {"__id__": 335}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["78jy6nmS1DIq9RYwfCKCCf"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 337}, "propertyPath": ["spriteRank3"], "target": {"__id__": 312}, "targetInfo": {"__id__": 338}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["16Bq+AJvBPYLBdj/Qfb0eH"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 340}, "propertyPath": ["spriteHead"], "target": {"__id__": 312}, "targetInfo": {"__id__": 341}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["aaoQkTFAdN0IrLAos3upky"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 343}, "propertyPath": ["labelName"], "target": {"__id__": 312}, "targetInfo": {"__id__": 344}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["85Ogio3S9DyIiKBuFJxc1J"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 346}, "propertyPath": ["labelAddress"], "target": {"__id__": 312}, "targetInfo": {"__id__": 347}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["1amqOzxfZAjrs31JOVp4hp"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 312}, "sourceInfo": {"__id__": 349}, "propertyPath": ["labelTime"], "target": {"__id__": 312}, "targetInfo": {"__id__": 350}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.TargetInfo", "localID": ["94rshfRmNOLJPLTq1zu358"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rankItemMe"], "target": {"__id__": 312}, "targetInfo": {"__id__": 352}}, {"__type__": "cc.TargetInfo", "localID": ["0eP/HSFkZByZcEG0hsiFQr"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 354}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 353}, "asset": {"__uuid__": "eb803e44-5d80-467b-84e3-a38f2d64e99c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 355}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "38HDGihTBMeqeIVxcr0Da7", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 356}, {"__id__": 358}, {"__id__": 359}, {"__id__": 360}, {"__id__": 361}, {"__id__": 363}, {"__id__": 365}, {"__id__": 367}, {"__id__": 369}, {"__id__": 371}, {"__id__": 373}, {"__id__": 375}, {"__id__": 377}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 357}, "propertyPath": ["_name"], "value": "TaskComplete"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 357}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 357}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 357}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 362}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 171.99765216382076, "height": 67}}, {"__type__": "cc.TargetInfo", "localID": ["02Tt7fECpGpbRkcEa7loTQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 364}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 66.99911956143279, "height": 67}}, {"__type__": "cc.TargetInfo", "localID": ["89Tgsk/QlLRpeK6PC91wEH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 366}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 83.96494161227585, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["faj1K5R0FMAb8O15KfV3i/", "1cm8jz6tZCk7dBDsaq6VM6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 368}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 52.544777227688776, "height": 67}}, {"__type__": "cc.TargetInfo", "localID": ["faj1K5R0FMAb8O15KfV3i/", "b5xQiK0ChO7p6oqyKKP+w8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 370}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 83.96494161227585, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["15XDqEtxBJ7p6BDzqgfTEd", "1cm8jz6tZCk7dBDsaq6VM6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 372}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 52.544777227688776, "height": 67}}, {"__type__": "cc.TargetInfo", "localID": ["15XDqEtxBJ7p6BDzqgfTEd", "b5xQiK0ChO7p6oqyKKP+w8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 374}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 83.96494161227585, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["9cvdFODtdPxomZTUMiAj8z", "1cm8jz6tZCk7dBDsaq6VM6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 376}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 52.544777227688776, "height": 67}}, {"__type__": "cc.TargetInfo", "localID": ["9cvdFODtdPxomZTUMiAj8z", "b5xQiK0ChO7p6oqyKKP+w8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 357}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 379}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 378}, "asset": {"__uuid__": "480f6ce9-5cbb-412a-b141-14ac95d9caf0", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 380}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "96o55y4TtARYlsMSjEUHk5", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 381}, {"__id__": 383}, {"__id__": 384}, {"__id__": 385}, {"__id__": 386}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 382}, "propertyPath": ["_name"], "value": "ShopItemSkillFragmentIntroduce"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 382}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 382}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 382}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 382}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 388}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 387}, "asset": {"__uuid__": "30ac582b-0390-403c-a7f3-cfa4c2aca968", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 389}, "targetOverrides": [], "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0fPupFE91HL6KHK3U+Si3O", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 390}, {"__id__": 392}, {"__id__": 393}, {"__id__": 394}, {"__id__": 395}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_name"], "value": "OfflineIncome"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 397}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 396}, "asset": {"__uuid__": "c9c5d6d6-1d41-40c0-b520-e330defd874a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 398}, "targetOverrides": [], "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "02NnvCAQVJA7v+msfHoJs+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 399}, {"__id__": 401}, {"__id__": 403}, {"__id__": 405}, {"__id__": 407}, {"__id__": 409}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 400}, "propertyPath": ["_name"], "value": "Welcome"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 402}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 404}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 408}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 168, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["canKPZJ4ZGrJ6QRx8joLNn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "51VeJlsU5Ne64MHkz6yxfL"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "3eZWqvlEdDEajmKw0lh7fX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "12yu9iGk9IM75svn8uqFNt"}, {"__type__": "efb39ViMgJMTb1K5Yiom4CT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "gameValuesAsset": {"__uuid__": "b02a817c-02b2-430e-9bd0-063379dbac5d", "__expectedType__": "cc.Json<PERSON>set"}, "purchasableItemsAsset": {"__uuid__": "297b1325-8676-4b76-a6d7-0853e6379407", "__expectedType__": "cc.Json<PERSON>set"}, "_id": "9eSUNmkhhGZIPWd7tG3OpZ"}, {"__type__": "b7372KcKgFLGJaSw6EjPp62", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "9ar4VJScRAequ3/TzHfFnn"}, {"__type__": "602edXaJXxG2YzFugc9Y3A4", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "94ETFsHhZBTY/qk1voQSIl"}, {"__type__": "b5600A7vEtJYLRuKh+JpvQe", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "17sDdciGJOmqlARPL8arp9"}, {"__type__": "b8945DCqU1BDJv/xbsn60/6", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "bgmClips": [{"__uuid__": "16c65363-cf5c-4681-b490-e7bceff81ac6", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "bdb81f5b-5a4a-4bc2-8b36-e7cb5a4bb29e", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "b420eaee-a6dd-42e1-ac8e-d610e4575e2b", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "02bc3e63-5273-42a9-89a9-ee0dd7bf97d7", "__expectedType__": "cc.AudioClip"}], "sfxClips": [{"__uuid__": "e6364a2d-8410-45a3-a92d-8a570488e386", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "3cc3f681-51e8-4552-a3e2-5c64ca4279b3", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "92cb7659-791e-4c90-b9a4-c1b1d12ea858", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "f7593552-a34d-46a2-9dc4-e34e43eb8c8a", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "7f97571a-832f-4e34-abe5-b14d952abcfd", "__expectedType__": "cc.AudioClip"}, null, {"__uuid__": "da8147a7-b5d6-4c64-911c-3eeac136d5fc", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "ad0fae19-9b33-4905-a31f-48e6ffd1e39a", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "50162a4b-302a-4a93-9733-8820e0a319f5", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "a0afd14d-c483-46f3-9a6c-031d2c81b11c", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "7a31d59a-f166-4bff-8a38-91d65018dee7", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "d005c8fc-c500-4272-8888-4b60faa915a7", "__expectedType__": "cc.AudioClip"}, {"__uuid__": "e9e33572-6447-4a07-b777-395a7653fc3d", "__expectedType__": "cc.AudioClip"}], "_id": "1d5XO3TRRMe4u6s6T5rNAZ"}, {"__type__": "810144GGKhNG6x7V6tvERBM", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "tableSprites": [{"__id__": 8}, {"__id__": 11}, {"__id__": 14}, {"__id__": 17}], "labelMoney": {"__id__": 38}, "labelDiamond": {"__id__": 67}, "moneyProgressBar": {"__id__": 50}, "moneyProgressLabel": {"__id__": 47}, "labelTarget": {"__id__": 54}, "targetProgressBar": {"__id__": 195}, "labelProgress": {"__id__": 189}, "moneySymbolLabel": {"__id__": 27}, "smallCoinPrefab": {"__uuid__": "166e7876-e472-4796-ae82-6cd47dd7dafb", "__expectedType__": "cc.Prefab"}, "mediumCoinPrefab": {"__uuid__": "f7c5a02e-5d7d-4f3e-9e31-8c9e1e6d9c4b", "__expectedType__": "cc.Prefab"}, "largeCoinPrefab": {"__uuid__": "e74c0405-e7db-4a92-89d5-14471ef4609e", "__expectedType__": "cc.Prefab"}, "interactablesContainer": {"__id__": 104}, "empolyeeBearPrefab": {"__uuid__": "48191345-7e15-4048-aa16-076b2055b821", "__expectedType__": "cc.Prefab"}, "empolyeeCatPrefab": {"__uuid__": "b0109b0b-fdad-4403-959f-8e6a15d2e555", "__expectedType__": "cc.Prefab"}, "empolyeeChookPrefab": {"__uuid__": "7106b2f7-1bfd-40c6-b043-f8f1aae37314", "__expectedType__": "cc.Prefab"}, "empolyeeCowPrefab": {"__uuid__": "58619808-16d1-481b-9181-a8b693109459", "__expectedType__": "cc.Prefab"}, "empolyeeDinosaurPrefab": {"__uuid__": "c6fc7867-942b-4035-b934-ea7791762b2a", "__expectedType__": "cc.Prefab"}, "empolyeeDogPrefab": {"__uuid__": "c5c185c3-1db3-4d4c-aa9a-f35ce75415c2", "__expectedType__": "cc.Prefab"}, "empolyeeFrogPrefab": {"__uuid__": "088d13de-b8b8-4f00-8685-c07cd7cadd6c", "__expectedType__": "cc.Prefab"}, "empolyeeHorsePrefab": {"__uuid__": "0b5a38be-3f72-4a3c-a131-792ca3f996ac", "__expectedType__": "cc.Prefab"}, "empolyeePandaPrefab": {"__uuid__": "b1954464-e7eb-41d4-b70a-c41874b82f55", "__expectedType__": "cc.Prefab"}, "empolyeePenguinPrefab": {"__uuid__": "d185689b-e620-437c-aba1-02bf7103069d", "__expectedType__": "cc.Prefab"}, "empolyeeRabbitPrefab": {"__uuid__": "a83e2e2f-988f-47a7-b485-dcb41a1f7560", "__expectedType__": "cc.Prefab"}, "skillTempValueAdd": {"__id__": 144}, "skillEmergencyMobilization": {"__id__": 154}, "skillImmediateProduction": {"__id__": 164}, "skillHighEfficientWork": {"__id__": 171}, "nodeWelcome": {"__id__": 396}, "_id": "cc5ZLPFyBJfYj+Q+zw3WE+"}, {"__type__": "5d654hbQLlBbo76hZP7TSYO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "mainPanel": null, "optionsPanel": null, "shopPanel": null, "moneyLabel": null, "shopItemInstroduce": {"__id__": 231}, "shopItemSkillInstroduce": {"__id__": 271}, "taskComplete": {"__id__": 353}, "shopItemSkillFragmentInstroduce": {"__id__": 378}, "floatingTextPrefab": {"__uuid__": "f966ab35-6931-42c0-8e70-fb67aaa3fdaf", "__expectedType__": "cc.Prefab"}, "nodeSetting": {"__id__": 280}, "nodeCheck": {"__id__": 293}, "rank": {"__id__": 302}, "offlineIncomeUI": {"__id__": 387}, "nodeTargetTips": {"__id__": 215}, "_id": "20dELnDTpCqKcmgAvdp1Bs"}, {"__type__": "18447lr3hRPIqLw2Zs5YZ3y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "56KK/k/p9E57d2vI+Lgorh"}, {"__type__": "2ba5b80wLdKHbuEtNVhJN9i", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "nodeGuide_2": null, "nodeGuide_3": null, "nodeGuide_13": null, "nodeGuide_14": null, "nodeGuide_18": null, "scrollView": null, "_id": "cbvXFjQEtP25NsD9kIayEb"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "0003f82c-28a4-4551-a91a-05423d4ff542", "instance": null, "targetOverrides": [{"__id__": 423}, {"__id__": 426}, {"__id__": 429}, {"__id__": 432}, {"__id__": 435}, {"__id__": 438}, {"__id__": 441}, {"__id__": 444}, {"__id__": 447}, {"__id__": 450}, {"__id__": 452}, {"__id__": 454}, {"__id__": 456}, {"__id__": 458}, {"__id__": 460}], "nestedPrefabInstanceRoots": [{"__id__": 132}, {"__id__": 196}, {"__id__": 231}, {"__id__": 271}, {"__id__": 280}, {"__id__": 293}, {"__id__": 302}, {"__id__": 353}, {"__id__": 378}, {"__id__": 387}, {"__id__": 396}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 424}, "propertyPath": ["_content"], "target": {"__id__": 196}, "targetInfo": {"__id__": 425}}, {"__type__": "cc.TargetInfo", "localID": ["91jzNdfatH85r2mzSCu2eE"]}, {"__type__": "cc.TargetInfo", "localID": ["cdG9Of3cVBL4eRFE7zl/2Q"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 427}, "propertyPath": ["_verticalScrollBar"], "target": {"__id__": 196}, "targetInfo": {"__id__": 428}}, {"__type__": "cc.TargetInfo", "localID": ["91jzNdfatH85r2mzSCu2eE"]}, {"__type__": "cc.TargetInfo", "localID": ["10YBlhnHJAxag7ktF5IOSu"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 430}, "propertyPath": ["content"], "target": {"__id__": 196}, "targetInfo": {"__id__": 431}}, {"__type__": "cc.TargetInfo", "localID": ["91jzNdfatH85r2mzSCu2eE"]}, {"__type__": "cc.TargetInfo", "localID": ["cdG9Of3cVBL4eRFE7zl/2Q"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 433}, "propertyPath": ["_horizontalScrollBar"], "target": {"__id__": 196}, "targetInfo": {"__id__": 434}}, {"__type__": "cc.TargetInfo", "localID": ["91jzNdfatH85r2mzSCu2eE"]}, {"__type__": "cc.TargetInfo", "localID": ["10YBlhnHJAxag7ktF5IOSu"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 436}, "propertyPath": ["horizontalScrollBar"], "target": {"__id__": 196}, "targetInfo": {"__id__": 437}}, {"__type__": "cc.TargetInfo", "localID": ["91jzNdfatH85r2mzSCu2eE"]}, {"__type__": "cc.TargetInfo", "localID": ["10YBlhnHJAxag7ktF5IOSu"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 439}, "propertyPath": ["_content"], "target": {"__id__": 196}, "targetInfo": {"__id__": 440}}, {"__type__": "cc.TargetInfo", "localID": ["86BJIHJ49CNI0ftdHEnCnw"]}, {"__type__": "cc.TargetInfo", "localID": ["87cc/mNs1MvK2ID+iSYi6O"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 196}, "sourceInfo": {"__id__": 442}, "propertyPath": ["_verticalScrollBar"], "target": {"__id__": 196}, "targetInfo": {"__id__": 443}}, {"__type__": "cc.TargetInfo", "localID": ["86BJIHJ49CNI0ftdHEnCnw"]}, {"__type__": "cc.TargetInfo", "localID": ["fbt3Cv90dFmLs2SfQNDoBa"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 280}, "sourceInfo": {"__id__": 445}, "propertyPath": ["_target"], "target": {"__id__": 280}, "targetInfo": {"__id__": 446}}, {"__type__": "cc.TargetInfo", "localID": ["fa/QntRtFK0LppY5S3ffvl"]}, {"__type__": "cc.TargetInfo", "localID": ["fdCX/T3ptKuK3p5XoHl7bE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 280}, "sourceInfo": {"__id__": 448}, "propertyPath": ["_target"], "target": {"__id__": 280}, "targetInfo": {"__id__": 449}}, {"__type__": "cc.TargetInfo", "localID": ["4fFIFFAxdA17HMnbinpJ2T"]}, {"__type__": "cc.TargetInfo", "localID": ["0ad7rY7bJN+pTn1qGziJeO"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 421}, "sourceInfo": null, "propertyPath": ["nodeGuide_2"], "target": {"__id__": 196}, "targetInfo": {"__id__": 451}}, {"__type__": "cc.TargetInfo", "localID": ["60CwQ55SdGlZiOnoBN3U6x"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 421}, "sourceInfo": null, "propertyPath": ["nodeGuide_3"], "target": {"__id__": 196}, "targetInfo": {"__id__": 453}}, {"__type__": "cc.TargetInfo", "localID": ["be/DsH/a9OQ55tgO5tnJQC"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 421}, "sourceInfo": null, "propertyPath": ["nodeGuide_13"], "target": {"__id__": 196}, "targetInfo": {"__id__": 455}}, {"__type__": "cc.TargetInfo", "localID": ["4cBKO3jlBPQZRfxI6JTJPQ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 421}, "sourceInfo": null, "propertyPath": ["nodeGuide_14"], "target": {"__id__": 196}, "targetInfo": {"__id__": 457}}, {"__type__": "cc.TargetInfo", "localID": ["17fnGPsplDP5YMZYNtTZZQ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 421}, "sourceInfo": null, "propertyPath": ["nodeGuide_18"], "target": {"__id__": 196}, "targetInfo": {"__id__": 459}}, {"__type__": "cc.TargetInfo", "localID": ["96dC6nOS1F0L7mFFX4Ggtn"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 421}, "sourceInfo": null, "propertyPath": ["scrollView"], "target": {"__id__": 196}, "targetInfo": {"__id__": 461}}, {"__type__": "cc.TargetInfo", "localID": ["8asK4r8rVEJapkwsk78G3g"]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 463}, "shadows": {"__id__": 464}, "_skybox": {"__id__": 465}, "fog": {"__id__": 466}, "octree": {"__id__": 467}, "skin": {"__id__": 468}, "lightProbeInfo": {"__id__": 469}, "postSettings": {"__id__": 470}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]