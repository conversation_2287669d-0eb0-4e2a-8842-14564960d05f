[{"__type__": "cc.AnimationClip", "_name": "middel_coin_new", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 0.8166666666666667, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 6}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 16}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channel": {"__id__": 4}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "spriteFrame"]}, {"__type__": "cc.animation.ComponentPath", "component": "cc.Sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.ObjectCurve", "_times": [0, 0.05, 0.11666666666666667, 0.16666666666666666, 0.21666666666666667, 0.26666666666666666, 0.3333333333333333, 0.38333333333333336, 0.4166666666666667, 0.45, 0.5, 0.5333333333333333, 0.5666666666666667, 0.6166666666666667, 0.65, 0.7, 0.7666666666666667, 0.8], "_values": [{"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@812f6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@814b0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@91e9c", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@35b10", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@91e9c", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@814b0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@812f6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@7bb45", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@812f6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@814b0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@91e9c", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@35b10", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@7bb45", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@35b10", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@91e9c", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@814b0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@812f6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@7bb45", "__expectedType__": "cc.SpriteFrame"}]}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 7}, "proxy": null}, "_channels": [{"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["scale"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [0, 0.23333333432674408, 0.800000011920929], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [0, 0.23333333432674408, 0.800000011920929], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "_times": [0, 0.23333333432674408, 0.800000011920929], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]