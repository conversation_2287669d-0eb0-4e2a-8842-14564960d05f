[{"__type__": "cc.AnimationClip", "_name": "large_coin", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 92, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 0.8152173913043478, "_hash": 1187270062, "_tracks": [{"__id__": 1}, {"__id__": 6}], "_exoticAnimation": null, "_events": [{"frame": 0.8, "func": "onFlipAnimationFinished", "params": []}], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 16}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channel": {"__id__": 4}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "spriteFrame"]}, {"__type__": "cc.animation.ComponentPath", "component": "cc.Sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.ObjectCurve", "_times": [0, 0.043478260869565216, 0.05434782608695652, 0.08695652173913043, 0.13043478260869565, 0.17391304347826086, 0.21739130434782608, 0.2608695652173913, 0.30434782608695654, 0.34782608695652173, 0.391304347826087, 0.43478260869565216, 0.4782608695652174, 0.4891304347826087, 0.5108695652173914, 0.5543478260869565, 0.5652173913043478, 0.5978260869565217, 0.6413043478260869, 0.6521739130434783, 0.6847826086956522, 0.7282608695652174, 0.7391304347826086, 0.7717391304347826, 0.782608695652174, 0.8043478260869565], "_values": [{"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@ca7d6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@9a6ae", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@879e0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@55b8b", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@9a6ae", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@55b8b", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@879e0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@ca7d6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@879e0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@55b8b", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@9a6ae", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@55b8b", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@879e0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@ca7d6", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@879e0", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@55b8b", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@9a6ae", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@70413", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@55b8b", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d6e7f8a9-b0c1-42d3-8e4f-5a6b7c8d9e0f@879e0", "__expectedType__": "cc.SpriteFrame"}]}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 7}, "proxy": null}, "_channels": [{"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["scale"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [0, 0.4021739065647125, 0.804347813129425], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.2000000476837158, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [0, 0.4021739065647125, 0.804347813129425], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.2000000476837158, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "_times": [0, 0.4021739065647125, 0.804347813129425], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]