#///////////////////////////
# Cocos Creator Project
#///////////////////////////
library/
temp/
build/
profiles/
godot-project/
tools/
art/
**/*.md
**/*.md.meta

#//////////////////////////
# web
#//////////////////////////
web/node_modules/
web/dist/

#//////////////////////////
# game-version
#//////////////////////////
game-version/dist/
game-version/node_modules/
game-version/package-lock.json

#//////////////////////////
# VSCode
#//////////////////////////
# .vscode/

#//////////////////////////
# WebStorm
#//////////////////////////
.idea/
native/engine/android/CMakeLists.txt
native/engine/android/Post-service.cmake
native/engine/android/Pre-service.cmake
native/engine/android/app/AndroidManifest.xml
native/engine/android/app/build.gradle
native/engine/android/app/proguard-rules.pro
native/engine/android/app/src/com/cocos/game/AppActivity.java
native/engine/android/build-cfg.json
native/engine/android/build.gradle
native/engine/android/instantapp/AndroidManifest.xml
native/engine/android/instantapp/build.gradle
native/engine/android/instantapp/proguard-rules.pro
native/engine/android/instantapp/src/com/cocos/game/InstantActivity.java
native/engine/android/res/mipmap-hdpi/ic_launcher.png
native/engine/android/res/mipmap-mdpi/ic_launcher.png
native/engine/android/res/mipmap-xhdpi/ic_launcher.png
native/engine/android/res/mipmap-xxhdpi/ic_launcher.png
native/engine/android/res/mipmap-xxxhdpi/ic_launcher.png
native/engine/android/res/values/strings.xml
native/engine/common/CMakeLists.txt
native/engine/common/Classes/Game.cpp
native/engine/common/Classes/Game.h
native/engine/common/cocos-version.json
native/engine/common/localCfg.cmake
native/engine/common/xr.cmake
settings/v2/packages/adsense-h5g-plugin.json
